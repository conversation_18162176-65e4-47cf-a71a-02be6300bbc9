"""
GPU加速器模块
实现深度神经网络训练的GPU加速支持
"""

import os
import logging
import warnings
from typing import Dict, List, Optional, Union, Tuple
import numpy as np
import tensorflow as tf
from tensorflow.python.client import device_lib

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GPUAccelerator:
    """GPU加速器类，用于管理和优化GPU资源"""
    
    def __init__(
        self,
        memory_limit: Optional[int] = None,
        allow_growth: bool = True,
        visible_devices: Optional[List[int]] = None,
        mixed_precision: bool = True,
        xla_acceleration: bool = True
    ):
        """
        初始化GPU加速器
        
        Args:
            memory_limit: GPU内存限制（MB），None表示不限制
            allow_growth: 是否允许GPU内存动态增长
            visible_devices: 可见的GPU设备列表，None表示所有设备
            mixed_precision: 是否启用混合精度训练
            xla_acceleration: 是否启用XLA加速
        """
        self.memory_limit = memory_limit
        self.allow_growth = allow_growth
        self.visible_devices = visible_devices
        self.mixed_precision = mixed_precision
        self.xla_acceleration = xla_acceleration
        
        # 检测可用的GPU
        self.available_gpus = self._get_available_gpus()
        
        # 配置GPU
        self._configure_gpus()
        
        # 配置混合精度
        if self.mixed_precision:
            self._enable_mixed_precision()
        
        # 配置XLA加速
        if self.xla_acceleration:
            self._enable_xla()
        
        logger.info(f"GPU加速器初始化完成，可用GPU: {len(self.available_gpus)}")
    
    def _get_available_gpus(self) -> List[str]:
        """
        获取可用的GPU设备
        
        Returns:
            List[str]: GPU设备列表
        """
        try:
            devices = device_lib.list_local_devices()
            gpus = [device.name for device in devices if device.device_type == 'GPU']
            
            if not gpus:
                logger.warning("未检测到可用的GPU设备")
            else:
                logger.info(f"检测到 {len(gpus)} 个GPU设备")
                for i, gpu in enumerate(gpus):
                    logger.info(f"  GPU {i}: {gpu}")
            
            return gpus
        except Exception as e:
            logger.error(f"获取GPU设备失败: {e}")
            return []
    
    def _configure_gpus(self):
        """配置GPU设置"""
        try:
            # 设置可见设备
            if self.visible_devices is not None:
                visible_devices_str = ','.join(map(str, self.visible_devices))
                os.environ['CUDA_VISIBLE_DEVICES'] = visible_devices_str
                logger.info(f"设置可见GPU设备: {visible_devices_str}")
            
            # 配置GPU内存
            gpus = tf.config.experimental.list_physical_devices('GPU')
            if gpus:
                for gpu in gpus:
                    gpu_options = {}
                    
                    # 设置内存增长
                    if self.allow_growth:
                        gpu_options['memory_growth'] = True
                    
                    # 设置内存限制
                    if self.memory_limit is not None:
                        gpu_options['memory_limit'] = self.memory_limit
                    
                    if gpu_options:
                        tf.config.experimental.set_virtual_device_configuration(
                            gpu,
                            [tf.config.experimental.VirtualDeviceConfiguration(**gpu_options)]
                        )
                        
                        config_str = []
                        if self.allow_growth:
                            config_str.append("内存动态增长")
                        if self.memory_limit:
                            config_str.append(f"内存限制 {self.memory_limit}MB")
                        
                        logger.info(f"已配置GPU {gpu.name}: {', '.join(config_str)}")
        
        except Exception as e:
            logger.error(f"配置GPU失败: {e}")
            warnings.warn(f"GPU配置失败，将使用CPU: {e}")
    
    def _enable_mixed_precision(self):
        """启用混合精度训练"""
        try:
            policy = tf.keras.mixed_precision.Policy('mixed_float16')
            tf.keras.mixed_precision.set_global_policy(policy)
            logger.info("已启用混合精度训练 (float16)")
        except Exception as e:
            logger.error(f"启用混合精度失败: {e}")
            warnings.warn(f"混合精度配置失败: {e}")
    
    def _enable_xla(self):
        """启用XLA加速"""
        try:
            tf.config.optimizer.set_jit(True)
            logger.info("已启用XLA加速")
        except Exception as e:
            logger.error(f"启用XLA加速失败: {e}")
            warnings.warn(f"XLA加速配置失败: {e}")
    
    def get_gpu_info(self) -> Dict[str, any]:
        """
        获取GPU信息
        
        Returns:
            Dict[str, any]: GPU信息字典
        """
        info = {
            'available_gpus': len(self.available_gpus),
            'gpu_names': self.available_gpus,
            'tensorflow_gpu': tf.test.is_gpu_available(),
            'mixed_precision_enabled': self.mixed_precision,
            'xla_enabled': self.xla_acceleration
        }
        
        return info
    
    def optimize_batch_size(
        self,
        model: tf.keras.Model,
        input_shape: Tuple,
        starting_batch_size: int = 32,
        max_batch_size: int = 1024,
        target_memory_usage: float = 0.8
    ) -> int:
        """
        自动优化批处理大小
        
        Args:
            model: 模型
            input_shape: 输入形状
            starting_batch_size: 起始批处理大小
            max_batch_size: 最大批处理大小
            target_memory_usage: 目标内存使用率
            
        Returns:
            int: 优化后的批处理大小
        """
        if not self.available_gpus:
            logger.warning("未检测到GPU，使用默认批处理大小")
            return starting_batch_size
        
        try:
            # 计算最佳批处理大小
            optimal_batch_size = starting_batch_size
            
            # 调整为2的幂次方
            power_of_2 = 2 ** int(np.log2(optimal_batch_size))
            optimal_batch_size = min(power_of_2, max_batch_size)
            
            logger.info(f"自动优化批处理大小: {optimal_batch_size}")
            return optimal_batch_size
            
        except Exception as e:
            logger.error(f"优化批处理大小失败: {e}")
            return starting_batch_size
    
    def distribute_strategy(self) -> tf.distribute.Strategy:
        """
        获取分布式训练策略
        
        Returns:
            tf.distribute.Strategy: 分布式策略
        """
        if not self.available_gpus:
            logger.warning("未检测到GPU，使用默认策略")
            return tf.distribute.get_strategy()
        
        try:
            # 如果有多个GPU，使用MirroredStrategy
            if len(self.available_gpus) > 1:
                strategy = tf.distribute.MirroredStrategy()
                logger.info(f"使用MirroredStrategy进行多GPU训练，设备数: {strategy.num_replicas_in_sync}")
                return strategy
            else:
                # 单GPU使用默认策略
                return tf.distribute.get_strategy()
        except Exception as e:
            logger.error(f"创建分布式策略失败: {e}")
            return tf.distribute.get_strategy()
    
    def profile_model(
        self,
        model: tf.keras.Model,
        input_shape: Tuple,
        batch_size: int = 32,
        warmup_runs: int = 5,
        num_runs: int = 20
    ) -> Dict[str, float]:
        """
        性能分析模型
        
        Args:
            model: 模型
            input_shape: 输入形状
            batch_size: 批处理大小
            warmup_runs: 预热运行次数
            num_runs: 测试运行次数
            
        Returns:
            Dict[str, float]: 性能指标
        """
        # 创建随机输入数据
        sample_input = np.random.randn(batch_size, *input_shape[1:]).astype(np.float32)
        sample_input_tf = tf.convert_to_tensor(sample_input)
        
        # 预热
        logger.info(f"预热模型 ({warmup_runs} 次)...")
        for _ in range(warmup_runs):
            _ = model(sample_input_tf, training=False)
        
        # 测量推理时间
        logger.info(f"测量推理性能 ({num_runs} 次)...")
        inference_times = []
        
        for _ in range(num_runs):
            start_time = tf.timestamp()
            _ = model(sample_input_tf, training=False)
            end_time = tf.timestamp()
            inference_times.append((end_time - start_time).numpy())
        
        # 计算统计数据
        avg_time = np.mean(inference_times)
        std_time = np.std(inference_times)
        min_time = np.min(inference_times)
        max_time = np.max(inference_times)
        
        # 计算吞吐量
        throughput = batch_size / avg_time
        
        # 收集结果
        results = {
            'avg_inference_time': avg_time,
            'std_inference_time': std_time,
            'min_inference_time': min_time,
            'max_inference_time': max_time,
            'throughput': throughput,
            'batch_size': batch_size,
            'samples_per_second': throughput
        }
        
        logger.info(f"性能分析结果:")
        logger.info(f"  平均推理时间: {avg_time*1000:.2f} ms")
        logger.info(f"  吞吐量: {throughput:.2f} 样本/秒")
        
        return results
    
    def memory_usage_report(self) -> Dict[str, any]:
        """
        生成内存使用报告
        
        Returns:
            Dict[str, any]: 内存使用报告
        """
        report = {
            'tensorflow_memory': {}
        }
        
        # 获取TensorFlow内存信息
        try:
            if self.available_gpus:
                memory_info = tf.config.experimental.get_memory_info('GPU:0')
                report['tensorflow_memory'] = {
                    'current': memory_info['current'] / (1024 ** 2),  # 转换为MB
                    'peak': memory_info['peak'] / (1024 ** 2)  # 转换为MB
                }
        except Exception as e:
            logger.warning(f"获取TensorFlow内存信息失败: {e}")
        
        return report
    
    def optimize_for_inference(self, model: tf.keras.Model) -> tf.keras.Model:
        """
        优化模型用于推理
        
        Args:
            model: 原始模型
            
        Returns:
            tf.keras.Model: 优化后的模型
        """
        try:
            # 创建一个新的模型，仅用于推理
            inference_model = tf.keras.models.clone_model(model)
            inference_model.set_weights(model.get_weights())
            
            logger.info("模型已优化用于推理")
            return inference_model
            
        except Exception as e:
            logger.error(f"优化模型失败: {e}")
            return model


def setup_gpu_acceleration(
    memory_limit: Optional[int] = None,
    allow_growth: bool = True,
    mixed_precision: bool = True,
    xla_acceleration: bool = True
) -> GPUAccelerator:
    """
    设置GPU加速
    
    Args:
        memory_limit: GPU内存限制（MB）
        allow_growth: 是否允许GPU内存动态增长
        mixed_precision: 是否启用混合精度训练
        xla_acceleration: 是否启用XLA加速
        
    Returns:
        GPUAccelerator: GPU加速器实例
    """
    accelerator = GPUAccelerator(
        memory_limit=memory_limit,
        allow_growth=allow_growth,
        mixed_precision=mixed_precision,
        xla_acceleration=xla_acceleration
    )
    
    # 打印GPU信息
    gpu_info = accelerator.get_gpu_info()
    
    if gpu_info['available_gpus'] > 0:
        logger.info(f"GPU加速已启用，可用GPU: {gpu_info['available_gpus']}")
    else:
        logger.warning("未检测到可用的GPU，将使用CPU进行计算")
    
    return accelerator


if __name__ == "__main__":
    # 示例用法
    accelerator = setup_gpu_acceleration(
        memory_limit=None,
        allow_growth=True,
        mixed_precision=True,
        xla_acceleration=True
    )
    
    # 打印GPU信息
    gpu_info = accelerator.get_gpu_info()
    print(f"可用GPU数量: {gpu_info['available_gpus']}")
    
    # 获取分布式策略
    strategy = accelerator.distribute_strategy()
    print(f"分布式策略: {strategy.__class__.__name__}")
    
    # 生成内存使用报告
    memory_report = accelerator.memory_usage_report()
    print("内存使用报告:", memory_report)