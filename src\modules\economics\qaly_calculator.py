"""
Quality-Adjusted Life Year (QALY) Calculator for health outcomes assessment.
"""
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Union
from enum import Enum
import numpy as np
from src.core.individual import Individual
from src.core.population import Population


class UtilityScale(Enum):
    """Standard utility scales for measuring health-related quality of life."""
    EQ5D = "eq5d"           # EuroQol-5D
    SF6D = "sf6d"           # SF-6D
    HUI3 = "hui3"           # Health Utilities Index Mark 3
    CUSTOM = "custom"       # Custom scale


@dataclass
class HealthState:
    """Represents a health state with associated utility value and duration."""
    state_name: str
    utility_value: float
    duration_years: float
    age_at_start: int
    confidence_interval: Optional[Tuple[float, float]] = None


@dataclass
class QALYResult:
    """Result of QALY calculation for an individual."""
    total_qalys: float
    undiscounted_qalys: float
    discounted_qalys: float
    qaly_by_age_group: Dict[str, float]
    confidence_interval: Optional[Tuple[float, float]] = None
    calculation_details: Optional[Dict] = None


class QALYCalculator:
    """Calculator for Quality-Adjusted Life Years (QALYs)."""
    
    def __init__(self, discount_rate: float = 0.03):
        """
        Initialize QALY calculator with discount rate.
        
        Args:
            discount_rate: Annual discount rate for future health benefits (default: 0.03 or 3%)
        """
        self.discount_rate = discount_rate
        self.utility_values = self._load_utility_values()
        self.age_adjustments = self._load_age_adjustments()
    
    def _load_utility_values(self) -> Dict:
        """
        Load utility values for different health states.
        
        Returns:
            Dictionary of utility values by health state
        """
        # Default utility values based on common health state utilities
        return {
            'normal': 1.0,
            'low_risk_adenoma': 0.98,
            'high_risk_adenoma': 0.96,
            'preclinical_cancer': 1.0,
            'clinical_cancer_stage_i': 0.85,
            'clinical_cancer_stage_ii': 0.78,
            'clinical_cancer_stage_iii': 0.70,
            'clinical_cancer_stage_iv': 0.55
        }
    
    def _load_age_adjustments(self) -> Dict:
        """
        Load age-specific adjustments to utility values.
        
        Returns:
            Dictionary of age adjustment factors
        """
        # Default age adjustments (utility multipliers by age group)
        return {
            '18_29': 0.95,
            '30_39': 0.93,
            '40_49': 0.91,
            '50_59': 0.89,
            '60_69': 0.86,
            '70_79': 0.82,
            '80_plus': 0.78
        }
    
    def _get_age_group(self, age: Union[int, float]) -> str:
        """
        Determine age group for an individual.
        
        Args:
            age: Age of individual
            
        Returns:
            Age group string
        """
        age = int(age)
        if 18 <= age <= 29:
            return '18_29'
        elif 30 <= age <= 39:
            return '30_39'
        elif 40 <= age <= 49:
            return '40_49'
        elif 50 <= age <= 59:
            return '50_59'
        elif 60 <= age <= 69:
            return '60_69'
        elif 70 <= age <= 79:
            return '70_79'
        else:
            return '80_plus'
    
    def _get_age_adjusted_utility(self, base_utility: float, age: Union[int, float]) -> float:
        """
        Get age-adjusted utility value.
        
        Args:
            base_utility: Base utility value for health state
            age: Age of individual
            
        Returns:
            Age-adjusted utility value
        """
        age_group = self._get_age_group(age)
        age_adjustment = self.age_adjustments.get(age_group, 1.0)
        return base_utility * age_adjustment
    
    def _extract_health_states(self, individual: Individual) -> List[HealthState]:
        """
        Extract health states from an individual's history.
        
        Args:
            individual: Individual to extract health states from
            
        Returns:
            List of HealthState objects
        """
        # This is a simplified implementation - in practice, this would extract
        # health states from the individual's history
        health_states = []
        
        # For demonstration, we'll create some sample health states
        # In a real implementation, this would be based on the individual's actual history
        current_age = individual.age if hasattr(individual, 'age') else 50
        baseline_age = getattr(individual, 'baseline_age', current_age)
        
        # Sample health states - would be derived from individual's history in practice
        if hasattr(individual, 'disease_state'):
            disease_state = individual.disease_state
            utility_value = self.utility_values.get(disease_state, 1.0)
        else:
            utility_value = self.utility_values.get('normal', 1.0)
            
        # Create a health state representing the current state
        health_states.append(HealthState(
            state_name=getattr(individual, 'disease_state', 'normal'),
            utility_value=utility_value,
            duration_years=max(1.0, current_age - baseline_age),
            age_at_start=baseline_age
        ))
        
        return health_states
    
    def _calculate_qaly_distribution(self, results: List[QALYResult]) -> Dict:
        """
        Calculate distribution statistics for QALY results.
        
        Args:
            results: List of QALYResult objects
            
        Returns:
            Dictionary with distribution statistics
        """
        qaly_values = [result.total_qalys for result in results]
        if not qaly_values:
            return {}
            
        return {
            'mean': np.mean(qaly_values),
            'std': np.std(qaly_values),
            'min': np.min(qaly_values),
            'max': np.max(qaly_values),
            'median': np.median(qaly_values)
        }
    
    def calculate_individual_qalys(
        self, 
        individual: Individual, 
        health_states: Optional[List[HealthState]] = None
    ) -> QALYResult:
        """
        Calculate QALYs for an individual.
        
        Args:
            individual: Individual for QALY calculation
            health_states: List of health states (optional, will be extracted if not provided)
            
        Returns:
            QALYResult with calculation results
        """
        if health_states is None:
            health_states = self._extract_health_states(individual)
        
        total_qalys = 0.0
        undiscounted_qalys = 0.0
        qaly_by_age = {}
        
        # Get baseline age from individual or use default
        baseline_age = getattr(individual, 'baseline_age', 
                              getattr(individual, 'age', 0) if hasattr(individual, 'age') else 0)
        current_age = getattr(individual, 'age', baseline_age)
        
        for health_state in health_states:
            # Get age-adjusted utility value
            adjusted_utility = self._get_age_adjusted_utility(
                health_state.utility_value, 
                health_state.age_at_start
            )
            
            # Calculate QALYs for this health state
            state_qalys = adjusted_utility * health_state.duration_years
            undiscounted_qalys += state_qalys
            
            # Calculate discounted QALYs
            years_from_baseline = health_state.age_at_start - baseline_age if baseline_age > 0 else 0
            discount_factor = (1 + self.discount_rate) ** (-years_from_baseline)
            discounted_state_qalys = state_qalys * discount_factor
            total_qalys += discounted_state_qalys
            
            # Record by age group
            age_group = self._get_age_group(health_state.age_at_start)
            if age_group in qaly_by_age:
                qaly_by_age[age_group] += discounted_state_qalys
            else:
                qaly_by_age[age_group] = discounted_state_qalys
        
        return QALYResult(
            total_qalys=total_qalys,
            undiscounted_qalys=undiscounted_qalys,
            discounted_qalys=total_qalys,
            qaly_by_age_group=qaly_by_age
        )
    
    def calculate_population_qalys(
        self, 
        population: Population, 
        scenario_name: str
    ) -> Dict:
        """
        Calculate QALYs for an entire population.
        
        Args:
            population: Population for QALY calculation
            scenario_name: Name of the scenario being evaluated
            
        Returns:
            Dictionary with population-level QALY results
        """
        individual_results = []
        for individual in population.individuals:
            try:
                health_states = self._extract_health_states(individual)
                qaly_result = self.calculate_individual_qalys(individual, health_states)
                individual_results.append(qaly_result)
            except Exception as e:
                # Handle cases where individual data may be incomplete
                continue
        
        # Calculate summary statistics
        total_qalys = sum(result.total_qalys for result in individual_results)
        mean_qalys = total_qalys / len(individual_results) if individual_results else 0.0
        
        return {
            'scenario_name': scenario_name,
            'total_population_qalys': total_qalys,
            'mean_individual_qalys': mean_qalys,
            'individual_results': individual_results,
            'qaly_distribution': self._calculate_qaly_distribution(individual_results)
        }
