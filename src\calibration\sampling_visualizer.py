"""
抽样可视化模块
实现抽样结果的多维可视化功能
"""

from typing import Dict, List, Optional, Any, Tuple
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from matplotlib.figure import Figure
from matplotlib.axes import Axes
import warnings
from pathlib import Path

from .parameter_sampler import SamplingResult
from .sampling_analytics import SamplingAnalyzer


class SamplingVisualizer:
    """抽样结果可视化器"""
    
    def __init__(self, style: str = 'seaborn-v0_8', figsize: Tuple[int, int] = (12, 8)):
        """
        初始化可视化器
        
        Args:
            style: matplotlib样式
            figsize: 默认图形大小
        """
        self.style = style
        self.figsize = figsize
        
        # 设置中文字体和样式
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置颜色主题
        self.colors = plt.cm.Set3(np.linspace(0, 1, 12))
        
        # 忽略matplotlib警告
        warnings.filterwarnings('ignore', category=UserWarning)
    
    def visualize_sampling_results(self, result: SamplingResult, 
                                 save_path: Optional[str] = None) -> Dict[str, Figure]:
        """
        生成抽样结果的完整可视化
        
        Args:
            result: 抽样结果
            save_path: 保存路径（可选）
            
        Returns:
            Dict[str, Figure]: 可视化图形字典
        """
        visualizations = {}
        
        # 1. 参数分布直方图
        visualizations['parameter_distributions'] = self.plot_parameter_distributions(result)
        
        # 2. 参数相关性热图
        visualizations['correlation_heatmap'] = self.plot_correlation_heatmap(result)
        
        # 3. 散点图矩阵
        visualizations['scatter_matrix'] = self.plot_scatter_matrix(result)
        
        # 4. 空间填充可视化
        visualizations['space_filling'] = self.plot_space_filling_analysis(result)
        
        # 5. 质量指标雷达图
        visualizations['quality_radar'] = self.plot_quality_radar(result)
        
        # 6. 抽样过程分析
        visualizations['sampling_process'] = self.plot_sampling_process_analysis(result)
        
        # 保存图形
        if save_path:
            self.save_visualizations(visualizations, save_path)
        
        return visualizations
    
    def plot_parameter_distributions(self, result: SamplingResult) -> Figure:
        """
        绘制参数分布直方图
        
        Args:
            result: 抽样结果
            
        Returns:
            Figure: 参数分布图
        """
        n_params = len(result.parameter_names)
        n_cols = min(3, n_params)
        n_rows = (n_params + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(5 * n_cols, 4 * n_rows))
        if n_params == 1:
            axes = [axes]
        elif n_rows == 1:
            axes = axes.reshape(1, -1)
        
        axes_flat = axes.flatten() if n_params > 1 else axes
        
        for i, param_name in enumerate(result.parameter_names):
            ax = axes_flat[i]
            param_samples = result.samples[:, i]
            
            # 绘制直方图
            n_bins = min(50, int(np.sqrt(len(param_samples))))
            ax.hist(param_samples, bins=n_bins, alpha=0.7, color=self.colors[i % len(self.colors)],
                   density=True, edgecolor='black', linewidth=0.5)
            
            # 添加统计信息
            mean_val = np.mean(param_samples)
            std_val = np.std(param_samples)
            ax.axvline(mean_val, color='red', linestyle='--', linewidth=2, label=f'均值: {mean_val:.3f}')
            ax.axvline(mean_val - std_val, color='orange', linestyle=':', alpha=0.7, label=f'±1σ')
            ax.axvline(mean_val + std_val, color='orange', linestyle=':', alpha=0.7)
            
            ax.set_title(f'{param_name} 分布', fontsize=12, fontweight='bold')
            ax.set_xlabel('参数值', fontsize=10)
            ax.set_ylabel('密度', fontsize=10)
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
        
        # 隐藏多余的子图
        for i in range(n_params, len(axes_flat)):
            axes_flat[i].set_visible(False)
        
        plt.suptitle(f'参数分布分析 (样本数: {result.config.n_samples:,})', 
                    fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        return fig
    
    def plot_correlation_heatmap(self, result: SamplingResult) -> Figure:
        """
        绘制参数相关性热图
        
        Args:
            result: 抽样结果
            
        Returns:
            Figure: 相关性热图
        """
        fig, ax = plt.subplots(figsize=self.figsize)
        
        # 计算相关性矩阵
        correlation_matrix = np.corrcoef(result.samples.T)
        
        # 创建掩码以隐藏上三角
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        
        # 绘制热图
        sns.heatmap(correlation_matrix, 
                   mask=mask,
                   xticklabels=result.parameter_names,
                   yticklabels=result.parameter_names,
                   annot=True, 
                   cmap='RdBu_r', 
                   center=0, 
                   square=True,
                   fmt='.3f',
                   cbar_kws={'label': '相关系数'},
                   ax=ax)
        
        ax.set_title('参数相关性矩阵', fontsize=16, fontweight='bold', pad=20)
        
        # 添加统计信息
        off_diagonal = correlation_matrix[np.triu_indices_from(correlation_matrix, k=1)]
        max_corr = np.max(np.abs(off_diagonal))
        mean_corr = np.mean(np.abs(off_diagonal))
        
        ax.text(0.02, 0.98, f'最大相关性: {max_corr:.3f}\n平均相关性: {mean_corr:.3f}',
               transform=ax.transAxes, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        return fig
    
    def plot_scatter_matrix(self, result: SamplingResult, max_params: int = 6) -> Figure:
        """
        绘制散点图矩阵
        
        Args:
            result: 抽样结果
            max_params: 最大显示参数数量
            
        Returns:
            Figure: 散点图矩阵
        """
        # 限制参数数量以避免图形过于复杂
        n_params = min(len(result.parameter_names), max_params)
        selected_params = result.parameter_names[:n_params]
        selected_samples = result.samples[:, :n_params]
        
        fig, axes = plt.subplots(n_params, n_params, figsize=(3 * n_params, 3 * n_params))
        
        for i in range(n_params):
            for j in range(n_params):
                ax = axes[i, j]
                
                if i == j:
                    # 对角线：直方图
                    ax.hist(selected_samples[:, i], bins=30, alpha=0.7, 
                           color=self.colors[i % len(self.colors)], density=True)
                    ax.set_ylabel('密度', fontsize=8)
                else:
                    # 非对角线：散点图
                    ax.scatter(selected_samples[:, j], selected_samples[:, i], 
                             alpha=0.5, s=1, color=self.colors[i % len(self.colors)])
                
                # 设置标签
                if i == n_params - 1:
                    ax.set_xlabel(selected_params[j], fontsize=10)
                if j == 0:
                    ax.set_ylabel(selected_params[i], fontsize=10)
                
                # 设置刻度标签大小
                ax.tick_params(labelsize=8)
                ax.grid(True, alpha=0.3)
        
        plt.suptitle(f'参数散点图矩阵 (前{n_params}个参数)', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        return fig
    
    def plot_space_filling_analysis(self, result: SamplingResult) -> Figure:
        """
        绘制空间填充分析图
        
        Args:
            result: 抽样结果
            
        Returns:
            Figure: 空间填充分析图
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 距离分布直方图
        from scipy.spatial.distance import pdist
        distances = pdist(result.samples)
        
        ax1.hist(distances, bins=50, alpha=0.7, color='skyblue', density=True)
        ax1.axvline(np.mean(distances), color='red', linestyle='--', 
                   label=f'平均距离: {np.mean(distances):.4f}')
        ax1.axvline(np.min(distances), color='orange', linestyle='--', 
                   label=f'最小距离: {np.min(distances):.4f}')
        ax1.set_xlabel('样本点间距离')
        ax1.set_ylabel('密度')
        ax1.set_title('样本点距离分布')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 最近邻距离分析
        from scipy.spatial.distance import squareform
        distance_matrix = squareform(distances)
        np.fill_diagonal(distance_matrix, np.inf)
        nearest_distances = np.min(distance_matrix, axis=1)
        
        ax2.hist(nearest_distances, bins=30, alpha=0.7, color='lightgreen', density=True)
        ax2.axvline(np.mean(nearest_distances), color='red', linestyle='--',
                   label=f'平均最近邻距离: {np.mean(nearest_distances):.4f}')
        ax2.set_xlabel('最近邻距离')
        ax2.set_ylabel('密度')
        ax2.set_title('最近邻距离分布')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 二维投影（前两个参数）
        if result.samples.shape[1] >= 2:
            ax3.scatter(result.samples[:, 0], result.samples[:, 1], 
                       alpha=0.6, s=20, c=range(len(result.samples)), cmap='viridis')
            ax3.set_xlabel(result.parameter_names[0])
            ax3.set_ylabel(result.parameter_names[1])
            ax3.set_title('二维空间填充可视化')
            ax3.grid(True, alpha=0.3)
        
        # 4. 质量指标时间序列（模拟）
        sample_sizes = np.logspace(2, np.log10(result.config.n_samples), 20).astype(int)
        quality_evolution = []
        
        for n in sample_sizes:
            if n <= len(result.samples):
                subset = result.samples[:n]
                subset_distances = pdist(subset)
                min_dist = np.min(subset_distances) if len(subset_distances) > 0 else 0
                quality_evolution.append(min_dist)
            else:
                quality_evolution.append(quality_evolution[-1] if quality_evolution else 0)
        
        ax4.plot(sample_sizes, quality_evolution, 'b-', linewidth=2, marker='o', markersize=4)
        ax4.set_xlabel('样本数量')
        ax4.set_ylabel('最小距离')
        ax4.set_title('抽样质量随样本数量变化')
        ax4.set_xscale('log')
        ax4.grid(True, alpha=0.3)
        
        plt.suptitle('空间填充性质分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        return fig
    
    def plot_quality_radar(self, result: SamplingResult) -> Figure:
        """
        绘制质量指标雷达图
        
        Args:
            result: 抽样结果
            
        Returns:
            Figure: 质量雷达图
        """
        # 使用分析器计算质量指标
        analyzer = SamplingAnalyzer()
        analysis = analyzer.analyze_sampling_quality(result)
        
        # 提取质量分数
        quality_scores = analysis['overall_quality']
        
        # 雷达图数据
        categories = ['距离评分', '相关性评分', '覆盖度评分', '空间填充', '均匀性', '整体质量']
        values = [
            quality_scores['distance_score'],
            quality_scores['correlation_score'], 
            quality_scores['coverage_score'],
            min(result.quality_metrics['mean_distance'] / 0.1, 1.0),  # 标准化空间填充
            result.quality_metrics['min_coverage'],  # 均匀性
            quality_scores['overall_score']
        ]
        
        # 创建雷达图
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        # 计算角度
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        values += values[:1]  # 闭合图形
        angles += angles[:1]
        
        # 绘制雷达图
        ax.plot(angles, values, 'o-', linewidth=2, color='blue', alpha=0.7)
        ax.fill(angles, values, alpha=0.25, color='blue')
        
        # 设置标签
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories, fontsize=12)
        
        # 设置径向刻度
        ax.set_ylim(0, 1)
        ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
        ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'], fontsize=10)
        ax.grid(True)
        
        # 添加质量等级
        grade = quality_scores['quality_grade']
        overall_score = quality_scores['overall_score']
        
        plt.title(f'抽样质量雷达图\n总体评分: {overall_score:.3f} ({grade})', 
                 fontsize=16, fontweight='bold', pad=30)
        
        return fig
    
    def plot_sampling_process_analysis(self, result: SamplingResult) -> Figure:
        """
        绘制抽样过程分析图
        
        Args:
            result: 抽样结果
            
        Returns:
            Figure: 抽样过程分析图
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 样本生成顺序分析（前100个样本）
        n_show = min(100, len(result.samples))
        sample_indices = range(n_show)
        
        if result.samples.shape[1] >= 2:
            scatter = ax1.scatter(result.samples[:n_show, 0], result.samples[:n_show, 1], 
                                c=sample_indices, cmap='viridis', s=30, alpha=0.7)
            ax1.set_xlabel(result.parameter_names[0])
            ax1.set_ylabel(result.parameter_names[1])
            ax1.set_title(f'样本生成顺序 (前{n_show}个)')
            plt.colorbar(scatter, ax=ax1, label='生成顺序')
        
        # 2. 参数范围覆盖分析
        coverage_data = []
        for i, param_name in enumerate(result.parameter_names):
            param_samples = result.samples[:, i]
            param_range = np.max(param_samples) - np.min(param_samples)
            coverage_data.append(param_range)
        
        ax2.bar(range(len(result.parameter_names)), coverage_data, 
               color=self.colors[:len(result.parameter_names)])
        ax2.set_xlabel('参数索引')
        ax2.set_ylabel('覆盖范围')
        ax2.set_title('参数覆盖范围分析')
        ax2.set_xticks(range(len(result.parameter_names)))
        ax2.set_xticklabels([f'P{i+1}' for i in range(len(result.parameter_names))], rotation=45)
        ax2.grid(True, alpha=0.3)
        
        # 3. 抽样效率分析
        generation_rate = result.config.n_samples / result.generation_time
        memory_per_sample = (result.samples.nbytes / (1024 * 1024)) / result.config.n_samples
        
        efficiency_metrics = ['生成速率\n(样本/秒)', '内存效率\n(MB/千样本)', '质量评分\n(0-1)']
        efficiency_values = [
            generation_rate,
            memory_per_sample * 1000,  # 转换为每千样本的MB
            result.quality_metrics.get('min_coverage', 0.5)
        ]
        
        bars = ax3.bar(efficiency_metrics, efficiency_values, 
                      color=['lightblue', 'lightgreen', 'lightcoral'])
        ax3.set_title('抽样效率指标')
        ax3.set_ylabel('指标值')
        
        # 在柱状图上添加数值标签
        for bar, value in zip(bars, efficiency_values):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{value:.2f}', ha='center', va='bottom')
        
        # 4. 质量指标对比
        quality_metrics = result.quality_metrics
        metric_names = ['最小距离', '平均距离', '最大相关性', '平均相关性', '最小覆盖度', '平均覆盖度']
        metric_values = [
            quality_metrics.get('min_distance', 0),
            quality_metrics.get('mean_distance', 0),
            quality_metrics.get('max_correlation', 0),
            quality_metrics.get('mean_correlation', 0),
            quality_metrics.get('min_coverage', 0),
            quality_metrics.get('mean_coverage', 0)
        ]
        
        bars = ax4.barh(metric_names, metric_values, color=self.colors[:len(metric_names)])
        ax4.set_xlabel('指标值')
        ax4.set_title('质量指标详情')
        ax4.grid(True, alpha=0.3, axis='x')
        
        # 在条形图上添加数值标签
        for bar, value in zip(bars, metric_values):
            width = bar.get_width()
            ax4.text(width + width*0.01, bar.get_y() + bar.get_height()/2.,
                    f'{value:.4f}', ha='left', va='center')
        
        plt.suptitle('抽样过程综合分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        return fig
    
    def save_visualizations(self, visualizations: Dict[str, Figure], 
                          save_path: str, format: str = 'png', dpi: int = 300):
        """
        保存可视化图形
        
        Args:
            visualizations: 可视化图形字典
            save_path: 保存路径
            format: 图片格式
            dpi: 图片分辨率
        """
        save_dir = Path(save_path)
        save_dir.mkdir(parents=True, exist_ok=True)
        
        for name, fig in visualizations.items():
            file_path = save_dir / f"{name}.{format}"
            fig.savefig(file_path, format=format, dpi=dpi, bbox_inches='tight')
            print(f"已保存: {file_path}")
    
    def create_summary_dashboard(self, result: SamplingResult) -> Figure:
        """
        创建抽样结果总结仪表板
        
        Args:
            result: 抽样结果
            
        Returns:
            Figure: 总结仪表板
        """
        fig = plt.figure(figsize=(20, 12))
        
        # 创建网格布局
        gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)
        
        # 1. 基本信息面板
        ax_info = fig.add_subplot(gs[0, 0])
        ax_info.axis('off')
        info_text = f"""
抽样配置信息
━━━━━━━━━━━━━━━━
样本数量: {result.config.n_samples:,}
参数维度: {len(result.parameter_names)}
抽样方法: {result.config.sampling_method.upper()}
随机种子: {result.config.random_seed}
生成时间: {result.generation_time:.2f}秒
生成速率: {result.config.n_samples/result.generation_time:.0f} 样本/秒
        """
        ax_info.text(0.05, 0.95, info_text, transform=ax_info.transAxes, 
                    fontsize=10, verticalalignment='top', fontfamily='monospace')
        
        # 2. 质量指标面板
        ax_quality = fig.add_subplot(gs[0, 1])
        ax_quality.axis('off')
        
        analyzer = SamplingAnalyzer()
        analysis = analyzer.analyze_sampling_quality(result)
        quality = analysis['overall_quality']
        
        quality_text = f"""
质量评估结果
━━━━━━━━━━━━━━━━
总体评分: {quality['overall_score']:.3f}
质量等级: {quality['quality_grade']}
距离评分: {quality['distance_score']:.3f}
相关性评分: {quality['correlation_score']:.3f}
覆盖度评分: {quality['coverage_score']:.3f}
        """
        ax_quality.text(0.05, 0.95, quality_text, transform=ax_quality.transAxes,
                       fontsize=10, verticalalignment='top', fontfamily='monospace')
        
        # 3. 参数分布（前4个参数）
        n_params_show = min(4, len(result.parameter_names))
        for i in range(n_params_show):
            ax = fig.add_subplot(gs[0, 2 + i//2], gs[1, 2 + i//2])
            param_samples = result.samples[:, i]
            ax.hist(param_samples, bins=30, alpha=0.7, color=self.colors[i])
            ax.set_title(f'{result.parameter_names[i]}', fontsize=10)
            ax.tick_params(labelsize=8)
        
        # 4. 相关性热图
        ax_corr = fig.add_subplot(gs[1, 0:2])
        correlation_matrix = np.corrcoef(result.samples.T)
        im = ax_corr.imshow(correlation_matrix, cmap='RdBu_r', vmin=-1, vmax=1)
        ax_corr.set_title('参数相关性矩阵', fontsize=12)
        ax_corr.set_xticks(range(len(result.parameter_names)))
        ax_corr.set_yticks(range(len(result.parameter_names)))
        ax_corr.set_xticklabels([f'P{i+1}' for i in range(len(result.parameter_names))], 
                               rotation=45, fontsize=8)
        ax_corr.set_yticklabels([f'P{i+1}' for i in range(len(result.parameter_names))], 
                               fontsize=8)
        plt.colorbar(im, ax=ax_corr, shrink=0.8)
        
        # 5. 空间填充可视化（2D投影）
        if len(result.parameter_names) >= 2:
            ax_space = fig.add_subplot(gs[2, 0:2])
            ax_space.scatter(result.samples[:, 0], result.samples[:, 1], 
                           alpha=0.6, s=10, c='blue')
            ax_space.set_xlabel(result.parameter_names[0], fontsize=10)
            ax_space.set_ylabel(result.parameter_names[1], fontsize=10)
            ax_space.set_title('空间填充可视化 (前两个参数)', fontsize=12)
            ax_space.grid(True, alpha=0.3)
        
        # 6. 质量指标条形图
        ax_metrics = fig.add_subplot(gs[2, 2:])
        metrics = result.quality_metrics
        metric_names = ['最小距离', '平均距离', '最大相关性', '最小覆盖度']
        metric_values = [
            metrics.get('min_distance', 0),
            metrics.get('mean_distance', 0),
            metrics.get('max_correlation', 0),
            metrics.get('min_coverage', 0)
        ]
        
        bars = ax_metrics.bar(metric_names, metric_values, color=self.colors[:4])
        ax_metrics.set_title('关键质量指标', fontsize=12)
        ax_metrics.set_ylabel('指标值', fontsize=10)
        ax_metrics.tick_params(axis='x', rotation=45, labelsize=8)
        
        # 添加数值标签
        for bar, value in zip(bars, metric_values):
            height = bar.get_height()
            ax_metrics.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                          f'{value:.4f}', ha='center', va='bottom', fontsize=8)
        
        plt.suptitle(f'参数抽样结果总结仪表板 - {result.config.sampling_method.upper()}', 
                    fontsize=18, fontweight='bold')
        
        return fig