"""
训练管理器模块
整合GPU加速的深度神经网络训练流程
"""

import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow import keras
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
import warnings
import json
import os
from datetime import datetime
import time

# 导入自定义模块
from .neural_network_gpu import GPUCalibrationDNN, NetworkConfig, GPUNetworkArchitectureFactory
from .gpu_accelerator import GPUAccelerator


class TrainingManager:
    """训练管理器类，负责管理整个训练流程"""
    
    def __init__(
        self,
        config: NetworkConfig,
        random_state: int = 42,
        log_level: int = logging.INFO
    ):
        """
        初始化训练管理器
        
        Args:
            config: 网络配置
            random_state: 随机种子
            log_level: 日志级别
        """
        self.config = config
        self.random_state = random_state
        
        # 设置日志
        logging.basicConfig(level=log_level)
        self.logger = logging.getLogger(__name__)
        
        # 设置随机种子
        np.random.seed(random_state)
        tf.random.set_seed(random_state)
        
        # 初始化组件
        self.model = None
        self.scaler_X = StandardScaler()
        self.scaler_y = StandardScaler()
        self.training_history = None
        self.evaluation_results = {}
        
        # 训练数据
        self.X_train = None
        self.X_val = None
        self.X_test = None
        self.y_train = None
        self.y_val = None
        self.y_test = None
        
        self.logger.info("训练管理器初始化完成")
    
    def prepare_data(
        self,
        X: np.ndarray,
        y: np.ndarray,
        test_size: float = 0.2,
        val_size: float = 0.2,
        normalize: bool = True
    ) -> Dict[str, np.ndarray]:
        """
        准备训练数据
        
        Args:
            X: 输入特征
            y: 目标变量
            test_size: 测试集比例
            val_size: 验证集比例
            normalize: 是否标准化
            
        Returns:
            Dict[str, np.ndarray]: 分割后的数据集
        """
        self.logger.info("开始准备训练数据...")
        
        # 首先分割出测试集
        X_temp, self.X_test, y_temp, self.y_test = train_test_split(
            X, y, test_size=test_size, random_state=self.random_state
        )
        
        # 从剩余数据中分割训练集和验证集
        val_size_adjusted = val_size / (1 - test_size)
        self.X_train, self.X_val, self.y_train, self.y_val = train_test_split(
            X_temp, y_temp, test_size=val_size_adjusted, random_state=self.random_state
        )
        
        # 数据标准化
        if normalize:
            self.X_train = self.scaler_X.fit_transform(self.X_train)
            self.X_val = self.scaler_X.transform(self.X_val)
            self.X_test = self.scaler_X.transform(self.X_test)
            
            self.y_train = self.scaler_y.fit_transform(self.y_train.reshape(-1, 1)).flatten()
            self.y_val = self.scaler_y.transform(self.y_val.reshape(-1, 1)).flatten()
            self.y_test = self.scaler_y.transform(self.y_test.reshape(-1, 1)).flatten()
            
            self.logger.info("数据标准化完成")
        
        # 数据集信息
        data_info = {
            'train_samples': len(self.X_train),
            'val_samples': len(self.X_val),
            'test_samples': len(self.X_test),
            'input_features': self.X_train.shape[1],
            'output_targets': 1 if len(self.y_train.shape) == 1 else self.y_train.shape[1]
        }
        
        self.logger.info(f"数据准备完成: {data_info}")
        
        return {
            'X_train': self.X_train,
            'X_val': self.X_val,
            'X_test': self.X_test,
            'y_train': self.y_train,
            'y_val': self.y_val,
            'y_test': self.y_test,
            'data_info': data_info
        }
    
    def create_model(self) -> GPUCalibrationDNN:
        """
        创建GPU加速的神经网络模型
        
        Returns:
            GPUCalibrationDNN: 创建的模型
        """
        self.logger.info("创建GPU加速神经网络模型...")
        
        # 创建模型
        self.model = GPUCalibrationDNN(self.config)
        
        # 构建模型
        keras_model = self.model.build_model()
        
        # 编译模型
        self.model.compile_model()
        
        # 打印模型信息
        self.logger.info("模型创建完成")
        self.logger.info(f"模型参数数量: {keras_model.count_params():,}")
        
        # 打印GPU信息
        gpu_info = self.model.get_gpu_info()
        if gpu_info.get('gpu_enabled', True):
            self.logger.info(f"GPU加速已启用，可用GPU: {gpu_info.get('available_gpus', 0)}")
        else:
            self.logger.warning("GPU加速未启用，将使用CPU训练")
        
        return self.model
    
    def train_model(
        self,
        epochs: int = 100,
        batch_size: Optional[int] = None,
        early_stopping_patience: int = 20,
        lr_reduction_patience: int = 10,
        save_best_model: bool = True,
        model_save_path: Optional[str] = None,
        verbose: int = 1
    ) -> keras.callbacks.History:
        """
        训练模型
        
        Args:
            epochs: 训练轮数
            batch_size: 批处理大小
            early_stopping_patience: 早停耐心值
            lr_reduction_patience: 学习率降低耐心值
            save_best_model: 是否保存最佳模型
            model_save_path: 模型保存路径
            verbose: 详细程度
            
        Returns:
            keras.callbacks.History: 训练历史
        """
        if self.model is None:
            raise ValueError("模型尚未创建，请先调用 create_model()")
        
        if self.X_train is None:
            raise ValueError("训练数据尚未准备，请先调用 prepare_data()")
        
        self.logger.info("开始训练模型...")
        
        # 准备回调函数
        callbacks = []
        
        # 早停回调
        early_stopping = keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=early_stopping_patience,
            restore_best_weights=True,
            verbose=verbose
        )
        callbacks.append(early_stopping)
        
        # 学习率调度
        lr_scheduler = keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=lr_reduction_patience,
            min_lr=1e-7,
            verbose=verbose
        )
        callbacks.append(lr_scheduler)
        
        # 模型检查点
        if save_best_model:
            if model_save_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                model_save_path = f"models/best_model_{timestamp}.h5"
            
            # 确保目录存在
            os.makedirs(os.path.dirname(model_save_path), exist_ok=True)
            
            checkpoint = keras.callbacks.ModelCheckpoint(
                model_save_path,
                monitor='val_loss',
                save_best_only=True,
                save_weights_only=False,
                verbose=verbose
            )
            callbacks.append(checkpoint)
        
        # 训练模型
        start_time = time.time()
        
        self.training_history = self.model.train(
            X_train=self.X_train,
            y_train=self.y_train,
            X_val=self.X_val,
            y_val=self.y_val,
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=verbose
        )
        
        training_time = time.time() - start_time
        self.logger.info(f"模型训练完成，耗时: {training_time:.2f} 秒")
        
        return self.training_history
    
    def evaluate_model(self, detailed: bool = True) -> Dict[str, Any]:
        """
        评估模型性能
        
        Args:
            detailed: 是否进行详细评估
            
        Returns:
            Dict[str, Any]: 评估结果
        """
        if self.model is None:
            raise ValueError("模型尚未创建")
        
        if self.X_test is None:
            raise ValueError("测试数据不可用")
        
        self.logger.info("开始评估模型性能...")
        
        # 预测
        y_pred_train = self.model.predict(self.X_train)
        y_pred_val = self.model.predict(self.X_val)
        y_pred_test = self.model.predict(self.X_test)
        
        # 反标准化（如果进行了标准化）
        if hasattr(self.scaler_y, 'scale_'):
            y_train_orig = self.scaler_y.inverse_transform(self.y_train.reshape(-1, 1)).flatten()
            y_val_orig = self.scaler_y.inverse_transform(self.y_val.reshape(-1, 1)).flatten()
            y_test_orig = self.scaler_y.inverse_transform(self.y_test.reshape(-1, 1)).flatten()
            
            y_pred_train_orig = self.scaler_y.inverse_transform(y_pred_train.reshape(-1, 1)).flatten()
            y_pred_val_orig = self.scaler_y.inverse_transform(y_pred_val.reshape(-1, 1)).flatten()
            y_pred_test_orig = self.scaler_y.inverse_transform(y_pred_test.reshape(-1, 1)).flatten()
        else:
            y_train_orig = self.y_train
            y_val_orig = self.y_val
            y_test_orig = self.y_test
            y_pred_train_orig = y_pred_train.flatten()
            y_pred_val_orig = y_pred_val.flatten()
            y_pred_test_orig = y_pred_test.flatten()
        
        # 计算评估指标
        def calculate_metrics(y_true, y_pred, dataset_name):
            return {
                f'{dataset_name}_mse': float(mean_squared_error(y_true, y_pred)),
                f'{dataset_name}_rmse': float(np.sqrt(mean_squared_error(y_true, y_pred))),
                f'{dataset_name}_mae': float(mean_absolute_error(y_true, y_pred)),
                f'{dataset_name}_r2': float(r2_score(y_true, y_pred)),
                f'{dataset_name}_mape': float(np.mean(np.abs((y_true - y_pred) / y_true)) * 100)
            }
        
        # 评估结果
        self.evaluation_results = {}
        self.evaluation_results.update(calculate_metrics(y_train_orig, y_pred_train_orig, 'train'))
        self.evaluation_results.update(calculate_metrics(y_val_orig, y_pred_val_orig, 'val'))
        self.evaluation_results.update(calculate_metrics(y_test_orig, y_pred_test_orig, 'test'))
        
        # 详细评估
        if detailed:
            # 性能分析
            if self.model.gpu_accelerator:
                performance_metrics = self.model.profile_performance(self.X_test[:100])
                self.evaluation_results['performance'] = performance_metrics
            
            # GPU信息
            gpu_info = self.model.get_gpu_info()
            self.evaluation_results['gpu_info'] = gpu_info
            
            # 内存使用
            memory_info = self.model.get_memory_usage()
            self.evaluation_results['memory_usage'] = memory_info
        
        # 打印评估结果
        self.logger.info("模型评估完成:")
        self.logger.info(f"  测试集 R²: {self.evaluation_results['test_r2']:.4f}")
        self.logger.info(f"  测试集 RMSE: {self.evaluation_results['test_rmse']:.4f}")
        self.logger.info(f"  测试集 MAE: {self.evaluation_results['test_mae']:.4f}")
        self.logger.info(f"  测试集 MAPE: {self.evaluation_results['test_mape']:.2f}%")
        
        return self.evaluation_results
    
    def plot_training_history(self, save_path: Optional[str] = None, figsize: Tuple[int, int] = (12, 8)):
        """
        绘制训练历史
        
        Args:
            save_path: 保存路径
            figsize: 图形大小
        """
        if self.training_history is None:
            self.logger.warning("训练历史不可用")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=figsize)
        fig.suptitle('训练历史', fontsize=16)
        
        # 损失函数
        axes[0, 0].plot(self.training_history.history['loss'], label='训练损失')
        axes[0, 0].plot(self.training_history.history['val_loss'], label='验证损失')
        axes[0, 0].set_title('损失函数')
        axes[0, 0].set_xlabel('轮次')
        axes[0, 0].set_ylabel('损失')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # MAE
        axes[0, 1].plot(self.training_history.history['mae'], label='训练MAE')
        axes[0, 1].plot(self.training_history.history['val_mae'], label='验证MAE')
        axes[0, 1].set_title('平均绝对误差')
        axes[0, 1].set_xlabel('轮次')
        axes[0, 1].set_ylabel('MAE')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # R²
        if 'r_squared_metric' in self.training_history.history:
            axes[1, 0].plot(self.training_history.history['r_squared_metric'], label='训练R²')
            axes[1, 0].plot(self.training_history.history['val_r_squared_metric'], label='验证R²')
            axes[1, 0].set_title('决定系数')
            axes[1, 0].set_xlabel('轮次')
            axes[1, 0].set_ylabel('R²')
            axes[1, 0].legend()
            axes[1, 0].grid(True)
        
        # 学习率
        if 'lr' in self.training_history.history:
            axes[1, 1].plot(self.training_history.history['lr'], label='学习率')
            axes[1, 1].set_title('学习率变化')
            axes[1, 1].set_xlabel('轮次')
            axes[1, 1].set_ylabel('学习率')
            axes[1, 1].set_yscale('log')
            axes[1, 1].legend()
            axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"训练历史图已保存到: {save_path}")
        
        plt.show()
    
    def plot_predictions(self, save_path: Optional[str] = None, figsize: Tuple[int, int] = (15, 5)):
        """
        绘制预测结果
        
        Args:
            save_path: 保存路径
            figsize: 图形大小
        """
        if self.model is None:
            self.logger.warning("模型不可用")
            return
        
        # 预测
        y_pred_train = self.model.predict(self.X_train)
        y_pred_val = self.model.predict(self.X_val)
        y_pred_test = self.model.predict(self.X_test)
        
        # 反标准化
        if hasattr(self.scaler_y, 'scale_'):
            y_train_orig = self.scaler_y.inverse_transform(self.y_train.reshape(-1, 1)).flatten()
            y_val_orig = self.scaler_y.inverse_transform(self.y_val.reshape(-1, 1)).flatten()
            y_test_orig = self.scaler_y.inverse_transform(self.y_test.reshape(-1, 1)).flatten()
            
            y_pred_train_orig = self.scaler_y.inverse_transform(y_pred_train.reshape(-1, 1)).flatten()
            y_pred_val_orig = self.scaler_y.inverse_transform(y_pred_val.reshape(-1, 1)).flatten()
            y_pred_test_orig = self.scaler_y.inverse_transform(y_pred_test.reshape(-1, 1)).flatten()
        else:
            y_train_orig = self.y_train
            y_val_orig = self.y_val
            y_test_orig = self.y_test
            y_pred_train_orig = y_pred_train.flatten()
            y_pred_val_orig = y_pred_val.flatten()
            y_pred_test_orig = y_pred_test.flatten()
        
        fig, axes = plt.subplots(1, 3, figsize=figsize)
        fig.suptitle('预测结果对比', fontsize=16)
        
        datasets = [
            (y_train_orig, y_pred_train_orig, '训练集'),
            (y_val_orig, y_pred_val_orig, '验证集'),
            (y_test_orig, y_pred_test_orig, '测试集')
        ]
        
        for i, (y_true, y_pred, title) in enumerate(datasets):
            axes[i].scatter(y_true, y_pred, alpha=0.6)
            
            # 绘制理想线
            min_val = min(y_true.min(), y_pred.min())
            max_val = max(y_true.max(), y_pred.max())
            axes[i].plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='理想线')
            
            axes[i].set_xlabel('真实值')
            axes[i].set_ylabel('预测值')
            axes[i].set_title(title)
            axes[i].legend()
            axes[i].grid(True)
            
            # 添加R²信息
            r2 = r2_score(y_true, y_pred)
            axes[i].text(0.05, 0.95, f'R² = {r2:.4f}', transform=axes[i].transAxes,
                        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"预测结果图已保存到: {save_path}")
        
        plt.show()
    
    def save_results(self, save_dir: str):
        """
        保存训练结果
        
        Args:
            save_dir: 保存目录
        """
        os.makedirs(save_dir, exist_ok=True)
        
        # 保存模型
        if self.model is not None:
            model_path = os.path.join(save_dir, 'model.h5')
            self.model.save_model(model_path)
        
        # 保存评估结果
        if self.evaluation_results:
            results_path = os.path.join(save_dir, 'evaluation_results.json')
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(self.evaluation_results, f, indent=2, ensure_ascii=False)
        
        # 保存训练历史
        if self.training_history is not None:
            history_path = os.path.join(save_dir, 'training_history.json')
            history_dict = {key: [float(val) for val in values] 
                           for key, values in self.training_history.history.items()}
            with open(history_path, 'w', encoding='utf-8') as f:
                json.dump(history_dict, f, indent=2, ensure_ascii=False)
        
        # 保存配置
        config_path = os.path.join(save_dir, 'config.json')
        config_dict = {
            'input_dim': self.config.input_dim,
            'output_dim': self.config.output_dim,
            'architecture': self.config.architecture,
            'layers': self.config.layers,
            'activation': self.config.activation,
            'dropout_rate': self.config.dropout_rate,
            'learning_rate': self.config.learning_rate,
            'batch_normalization': self.config.batch_normalization,
            'l2_regularization': self.config.l2_regularization,
            'enable_gpu': self.config.enable_gpu,
            'mixed_precision': self.config.mixed_precision,
            'xla_acceleration': self.config.xla_acceleration,
            'auto_batch_size': self.config.auto_batch_size
        }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"训练结果已保存到: {save_dir}")
    
    def generate_report(self) -> str:
        """
        生成训练报告
        
        Returns:
            str: 训练报告
        """
        if not self.evaluation_results:
            return "评估结果不可用"
        
        report = []
        report.append("=" * 60)
        report.append("深度神经网络训练报告")
        report.append("=" * 60)
        report.append("")
        
        # 模型配置
        report.append("模型配置:")
        report.append(f"  架构类型: {self.config.architecture}")
        report.append(f"  输入维度: {self.config.input_dim}")
        report.append(f"  输出维度: {self.config.output_dim}")
        report.append(f"  隐藏层: {self.config.layers}")
        report.append(f"  激活函数: {self.config.activation}")
        report.append(f"  Dropout率: {self.config.dropout_rate}")
        report.append(f"  学习率: {self.config.learning_rate}")
        report.append(f"  GPU加速: {'是' if self.config.enable_gpu else '否'}")
        report.append(f"  混合精度: {'是' if self.config.mixed_precision else '否'}")
        report.append("")
        
        # 数据集信息
        if self.X_train is not None:
            report.append("数据集信息:")
            report.append(f"  训练样本: {len(self.X_train):,}")
            report.append(f"  验证样本: {len(self.X_val):,}")
            report.append(f"  测试样本: {len(self.X_test):,}")
            report.append("")
        
        # 性能指标
        report.append("性能指标:")
        for dataset in ['train', 'val', 'test']:
            report.append(f"  {dataset.upper()}集:")
            report.append(f"    R²: {self.evaluation_results.get(f'{dataset}_r2', 0):.4f}")
            report.append(f"    RMSE: {self.evaluation_results.get(f'{dataset}_rmse', 0):.4f}")
            report.append(f"    MAE: {self.evaluation_results.get(f'{dataset}_mae', 0):.4f}")
            report.append(f"    MAPE: {self.evaluation_results.get(f'{dataset}_mape', 0):.2f}%")
        report.append("")
        
        # GPU信息
        if 'gpu_info' in self.evaluation_results:
            gpu_info = self.evaluation_results['gpu_info']
            report.append("GPU信息:")
            report.append(f"  可用GPU: {gpu_info.get('available_gpus', 0)}")
            report.append(f"  混合精度: {'是' if gpu_info.get('mixed_precision_enabled', False) else '否'}")
            report.append(f"  XLA加速: {'是' if gpu_info.get('xla_enabled', False) else '否'}")
            report.append("")
        
        # 性能分析
        if 'performance' in self.evaluation_results:
            perf = self.evaluation_results['performance']
            report.append("性能分析:")
            report.append(f"  平均推理时间: {perf.get('avg_inference_time', 0)*1000:.2f} ms")
            report.append(f"  吞吐量: {perf.get('throughput', 0):.2f} 样本/秒")
            report.append("")
        
        report.append("=" * 60)
        
        return "\n".join(report)


def create_training_pipeline(
    X: np.ndarray,
    y: np.ndarray,
    architecture: str = 'feedforward',
    complexity: str = 'medium',
    enable_gpu: bool = True,
    epochs: int = 100,
    test_size: float = 0.2,
    val_size: float = 0.2,
    save_results: bool = True,
    results_dir: str = "training_results"
) -> TrainingManager:
    """
    创建完整的训练流水线
    
    Args:
        X: 输入特征
        y: 目标变量
        architecture: 网络架构
        complexity: 复杂度
        enable_gpu: 是否启用GPU
        epochs: 训练轮数
        test_size: 测试集比例
        val_size: 验证集比例
        save_results: 是否保存结果
        results_dir: 结果保存目录
        
    Returns:
        TrainingManager: 训练管理器
    """
    # 创建配置
    input_dim = X.shape[1]
    output_dim = 1 if len(y.shape) == 1 else y.shape[1]
    
    config = GPUNetworkArchitectureFactory.create_gpu_config(
        input_dim=input_dim,
        output_dim=output_dim,
        complexity=complexity,
        enable_gpu=enable_gpu
    )
    config.architecture = architecture
    
    # 创建训练管理器
    trainer = TrainingManager(config)
    
    # 准备数据
    trainer.prepare_data(X, y, test_size=test_size, val_size=val_size)
    
    # 创建模型
    trainer.create_model()
    
    # 训练模型
    trainer.train_model(epochs=epochs)
    
    # 评估模型
    trainer.evaluate_model()
    
    # 保存结果
    if save_results:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_dir = f"{results_dir}_{timestamp}"
        trainer.save_results(save_dir)
        
        # 生成图表
        trainer.plot_training_history(os.path.join(save_dir, 'training_history.png'))
        trainer.plot_predictions(os.path.join(save_dir, 'predictions.png'))
        
        # 生成报告
        report = trainer.generate_report()
        with open(os.path.join(save_dir, 'report.txt'), 'w', encoding='utf-8') as f:
            f.write(report)
    
    return trainer


if __name__ == "__main__":
    # 示例用法
    # 生成示例数据
    np.random.seed(42)
    n_samples = 1000
    n_features = 20
    
    X = np.random.randn(n_samples, n_features)
    y = np.sum(X[:, :5], axis=1) + 0.1 * np.random.randn(n_samples)
    
    # 创建训练流水线
    trainer = create_training_pipeline(
        X=X,
        y=y,
        architecture='feedforward',
        complexity='medium',
        enable_gpu=True,
        epochs=50,
        save_results=True
    )
    
    # 打印报告
    print(trainer.generate_report())