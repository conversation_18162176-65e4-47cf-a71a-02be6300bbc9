"""
抽样缓存模块
实现抽样结果的缓存和重用机制
"""

import hashlib
import pickle
import gzip
import time
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
from dataclasses import dataclass, asdict
import numpy as np
import json
import logging
from collections import OrderedDict
import sqlite3
import psutil

from .parameter_sampler import SamplingConfig, SamplingResult, ParameterDefinition

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """缓存条目数据类"""
    key: str
    config_hash: str
    samples_hash: str
    file_path: Optional[str]
    memory_data: Optional[np.ndarray]
    metadata: Dict[str, Any]
    created_time: float
    last_accessed: float
    access_count: int
    size_bytes: int
    compression_ratio: float

@dataclass
class CacheStats:
    """缓存统计数据类"""
    total_entries: int
    memory_entries: int
    disk_entries: int
    total_size_mb: float
    hit_count: int
    miss_count: int
    hit_rate: float
    eviction_count: int
    compression_savings_mb: float

class SamplingCache:
    """抽样缓存管理器"""
    
    def __init__(self, 
                 cache_dir: str = "cache/sampling",
                 max_memory_mb: float = 500,
                 max_disk_mb: float = 2000,
                 enable_compression: bool = True,
                 enable_persistence: bool = True):
        """
        初始化抽样缓存
        
        Args:
            cache_dir: 缓存目录
            max_memory_mb: 最大内存缓存大小（MB）
            max_disk_mb: 最大磁盘缓存大小（MB）
            enable_compression: 启用压缩
            enable_persistence: 启用持久化
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.max_memory_mb = max_memory_mb
        self.max_disk_mb = max_disk_mb
        self.enable_compression = enable_compression
        self.enable_persistence = enable_persistence
        
        # 内存缓存（LRU）
        self.memory_cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.current_memory_mb = 0.0
        
        # 磁盘缓存索引
        self.disk_index: Dict[str, CacheEntry] = {}
        
        # 统计信息
        self.hit_count = 0
        self.miss_count = 0
        self.eviction_count = 0
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 初始化数据库
        if enable_persistence:
            self._init_database()
            self._load_disk_index()
    
    def _init_database(self):
        """初始化SQLite数据库"""
        self.db_path = self.cache_dir / "cache_index.db"
        
        with sqlite3.connect(str(self.db_path)) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS cache_entries (
                    key TEXT PRIMARY KEY,
                    config_hash TEXT NOT NULL,
                    samples_hash TEXT NOT NULL,
                    file_path TEXT,
                    metadata TEXT,
                    created_time REAL NOT NULL,
                    last_accessed REAL NOT NULL,
                    access_count INTEGER DEFAULT 0,
                    size_bytes INTEGER NOT NULL,
                    compression_ratio REAL DEFAULT 1.0
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_config_hash ON cache_entries(config_hash)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_last_accessed ON cache_entries(last_accessed)
            """)
    
    def _load_disk_index(self):
        """从数据库加载磁盘索引"""
        if not self.enable_persistence or not self.db_path.exists():
            return
        
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.execute("""
                    SELECT key, config_hash, samples_hash, file_path, metadata,
                           created_time, last_accessed, access_count, size_bytes, compression_ratio
                    FROM cache_entries
                """)
                
                for row in cursor:
                    key, config_hash, samples_hash, file_path, metadata_json, \
                    created_time, last_accessed, access_count, size_bytes, compression_ratio = row
                    
                    metadata = json.loads(metadata_json) if metadata_json else {}
                    
                    entry = CacheEntry(
                        key=key,
                        config_hash=config_hash,
                        samples_hash=samples_hash,
                        file_path=file_path,
                        memory_data=None,  # 磁盘缓存不在内存中
                        metadata=metadata,
                        created_time=created_time,
                        last_accessed=last_accessed,
                        access_count=access_count,
                        size_bytes=size_bytes,
                        compression_ratio=compression_ratio
                    )
                    
                    self.disk_index[key] = entry
                    
        except Exception as e:
            logger.error(f"加载磁盘索引失败: {e}")
    
    def _generate_cache_key(self, config: SamplingConfig) -> str:
        """生成缓存键"""
        # 创建配置的标准化表示
        config_dict = {
            'n_samples': config.n_samples,
            'random_seed': config.random_seed,
            'sampling_method': config.sampling_method,
            'optimization_criterion': config.optimization_criterion,
            'parameters': [
                {
                    'name': p.name,
                    'min_value': p.min_value,
                    'max_value': p.max_value,
                    'distribution': p.distribution,
                    'constraints': p.constraints
                } for p in config.parameters
            ]
        }
        
        # 生成哈希
        config_str = json.dumps(config_dict, sort_keys=True)
        return hashlib.sha256(config_str.encode()).hexdigest()
    
    def _generate_config_hash(self, config: SamplingConfig) -> str:
        """生成配置哈希（不包含样本数和随机种子）"""
        config_dict = {
            'sampling_method': config.sampling_method,
            'optimization_criterion': config.optimization_criterion,
            'parameters': [
                {
                    'name': p.name,
                    'min_value': p.min_value,
                    'max_value': p.max_value,
                    'distribution': p.distribution,
                    'constraints': p.constraints
                } for p in config.parameters
            ]
        }
        
        config_str = json.dumps(config_dict, sort_keys=True)
        return hashlib.md5(config_str.encode()).hexdigest()
    
    def _generate_samples_hash(self, samples: np.ndarray) -> str:
        """生成样本哈希"""
        return hashlib.md5(samples.tobytes()).hexdigest()
    
    def get(self, config: SamplingConfig) -> Optional[SamplingResult]:
        """从缓存获取抽样结果"""
        cache_key = self._generate_cache_key(config)
        
        with self.lock:
            # 首先检查内存缓存
            if cache_key in self.memory_cache:
                entry = self.memory_cache[cache_key]
                
                # 更新访问信息
                entry.last_accessed = time.time()
                entry.access_count += 1
                
                # 移动到末尾（LRU）
                self.memory_cache.move_to_end(cache_key)
                
                self.hit_count += 1
                
                # 重建SamplingResult
                result = SamplingResult(
                    samples=entry.memory_data,
                    parameter_names=[p.name for p in config.parameters],
                    config=config,
                    quality_metrics=entry.metadata.get('quality_metrics', {}),
                    generation_time=entry.metadata.get('generation_time', 0.0),
                    hash_signature=entry.samples_hash
                )
                
                logger.debug(f"内存缓存命中: {cache_key[:8]}...")
                return result
            
            # 检查磁盘缓存
            if cache_key in self.disk_index:
                entry = self.disk_index[cache_key]
                
                # 从磁盘加载数据
                samples = self._load_from_disk(entry.file_path)
                if samples is not None:
                    # 更新访问信息
                    entry.last_accessed = time.time()
                    entry.access_count += 1
                    
                    # 尝试加载到内存缓存
                    self._try_promote_to_memory(cache_key, entry, samples)
                    
                    self.hit_count += 1
                    
                    # 重建SamplingResult
                    result = SamplingResult(
                        samples=samples,
                        parameter_names=[p.name for p in config.parameters],
                        config=config,
                        quality_metrics=entry.metadata.get('quality_metrics', {}),
                        generation_time=entry.metadata.get('generation_time', 0.0),
                        hash_signature=entry.samples_hash
                    )
                    
                    logger.debug(f"磁盘缓存命中: {cache_key[:8]}...")
                    return result
            
            self.miss_count += 1
            return None
    
    def put(self, config: SamplingConfig, result: SamplingResult):
        """将抽样结果放入缓存"""
        cache_key = self._generate_cache_key(config)
        config_hash = self._generate_config_hash(config)
        samples_hash = self._generate_samples_hash(result.samples)
        
        with self.lock:
            # 检查是否已存在
            if cache_key in self.memory_cache or cache_key in self.disk_index:
                return
            
            # 计算数据大小
            size_bytes = result.samples.nbytes
            size_mb = size_bytes / (1024 * 1024)
            
            # 准备元数据
            metadata = {
                'quality_metrics': result.quality_metrics,
                'generation_time': result.generation_time,
                'parameter_count': len(config.parameters),
                'sample_count': config.n_samples
            }
            
            # 创建缓存条目
            entry = CacheEntry(
                key=cache_key,
                config_hash=config_hash,
                samples_hash=samples_hash,
                file_path=None,
                memory_data=result.samples.copy(),
                metadata=metadata,
                created_time=time.time(),
                last_accessed=time.time(),
                access_count=0,
                size_bytes=size_bytes,
                compression_ratio=1.0
            )
            
            # 决定存储位置
            if size_mb <= self.max_memory_mb * 0.1:  # 小于内存限制的10%，直接存内存
                self._put_in_memory(cache_key, entry)
            else:
                # 存储到磁盘
                if self.enable_persistence:
                    self._put_on_disk(cache_key, entry)
                else:
                    # 如果不启用持久化，尝试存内存
                    self._put_in_memory(cache_key, entry)
    
    def _put_in_memory(self, cache_key: str, entry: CacheEntry):
        """将条目放入内存缓存"""
        size_mb = entry.size_bytes / (1024 * 1024)
        
        # 检查是否需要清理空间
        while (self.current_memory_mb + size_mb > self.max_memory_mb and 
               len(self.memory_cache) > 0):
            self._evict_from_memory()
        
        # 如果仍然无法容纳，存储到磁盘
        if self.current_memory_mb + size_mb > self.max_memory_mb:
            if self.enable_persistence:
                self._put_on_disk(cache_key, entry)
                return
            else:
                logger.warning(f"内存缓存空间不足，丢弃缓存条目: {cache_key[:8]}...")
                return
        
        # 添加到内存缓存
        self.memory_cache[cache_key] = entry
        self.current_memory_mb += size_mb
        
        logger.debug(f"添加到内存缓存: {cache_key[:8]}..., 大小: {size_mb:.2f}MB")
    
    def _put_on_disk(self, cache_key: str, entry: CacheEntry):
        """将条目存储到磁盘"""
        if not self.enable_persistence:
            return
        
        try:
            # 生成文件路径
            filename = f"{cache_key}.pkl"
            if self.enable_compression:
                filename += ".gz"
            
            file_path = self.cache_dir / filename
            
            # 准备数据
            data = {
                'samples': entry.memory_data,
                'metadata': entry.metadata
            }
            
            # 保存到文件
            if self.enable_compression:
                with gzip.open(file_path, 'wb') as f:
                    pickle.dump(data, f)
                
                # 计算压缩比
                uncompressed_size = entry.size_bytes
                compressed_size = file_path.stat().st_size
                entry.compression_ratio = compressed_size / uncompressed_size
            else:
                with open(file_path, 'wb') as f:
                    pickle.dump(data, f)
            
            # 更新条目信息
            entry.file_path = str(file_path)
            entry.memory_data = None  # 清除内存数据
            
            # 添加到磁盘索引
            self.disk_index[cache_key] = entry
            
            # 保存到数据库
            self._save_to_database(entry)
            
            logger.debug(f"保存到磁盘缓存: {cache_key[:8]}..., 压缩比: {entry.compression_ratio:.2f}")
            
        except Exception as e:
            logger.error(f"保存磁盘缓存失败: {e}")
    
    def _load_from_disk(self, file_path: str) -> Optional[np.ndarray]:
        """从磁盘加载数据"""
        if not file_path or not Path(file_path).exists():
            return None
        
        try:
            if file_path.endswith('.gz'):
                with gzip.open(file_path, 'rb') as f:
                    data = pickle.load(f)
            else:
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)
            
            return data['samples']
            
        except Exception as e:
            logger.error(f"从磁盘加载缓存失败: {e}")
            return None
    
    def _try_promote_to_memory(self, cache_key: str, entry: CacheEntry, samples: np.ndarray):
        """尝试将磁盘缓存提升到内存缓存"""
        size_mb = entry.size_bytes / (1024 * 1024)
        
        # 如果数据较小且内存有空间，提升到内存
        if size_mb <= self.max_memory_mb * 0.2:  # 小于内存限制的20%
            # 清理空间
            while (self.current_memory_mb + size_mb > self.max_memory_mb and 
                   len(self.memory_cache) > 0):
                self._evict_from_memory()
            
            if self.current_memory_mb + size_mb <= self.max_memory_mb:
                # 创建内存条目
                memory_entry = CacheEntry(
                    key=entry.key,
                    config_hash=entry.config_hash,
                    samples_hash=entry.samples_hash,
                    file_path=entry.file_path,
                    memory_data=samples.copy(),
                    metadata=entry.metadata,
                    created_time=entry.created_time,
                    last_accessed=entry.last_accessed,
                    access_count=entry.access_count,
                    size_bytes=entry.size_bytes,
                    compression_ratio=entry.compression_ratio
                )
                
                self.memory_cache[cache_key] = memory_entry
                self.current_memory_mb += size_mb
                
                logger.debug(f"提升到内存缓存: {cache_key[:8]}...")
    
    def _evict_from_memory(self):
        """从内存缓存中驱逐最久未使用的条目"""
        if not self.memory_cache:
            return
        
        # 获取最久未使用的条目
        cache_key, entry = self.memory_cache.popitem(last=False)
        size_mb = entry.size_bytes / (1024 * 1024)
        self.current_memory_mb -= size_mb
        self.eviction_count += 1
        
        logger.debug(f"从内存缓存驱逐: {cache_key[:8]}..., 释放: {size_mb:.2f}MB")
        
        # 如果启用持久化且不在磁盘缓存中，保存到磁盘
        if (self.enable_persistence and 
            cache_key not in self.disk_index and 
            entry.memory_data is not None):
            
            entry.file_path = None  # 重置文件路径
            self._put_on_disk(cache_key, entry)
    
    def _save_to_database(self, entry: CacheEntry):
        """保存条目到数据库"""
        if not self.enable_persistence:
            return
        
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO cache_entries 
                    (key, config_hash, samples_hash, file_path, metadata, 
                     created_time, last_accessed, access_count, size_bytes, compression_ratio)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    entry.key,
                    entry.config_hash,
                    entry.samples_hash,
                    entry.file_path,
                    json.dumps(entry.metadata),
                    entry.created_time,
                    entry.last_accessed,
                    entry.access_count,
                    entry.size_bytes,
                    entry.compression_ratio
                ))
                
        except Exception as e:
            logger.error(f"保存到数据库失败: {e}")
    
    def find_similar_configs(self, config: SamplingConfig, 
                           max_results: int = 5) -> List[Tuple[str, float]]:
        """查找相似的配置"""
        config_hash = self._generate_config_hash(config)
        similar_configs = []
        
        with self.lock:
            # 检查内存缓存
            for key, entry in self.memory_cache.items():
                if entry.config_hash == config_hash:
                    # 计算相似度（基于样本数差异）
                    sample_diff = abs(config.n_samples - entry.metadata.get('sample_count', 0))
                    similarity = 1.0 / (1.0 + sample_diff / config.n_samples)
                    similar_configs.append((key, similarity))
            
            # 检查磁盘缓存
            for key, entry in self.disk_index.items():
                if key not in self.memory_cache and entry.config_hash == config_hash:
                    sample_diff = abs(config.n_samples - entry.metadata.get('sample_count', 0))
                    similarity = 1.0 / (1.0 + sample_diff / config.n_samples)
                    similar_configs.append((key, similarity))
        
        # 按相似度排序
        similar_configs.sort(key=lambda x: x[1], reverse=True)
        return similar_configs[:max_results]
    
    def get_cache_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        with self.lock:
            total_entries = len(self.memory_cache) + len(self.disk_index)
            memory_entries = len(self.memory_cache)
            disk_entries = len(self.disk_index)
            
            # 计算磁盘缓存大小
            disk_size_mb = 0.0
            for entry in self.disk_index.values():
                if entry.file_path and Path(entry.file_path).exists():
                    disk_size_mb += Path(entry.file_path).stat().st_size / (1024 * 1024)
            
            total_size_mb = self.current_memory_mb + disk_size_mb
            
            # 计算命中率
            total_requests = self.hit_count + self.miss_count
            hit_rate = self.hit_count / total_requests if total_requests > 0 else 0.0
            
            # 计算压缩节省
            compression_savings_mb = 0.0
            for entry in self.disk_index.values():
                if entry.compression_ratio < 1.0:
                    original_size_mb = entry.size_bytes / (1024 * 1024)
                    compressed_size_mb = original_size_mb * entry.compression_ratio
                    compression_savings_mb += original_size_mb - compressed_size_mb
            
            return CacheStats(
                total_entries=total_entries,
                memory_entries=memory_entries,
                disk_entries=disk_entries,
                total_size_mb=total_size_mb,
                hit_count=self.hit_count,
                miss_count=self.miss_count,
                hit_rate=hit_rate,
                eviction_count=self.eviction_count,
                compression_savings_mb=compression_savings_mb
            )
    
    def cleanup_expired_entries(self, max_age_hours: float = 24 * 7):  # 默认7天
        """清理过期的缓存条目"""
        cutoff_time = time.time() - max_age_hours * 3600
        expired_keys = []
        
        with self.lock:
            # 检查内存缓存
            for key, entry in list(self.memory_cache.items()):
                if entry.last_accessed < cutoff_time:
                    expired_keys.append(key)
                    size_mb = entry.size_bytes / (1024 * 1024)
                    self.current_memory_mb -= size_mb
                    del self.memory_cache[key]
            
            # 检查磁盘缓存
            for key, entry in list(self.disk_index.items()):
                if entry.last_accessed < cutoff_time:
                    expired_keys.append(key)
                    
                    # 删除文件
                    if entry.file_path and Path(entry.file_path).exists():
                        try:
                            Path(entry.file_path).unlink()
                        except Exception as e:
                            logger.error(f"删除缓存文件失败: {e}")
                    
                    del self.disk_index[key]
            
            # 从数据库删除
            if expired_keys and self.enable_persistence:
                try:
                    with sqlite3.connect(str(self.db_path)) as conn:
                        placeholders = ','.join(['?'] * len(expired_keys))
                        conn.execute(f"DELETE FROM cache_entries WHERE key IN ({placeholders})", 
                                   expired_keys)
                except Exception as e:
                    logger.error(f"从数据库删除过期条目失败: {e}")
        
        if expired_keys:
            logger.info(f"清理了 {len(expired_keys)} 个过期缓存条目")
        
        return len(expired_keys)
    
    def clear_cache(self):
        """清空所有缓存"""
        with self.lock:
            # 清空内存缓存
            self.memory_cache.clear()
            self.current_memory_mb = 0.0
            
            # 删除磁盘文件
            for entry in self.disk_index.values():
                if entry.file_path and Path(entry.file_path).exists():
                    try:
                        Path(entry.file_path).unlink()
                    except Exception as e:
                        logger.error(f"删除缓存文件失败: {e}")
            
            self.disk_index.clear()
            
            # 清空数据库
            if self.enable_persistence:
                try:
                    with sqlite3.connect(str(self.db_path)) as conn:
                        conn.execute("DELETE FROM cache_entries")
                except Exception as e:
                    logger.error(f"清空数据库失败: {e}")
            
            # 重置统计
            self.hit_count = 0
            self.miss_count = 0
            self.eviction_count = 0
        
        logger.info("已清空所有缓存")

class CacheManager:
    """缓存管理器"""
    
    def __init__(self, cache_config: Optional[Dict[str, Any]] = None):
        """
        初始化缓存管理器
        
        Args:
            cache_config: 缓存配置字典
        """
        config = cache_config or {}
        
        self.cache = SamplingCache(
            cache_dir=config.get('cache_dir', 'cache/sampling'),
            max_memory_mb=config.get('max_memory_mb', 500),
            max_disk_mb=config.get('max_disk_mb', 2000),
            enable_compression=config.get('enable_compression', True),
            enable_persistence=config.get('enable_persistence', True)
        )
        
        # 自动清理配置
        self.auto_cleanup_enabled = config.get('auto_cleanup_enabled', True)
        self.cleanup_interval_hours = config.get('cleanup_interval_hours', 24)
        self.max_entry_age_hours = config.get('max_entry_age_hours', 24 * 7)
        
        # 启动自动清理
        if self.auto_cleanup_enabled:
            self._start_auto_cleanup()
    
    def _start_auto_cleanup(self):
        """启动自动清理线程"""
        def cleanup_worker():
            while self.auto_cleanup_enabled:
                try:
                    time.sleep(self.cleanup_interval_hours * 3600)
                    if self.auto_cleanup_enabled:
                        self.cache.cleanup_expired_entries(self.max_entry_age_hours)
                except Exception as e:
                    logger.error(f"自动清理失败: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def get_or_compute(self, config: SamplingConfig, 
                      compute_func: Callable[[SamplingConfig], SamplingResult]) -> SamplingResult:
        """
        获取缓存结果或计算新结果
        
        Args:
            config: 抽样配置
            compute_func: 计算函数
            
        Returns:
            SamplingResult: 抽样结果
        """
        # 尝试从缓存获取
        result = self.cache.get(config)
        if result is not None:
            logger.info(f"缓存命中，样本数: {len(result.samples)}")
            return result
        
        # 缓存未命中，计算新结果
        logger.info(f"缓存未命中，开始计算，样本数: {config.n_samples}")
        start_time = time.time()
        
        result = compute_func(config)
        
        compute_time = time.time() - start_time
        logger.info(f"计算完成，耗时: {compute_time:.2f}秒")
        
        # 存入缓存
        self.cache.put(config, result)
        
        return result
    
    def precompute_common_configs(self, configs: List[SamplingConfig],
                                compute_func: Callable[[SamplingConfig], SamplingResult],
                                progress_callback: Optional[Callable] = None):
        """
        预计算常用配置
        
        Args:
            configs: 配置列表
            compute_func: 计算函数
            progress_callback: 进度回调
        """
        logger.info(f"开始预计算 {len(configs)} 个配置")
        
        for i, config in enumerate(configs):
            # 检查是否已缓存
            if self.cache.get(config) is not None:
                logger.debug(f"配置 {i+1}/{len(configs)} 已缓存，跳过")
                continue
            
            # 计算并缓存
            try:
                result = compute_func(config)
                self.cache.put(config, result)
                logger.info(f"预计算完成 {i+1}/{len(configs)}")
                
                if progress_callback:
                    progress_callback(i + 1, len(configs))
                    
            except Exception as e:
                logger.error(f"预计算配置 {i+1} 失败: {e}")
        
        logger.info("预计算完成")
    
    def get_recommendations(self, config: SamplingConfig) -> List[Dict[str, Any]]:
        """
        获取缓存建议
        
        Args:
            config: 当前配置
            
        Returns:
            List[Dict[str, Any]]: 建议列表
        """
        recommendations = []
        
        # 查找相似配置
        similar_configs = self.cache.find_similar_configs(config)
        
        for cache_key, similarity in similar_configs:
            # 获取缓存条目信息
            entry = None
            if cache_key in self.cache.memory_cache:
                entry = self.cache.memory_cache[cache_key]
            elif cache_key in self.cache.disk_index:
                entry = self.cache.disk_index[cache_key]
            
            if entry:
                recommendations.append({
                    'cache_key': cache_key[:8] + '...',
                    'similarity': similarity,
                    'sample_count': entry.metadata.get('sample_count', 0),
                    'generation_time': entry.metadata.get('generation_time', 0),
                    'last_accessed': entry.last_accessed,
                    'access_count': entry.access_count,
                    'location': 'memory' if cache_key in self.cache.memory_cache else 'disk'
                })
        
        return recommendations
    
    def export_cache_report(self, output_path: str):
        """导出缓存报告"""
        stats = self.cache.get_cache_stats()
        
        report = {
            'timestamp': time.time(),
            'cache_statistics': asdict(stats),
            'memory_usage': {
                'current_mb': self.cache.current_memory_mb,
                'max_mb': self.cache.max_memory_mb,
                'utilization': self.cache.current_memory_mb / self.cache.max_memory_mb
            },
            'top_accessed_entries': self._get_top_accessed_entries(10),
            'recommendations': self._get_cache_recommendations()
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"缓存报告已导出到: {output_path}")
    
    def _get_top_accessed_entries(self, limit: int) -> List[Dict[str, Any]]:
        """获取访问次数最多的条目"""
        all_entries = []
        
        # 收集所有条目
        for key, entry in self.cache.memory_cache.items():
            all_entries.append({
                'key': key[:8] + '...',
                'access_count': entry.access_count,
                'last_accessed': entry.last_accessed,
                'size_mb': entry.size_bytes / (1024 * 1024),
                'location': 'memory'
            })
        
        for key, entry in self.cache.disk_index.items():
            if key not in self.cache.memory_cache:
                all_entries.append({
                    'key': key[:8] + '...',
                    'access_count': entry.access_count,
                    'last_accessed': entry.last_accessed,
                    'size_mb': entry.size_bytes / (1024 * 1024),
                    'location': 'disk'
                })
        
        # 按访问次数排序
        all_entries.sort(key=lambda x: x['access_count'], reverse=True)
        return all_entries[:limit]
    
    def _get_cache_recommendations(self) -> List[str]:
        """获取缓存优化建议"""
        recommendations = []
        stats = self.cache.get_cache_stats()
        
        # 命中率建议
        if stats.hit_rate < 0.3:
            recommendations.append("缓存命中率较低，考虑增加缓存大小或调整缓存策略")
        elif stats.hit_rate > 0.8:
            recommendations.append("缓存命中率良好，当前配置有效")
        
        # 内存使用建议
        memory_utilization = self.cache.current_memory_mb / self.cache.max_memory_mb
        if memory_utilization > 0.9:
            recommendations.append("内存缓存使用率过高，考虑增加内存限制或启用磁盘缓存")
        elif memory_utilization < 0.3:
            recommendations.append("内存缓存使用率较低，可以考虑减少内存分配")
        
        # 驱逐建议
        if stats.eviction_count > stats.total_entries:
            recommendations.append("缓存驱逐频繁，建议增加缓存大小")
        
        # 压缩建议
        if stats.compression_savings_mb > 100:
            recommendations.append(f"压缩节省了 {stats.compression_savings_mb:.1f}MB 空间，压缩策略有效")
        
        return recommendations if recommendations else ["缓存配置良好，无需调整"]
    
    def stop(self):
        """停止缓存管理器"""
        self.auto_cleanup_enabled = False
        logger.info("缓存管理器已停止")
