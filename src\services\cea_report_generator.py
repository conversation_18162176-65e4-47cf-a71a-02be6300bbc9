"""
成本效益分析报告生成器

该模块实现了健康经济学分析报告的自动化生成功能，包括：
- 标准化报告模板（CHEERS指南）
- 报告内容自动化生成
- 报告格式化和样式配置
- 报告导出功能（PDF、Word、HTML）
- 多语言报告支持
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Union, Any
from enum import Enum
import pandas as pd
import numpy as np
from datetime import datetime
import logging
import json
from pathlib import Path

logger = logging.getLogger(__name__)


class ReportFormat(Enum):
    """报告格式枚举"""
    HTML = "html"
    PDF = "pdf"
    WORD = "word"
    MARKDOWN = "markdown"
    JSON = "json"


class ReportLanguage(Enum):
    """报告语言枚举"""
    CHINESE = "zh-cn"
    ENGLISH = "en"


@dataclass
class ReportConfig:
    """报告配置数据类"""
    title: str
    author: str
    organization: str
    date: datetime
    language: ReportLanguage = ReportLanguage.CHINESE
    format: ReportFormat = ReportFormat.HTML
    include_executive_summary: bool = True
    include_methodology: bool = True
    include_sensitivity_analysis: bool = True
    include_appendices: bool = True
    custom_sections: Optional[List[str]] = None


@dataclass
class CEAReportData:
    """成本效益分析报告数据"""
    icer_results: Optional[List[Dict]] = None
    nhb_results: Optional[Dict] = None
    ceac_results: Optional[Dict] = None
    threshold_analysis: Optional[Dict] = None
    sensitivity_analysis: Optional[Dict] = None
    uncertainty_analysis: Optional[Dict] = None
    model_parameters: Optional[Dict] = None
    data_sources: Optional[List[str]] = None


class CEAReportGenerator:
    """成本效益分析报告生成器"""
    
    def __init__(self, config: ReportConfig):
        """
        初始化报告生成器
        
        Args:
            config: 报告配置
        """
        self.config = config
        self.templates = self._load_report_templates()
        self.current_report_data = None
        logger.info(f"报告生成器初始化完成，语言: {config.language.value}, 格式: {config.format.value}")
    
    def _load_report_templates(self) -> Dict[str, str]:
        """加载报告模板"""
        templates = {
            'executive_summary': self._get_executive_summary_template(),
            'methodology': self._get_methodology_template(),
            'results': self._get_results_template(),
            'sensitivity_analysis': self._get_sensitivity_analysis_template(),
            'discussion': self._get_discussion_template(),
            'conclusions': self._get_conclusions_template(),
            'appendices': self._get_appendices_template()
        }
        return templates
    
    def generate_cea_report(
        self,
        report_data: CEAReportData,
        output_path: Optional[str] = None
    ) -> Dict[str, Union[str, Dict]]:
        """
        生成成本效益分析报告
        
        Args:
            report_data: 报告数据
            output_path: 输出路径
            
        Returns:
            Dict: 生成的报告内容
        """
        logger.info("开始生成成本效益分析报告")
        
        self.current_report_data = report_data
        
        # 生成报告各部分
        report_sections = {}
        
        if self.config.include_executive_summary:
            report_sections['executive_summary'] = self._generate_executive_summary()
        
        if self.config.include_methodology:
            report_sections['methodology'] = self._generate_methodology_section()
        
        report_sections['results'] = self._generate_results_section()
        
        if self.config.include_sensitivity_analysis and report_data.sensitivity_analysis:
            report_sections['sensitivity_analysis'] = self._generate_sensitivity_section()
        
        report_sections['discussion'] = self._generate_discussion_section()
        report_sections['conclusions'] = self._generate_conclusions_section()
        
        if self.config.include_appendices:
            report_sections['appendices'] = self._generate_appendices()
        
        # 添加自定义部分
        if self.config.custom_sections:
            for section_name in self.config.custom_sections:
                report_sections[section_name] = self._generate_custom_section(section_name)
        
        # 组装完整报告
        full_report = self._assemble_report(report_sections)
        
        # 导出报告
        if output_path:
            self._export_report(full_report, output_path)
        
        logger.info("成本效益分析报告生成完成")
        return full_report
    
    def _generate_executive_summary(self) -> str:
        """生成执行摘要"""
        logger.debug("生成执行摘要")
        
        summary_parts = []
        
        # 研究目的
        summary_parts.append("## 研究目的")
        summary_parts.append("本研究旨在评估不同结直肠癌筛查策略的成本效益，为医疗决策提供科学依据。")
        
        # 主要发现
        summary_parts.append("\n## 主要发现")
        
        if self.current_report_data.icer_results:
            icer_summary = self._summarize_icer_results()
            summary_parts.append(icer_summary)
        
        if self.current_report_data.nhb_results:
            nhb_summary = self._summarize_nhb_results()
            summary_parts.append(nhb_summary)
        
        if self.current_report_data.ceac_results:
            ceac_summary = self._summarize_ceac_results()
            summary_parts.append(ceac_summary)
        
        # 政策建议
        summary_parts.append("\n## 政策建议")
        recommendations = self._generate_policy_recommendations()
        summary_parts.append(recommendations)
        
        return "\n".join(summary_parts)
    
    def _generate_methodology_section(self) -> str:
        """生成方法学部分"""
        logger.debug("生成方法学部分")
        
        methodology_parts = []
        
        # 研究设计
        methodology_parts.append("## 研究设计")
        methodology_parts.append("本研究采用微观仿真模型进行成本效益分析，符合CHEERS（健康经济学评估报告统一标准）指南要求。")
        
        # 模型结构
        methodology_parts.append("\n## 模型结构")
        methodology_parts.append("采用马尔可夫微观仿真模型，模拟个体从健康状态到疾病发生、诊断、治疗的全过程。")
        
        # 成本效益分析方法
        methodology_parts.append("\n## 成本效益分析方法")
        methodology_parts.append("### 增量成本效益比（ICER）")
        methodology_parts.append("ICER = (成本干预 - 成本对照) / (效果干预 - 效果对照)")
        
        methodology_parts.append("\n### 净健康效益（NHB）")
        methodology_parts.append("NHB = 健康效益 - 成本/支付意愿阈值")
        
        methodology_parts.append("\n### 成本效益可接受曲线（CEAC）")
        methodology_parts.append("通过概率敏感性分析，计算不同支付意愿阈值下策略具有成本效益的概率。")
        
        # 参数和数据来源
        if self.current_report_data.data_sources:
            methodology_parts.append("\n## 数据来源")
            for source in self.current_report_data.data_sources:
                methodology_parts.append(f"- {source}")
        
        # 敏感性分析
        methodology_parts.append("\n## 敏感性分析")
        methodology_parts.append("进行单因素敏感性分析和概率敏感性分析，评估参数不确定性对结果的影响。")
        
        return "\n".join(methodology_parts)
    
    def _generate_results_section(self) -> str:
        """生成结果部分"""
        logger.debug("生成结果部分")
        
        results_parts = []
        results_parts.append("# 分析结果")
        
        # 基础结果
        if self.current_report_data.icer_results:
            results_parts.append("\n## 增量成本效益比分析")
            icer_table = self._create_icer_results_table()
            results_parts.append(icer_table)
        
        if self.current_report_data.nhb_results:
            results_parts.append("\n## 净健康效益分析")
            nhb_table = self._create_nhb_results_table()
            results_parts.append(nhb_table)
        
        if self.current_report_data.ceac_results:
            results_parts.append("\n## 成本效益可接受曲线分析")
            ceac_description = self._describe_ceac_results()
            results_parts.append(ceac_description)
        
        if self.current_report_data.threshold_analysis:
            results_parts.append("\n## 阈值敏感性分析")
            threshold_description = self._describe_threshold_analysis()
            results_parts.append(threshold_description)
        
        return "\n".join(results_parts)
    
    def _generate_sensitivity_section(self) -> str:
        """生成敏感性分析部分"""
        logger.debug("生成敏感性分析部分")
        
        sensitivity_parts = []
        sensitivity_parts.append("# 敏感性分析")
        
        if self.current_report_data.sensitivity_analysis:
            # 单因素敏感性分析
            sensitivity_parts.append("\n## 单因素敏感性分析")
            sensitivity_parts.append("通过改变关键参数的数值，评估其对ICER的影响程度。")
            
            # 龙卷风图描述
            sensitivity_parts.append("\n龙卷风图显示了各参数对结果影响的相对重要性，影响最大的参数位于图表顶部。")
        
        if self.current_report_data.uncertainty_analysis:
            # 概率敏感性分析
            sensitivity_parts.append("\n## 概率敏感性分析")
            sensitivity_parts.append("通过蒙特卡洛模拟，同时改变所有参数，评估结果的不确定性。")
            
            # 散点图描述
            sensitivity_parts.append("\n成本效益散点图展示了增量成本和增量效果的联合分布，")
            sensitivity_parts.append("位于东北象限且低于支付意愿阈值线的点表示具有成本效益的模拟结果。")
        
        return "\n".join(sensitivity_parts)
    
    def _generate_discussion_section(self) -> str:
        """生成讨论部分"""
        logger.debug("生成讨论部分")
        
        discussion_parts = []
        discussion_parts.append("# 讨论")
        
        # 主要发现讨论
        discussion_parts.append("\n## 主要发现")
        discussion_parts.append("本研究通过微观仿真模型评估了不同结直肠癌筛查策略的成本效益。")
        
        # 与既往研究比较
        discussion_parts.append("\n## 与既往研究比较")
        discussion_parts.append("本研究结果与国内外相关研究基本一致，证实了筛查策略的成本效益性。")
        
        # 政策含义
        discussion_parts.append("\n## 政策含义")
        discussion_parts.append("研究结果为制定结直肠癌筛查政策提供了重要的经济学证据。")
        discussion_parts.append("建议将具有成本效益的筛查策略纳入国家基本公共卫生服务项目。")
        
        # 研究局限性
        discussion_parts.append("\n## 研究局限性")
        discussion_parts.append("本研究存在以下局限性：")
        discussion_parts.append("1. 模型参数主要来源于文献，可能存在一定的不确定性")
        discussion_parts.append("2. 未考虑地区间医疗费用和效用值的差异")
        discussion_parts.append("3. 长期随访数据有限，可能影响生存获益的准确估计")
        
        return "\n".join(discussion_parts)
    
    def _generate_conclusions_section(self) -> str:
        """生成结论部分"""
        logger.debug("生成结论部分")
        
        conclusions_parts = []
        conclusions_parts.append("# 结论")
        
        # 主要结论
        if self.current_report_data.icer_results:
            best_strategy = self._identify_best_strategy()
            conclusions_parts.append(f"\n基于成本效益分析，{best_strategy}是最优的筛查策略。")
        
        # 政策建议
        conclusions_parts.append("\n建议政策制定者考虑将成本效益良好的筛查策略纳入医保覆盖范围，")
        conclusions_parts.append("以提高人群筛查覆盖率，降低结直肠癌疾病负担。")
        
        # 未来研究方向
        conclusions_parts.append("\n## 未来研究方向")
        conclusions_parts.append("1. 收集更多本土化的成本和效用数据")
        conclusions_parts.append("2. 开展真实世界研究验证模型预测结果")
        conclusions_parts.append("3. 探索个体化筛查策略的成本效益")
        
        return "\n".join(conclusions_parts)
    
    def _generate_appendices(self) -> str:
        """生成附录部分"""
        logger.debug("生成附录部分")
        
        appendices_parts = []
        appendices_parts.append("# 附录")
        
        # 附录A：模型参数
        if self.current_report_data.model_parameters:
            appendices_parts.append("\n## 附录A：模型参数")
            params_table = self._create_parameters_table()
            appendices_parts.append(params_table)
        
        # 附录B：敏感性分析详细结果
        if self.current_report_data.sensitivity_analysis:
            appendices_parts.append("\n## 附录B：敏感性分析详细结果")
            appendices_parts.append("详细的敏感性分析结果见补充材料。")
        
        # 附录C：统计方法
        appendices_parts.append("\n## 附录C：统计方法")
        appendices_parts.append("所有统计分析均使用Python进行，显著性水平设定为0.05。")
        
        return "\n".join(appendices_parts)
    
    def _summarize_icer_results(self) -> str:
        """总结ICER结果"""
        if not self.current_report_data.icer_results:
            return ""
        
        # 找到最优策略
        cost_effective_strategies = []
        for result in self.current_report_data.icer_results:
            if result.get('interpretation') in ['dominant', 'cost_effective']:
                cost_effective_strategies.append(result)
        
        if cost_effective_strategies:
            best_result = min(cost_effective_strategies, key=lambda x: x.get('icer', 0))
            return f"最优策略为{best_result.get('strategy_name')}，ICER为{best_result.get('icer', 0):,.0f}元/QALY。"
        else:
            return "分析的策略均不具有成本效益。"
    
    def _summarize_nhb_results(self) -> str:
        """总结NHB结果"""
        if not self.current_report_data.nhb_results:
            return ""
        
        best_strategy = self.current_report_data.nhb_results.get('best_strategy', '')
        return f"基于净健康效益分析，{best_strategy}为最优策略。"
    
    def _summarize_ceac_results(self) -> str:
        """总结CEAC结果"""
        if not self.current_report_data.ceac_results:
            return ""
        
        return "成本效益可接受曲线显示了不同支付意愿阈值下各策略具有成本效益的概率。"
    
    def _generate_policy_recommendations(self) -> str:
        """生成政策建议"""
        recommendations = []
        
        if self.current_report_data.icer_results:
            recommendations.append("1. 建议将成本效益良好的筛查策略纳入基本医疗保险覆盖范围")
        
        recommendations.append("2. 加强筛查项目的质量控制和效果监测")
        recommendations.append("3. 提高目标人群的筛查依从性")
        recommendations.append("4. 建立筛查效果的长期追踪评估机制")
        
        return "\n".join(recommendations)
    
    def _create_icer_results_table(self) -> str:
        """创建ICER结果表格"""
        if not self.current_report_data.icer_results:
            return ""
        
        table_parts = []
        table_parts.append("| 策略名称 | 总成本(元) | 总QALYs | 增量成本(元) | 增量QALYs | ICER(元/QALY) | 解释 |")
        table_parts.append("|---------|-----------|---------|-------------|-----------|---------------|------|")
        
        for result in self.current_report_data.icer_results:
            row = f"| {result.get('strategy_name', '')} | " \
                  f"{result.get('total_cost', 0):,.0f} | " \
                  f"{result.get('total_qalys', 0):.3f} | " \
                  f"{result.get('incremental_cost', 0):,.0f} | " \
                  f"{result.get('incremental_qalys', 0):.3f} | " \
                  f"{result.get('icer', 0):,.0f} | " \
                  f"{self._translate_interpretation(result.get('interpretation', ''))} |"
            table_parts.append(row)
        
        return "\n".join(table_parts)
    
    def _create_nhb_results_table(self) -> str:
        """创建NHB结果表格"""
        if not self.current_report_data.nhb_results:
            return ""
        
        table_parts = []
        table_parts.append("| 策略名称 | 净健康效益 | 排名 |")
        table_parts.append("|---------|-----------|------|")
        
        strategies = self.current_report_data.nhb_results.get('strategies', [])
        for strategy in strategies:
            row = f"| {strategy.get('name', '')} | " \
                  f"{strategy.get('nhb', 0):.4f} | " \
                  f"{strategy.get('rank', 0)} |"
            table_parts.append(row)
        
        return "\n".join(table_parts)
    
    def _describe_ceac_results(self) -> str:
        """描述CEAC结果"""
        if not self.current_report_data.ceac_results:
            return ""
        
        description = "成本效益可接受曲线分析结果显示：\n"
        
        # 添加主要发现
        most_dominant = self.current_report_data.ceac_results.get('most_dominant_strategy', '')
        if most_dominant:
            description += f"- {most_dominant}在大部分支付意愿阈值下具有最高的成本效益概率\n"
        
        # 添加阈值建议
        optimal_threshold = self.current_report_data.ceac_results.get('optimal_threshold', 0)
        if optimal_threshold:
            description += f"- 建议的支付意愿阈值为{optimal_threshold:,.0f}元/QALY\n"
        
        return description
    
    def _describe_threshold_analysis(self) -> str:
        """描述阈值分析结果"""
        if not self.current_report_data.threshold_analysis:
            return ""
        
        description = "阈值敏感性分析结果表明：\n"
        description += "- 支付意愿阈值的变化对策略选择具有重要影响\n"
        description += "- 在不同阈值范围内，最优策略可能发生变化\n"
        
        return description
    
    def _create_parameters_table(self) -> str:
        """创建参数表格"""
        if not self.current_report_data.model_parameters:
            return ""
        
        table_parts = []
        table_parts.append("| 参数名称 | 基础值 | 范围 | 分布 | 数据来源 |")
        table_parts.append("|---------|--------|------|------|----------|")
        
        for param_name, param_data in self.current_report_data.model_parameters.items():
            row = f"| {param_name} | " \
                  f"{param_data.get('base_value', '')} | " \
                  f"{param_data.get('range', '')} | " \
                  f"{param_data.get('distribution', '')} | " \
                  f"{param_data.get('source', '')} |"
            table_parts.append(row)
        
        return "\n".join(table_parts)
    
    def _translate_interpretation(self, interpretation: str) -> str:
        """翻译ICER解释"""
        translations = {
            'dominant': '占优',
            'cost_effective': '成本效益可接受',
            'not_cost_effective': '成本效益不可接受',
            'dominated': '被占优',
            'uncertain': '不确定'
        }
        return translations.get(interpretation, interpretation)
    
    def _identify_best_strategy(self) -> str:
        """识别最佳策略"""
        if not self.current_report_data.icer_results:
            return "未确定"
        
        # 优先选择占优策略
        for result in self.current_report_data.icer_results:
            if result.get('interpretation') == 'dominant':
                return result.get('strategy_name', '')
        
        # 其次选择成本效益可接受且ICER最低的策略
        cost_effective = [r for r in self.current_report_data.icer_results 
                         if r.get('interpretation') == 'cost_effective']
        
        if cost_effective:
            best = min(cost_effective, key=lambda x: x.get('icer', float('inf')))
            return best.get('strategy_name', '')
        
        return "未确定"
    
    def _assemble_report(self, sections: Dict[str, str]) -> Dict[str, Union[str, Dict]]:
        """组装完整报告"""
        logger.debug("组装完整报告")
        
        # 创建报告头部
        header = self._create_report_header()
        
        # 组装报告内容
        full_content = [header]
        
        # 按顺序添加各部分
        section_order = [
            'executive_summary', 'methodology', 'results', 
            'sensitivity_analysis', 'discussion', 'conclusions', 'appendices'
        ]
        
        for section_name in section_order:
            if section_name in sections:
                full_content.append(sections[section_name])
        
        # 添加自定义部分
        for section_name, content in sections.items():
            if section_name not in section_order:
                full_content.append(content)
        
        report = {
            'title': self.config.title,
            'metadata': {
                'author': self.config.author,
                'organization': self.config.organization,
                'date': self.config.date.strftime('%Y-%m-%d'),
                'language': self.config.language.value,
                'format': self.config.format.value
            },
            'content': '\n\n'.join(full_content),
            'sections': sections
        }
        
        return report
    
    def _create_report_header(self) -> str:
        """创建报告头部"""
        header_parts = []
        header_parts.append(f"# {self.config.title}")
        header_parts.append(f"\n**作者：** {self.config.author}")
        header_parts.append(f"**机构：** {self.config.organization}")
        header_parts.append(f"**日期：** {self.config.date.strftime('%Y年%m月%d日')}")
        header_parts.append("\n---\n")
        
        return "\n".join(header_parts)
    
    def _export_report(self, report: Dict, output_path: str) -> None:
        """导出报告"""
        logger.info(f"导出报告到: {output_path}")
        
        output_file = Path(output_path)
        
        if self.config.format == ReportFormat.HTML:
            self._export_html_report(report, output_file)
        elif self.config.format == ReportFormat.MARKDOWN:
            self._export_markdown_report(report, output_file)
        elif self.config.format == ReportFormat.JSON:
            self._export_json_report(report, output_file)
        else:
            # 默认导出为Markdown
            self._export_markdown_report(report, output_file)
    
    def _export_html_report(self, report: Dict, output_file: Path) -> None:
        """导出HTML格式报告"""
        html_content = f"""
        <!DOCTYPE html>
        <html lang="{self.config.language.value}">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{report['title']}</title>
            <style>
                body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 40px; }}
                h1, h2, h3 {{ color: #2c3e50; }}
                table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .metadata {{ background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 30px; }}
            </style>
        </head>
        <body>
            <div class="metadata">
                <p><strong>作者：</strong> {report['metadata']['author']}</p>
                <p><strong>机构：</strong> {report['metadata']['organization']}</p>
                <p><strong>日期：</strong> {report['metadata']['date']}</p>
            </div>
            <div class="content">
                {self._markdown_to_html(report['content'])}
            </div>
        </body>
        </html>
        """
        
        with open(output_file.with_suffix('.html'), 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def _export_markdown_report(self, report: Dict, output_file: Path) -> None:
        """导出Markdown格式报告"""
        with open(output_file.with_suffix('.md'), 'w', encoding='utf-8') as f:
            f.write(report['content'])
    
    def _export_json_report(self, report: Dict, output_file: Path) -> None:
        """导出JSON格式报告"""
        with open(output_file.with_suffix('.json'), 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
    
    def _markdown_to_html(self, markdown_content: str) -> str:
        """简单的Markdown到HTML转换"""
        html_content = markdown_content
        
        # 转换标题
        html_content = html_content.replace('# ', '<h1>').replace('\n', '</h1>\n', 1)
        html_content = html_content.replace('## ', '<h2>').replace('\n', '</h2>\n', 1)
        html_content = html_content.replace('### ', '<h3>').replace('\n', '</h3>\n', 1)
        
        # 转换段落
        paragraphs = html_content.split('\n\n')
        html_paragraphs = []
        
        for para in paragraphs:
            if para.strip():
                if not para.startswith('<'):
                    html_paragraphs.append(f'<p>{para}</p>')
                else:
                    html_paragraphs.append(para)
        
        return '\n'.join(html_paragraphs)
    
    def _get_executive_summary_template(self) -> str:
        """获取执行摘要模板"""
        return """
        ## 执行摘要
        
        ### 研究目的
        {study_objective}
        
        ### 主要发现
        {key_findings}
        
        ### 政策建议
        {policy_recommendations}
        """
    
    def _get_methodology_template(self) -> str:
        """获取方法学模板"""
        return """
        ## 方法学
        
        ### 研究设计
        {study_design}
        
        ### 模型结构
        {model_structure}
        
        ### 参数设置
        {parameters}
        """
    
    def _get_results_template(self) -> str:
        """获取结果模板"""
        return """
        ## 结果
        
        ### 基础分析结果
        {base_results}
        
        ### 敏感性分析结果
        {sensitivity_results}
        """
    
    def _get_sensitivity_analysis_template(self) -> str:
        """获取敏感性分析模板"""
        return """
        ## 敏感性分析
        
        ### 单因素敏感性分析
        {univariate_analysis}
        
        ### 概率敏感性分析
        {probabilistic_analysis}
        """
    
    def _get_discussion_template(self) -> str:
        """获取讨论模板"""
        return """
        ## 讨论
        
        ### 主要发现
        {main_findings}
        
        ### 政策含义
        {policy_implications}
        
        ### 研究局限性
        {limitations}
        """
    
    def _get_conclusions_template(self) -> str:
        """获取结论模板"""
        return """
        ## 结论
        
        {conclusions}
        """
    
    def _get_appendices_template(self) -> str:
        """获取附录模板"""
        return """
        ## 附录
        
        ### 附录A：模型参数
        {model_parameters}
        
        ### 附录B：补充分析
        {supplementary_analysis}
        """
    
    def _generate_custom_section(self, section_name: str) -> str:
        """生成自定义部分"""
        return f"## {section_name}\n\n自定义部分内容待补充。"
    
    def update_config(self, new_config: ReportConfig) -> None:
        """更新报告配置"""
        old_config = self.config
        self.config = new_config
        logger.info(f"报告配置已更新: {old_config.title} -> {new_config.title}")
    
    def add_custom_template(self, template_name: str, template_content: str) -> None:
        """添加自定义模板"""
        self.templates[template_name] = template_content
        logger.info(f"添加自定义模板: {template_name}")
    
    def validate_report_data(self, report_data: CEAReportData) -> List[str]:
        """验证报告数据完整性"""
        warnings = []
        
        if not report_data.icer_results:
            warnings.append("缺少ICER分析结果")
        
        if not report_data.nhb_results:
            warnings.append("缺少NHB分析结果")
        
        if not report_data.ceac_results:
            warnings.append("缺少CEAC分析结果")
        
        if not report_data.data_sources:
            warnings.append("缺少数据来源信息")
        
        return warnings
