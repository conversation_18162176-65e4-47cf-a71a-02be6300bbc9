"""
Life Years Gained (LYG) Calculator for health outcomes assessment.
"""
from typing import Dict, List, Tuple, Optional
import numpy as np
from src.core.population import Population
from src.modules.population.life_table import LifeTable


class LYGCalculator:
    """Calculator for Life Years Gained (LYG) comparing intervention and control scenarios."""
    
    def __init__(self, life_table: Optional[LifeTable] = None):
        """
        Initialize LYG calculator with optional life table.
        
        Args:
            life_table: Life table for reference survival data (optional)
        """
        self.life_table = life_table
    
    def calculate_lyg(
        self, 
        intervention_survival: List[float], 
        control_survival: List[float],
        ages: List[int]
    ) -> Dict:
        """
        Calculate life years gained by comparing intervention and control survival.
        
        Args:
            intervention_survival: Survival probabilities for intervention group
            control_survival: Survival probabilities for control group
            ages: Ages corresponding to survival probabilities
            
        Returns:
            Dictionary with LYG calculation results
        """
        if len(intervention_survival) != len(control_survival) or len(intervention_survival) != len(ages):
            raise ValueError("All input lists must have the same length")
        
        lyg_by_age = []
        total_lyg = 0.0
        
        for i, age in enumerate(ages):
            # Calculate life years difference at this age
            intervention_ly = intervention_survival[i]
            control_ly = control_survival[i]
            age_specific_lyg = intervention_ly - control_ly
            
            lyg_by_age.append({
                'age': age,
                'intervention_ly': intervention_ly,
                'control_ly': control_ly,
                'lyg': age_specific_lyg
            })
            
            total_lyg += age_specific_lyg
        
        return {
            'total_lyg': total_lyg,
            'lyg_by_age': lyg_by_age,
            'mean_lyg_per_person': total_lyg / len(ages) if ages else 0
        }
    
    def calculate_discounted_lyg(
        self, 
        lyg_result: Dict, 
        discount_rate: float = 0.03
    ) -> Dict:
        """
        Calculate discounted life years gained.
        
        Args:
            lyg_result: Result from calculate_lyg method
            discount_rate: Annual discount rate (default: 0.03 or 3%)
            
        Returns:
            LYG result with discounted values added
        """
        if 'lyg_by_age' not in lyg_result:
            raise ValueError("Invalid LYG result format")
        
        discounted_lyg = 0.0
        
        # Determine baseline age for discounting
        if lyg_result['lyg_by_age']:
            baseline_age = min(item['age'] for item in lyg_result['lyg_by_age'])
        else:
            baseline_age = 0
        
        # Calculate discounted LYG for each age
        for item in lyg_result['lyg_by_age']:
            years_from_baseline = item['age'] - baseline_age
            discount_factor = (1 + discount_rate) ** (-years_from_baseline)
            discounted_age_lyg = item['lyg'] * discount_factor
            discounted_lyg += discounted_age_lyg
            item['discounted_lyg'] = discounted_age_lyg
        
        lyg_result['total_discounted_lyg'] = discounted_lyg
        return lyg_result
    
    def calculate_population_lyg(
        self,
        intervention_population: Population,
        control_population: Population
    ) -> Dict:
        """
        Calculate life years gained for entire populations.
        
        Args:
            intervention_population: Population receiving intervention
            control_population: Control population
            
        Returns:
            Dictionary with population-level LYG results
        """
        # This is a simplified implementation - in practice, this would involve
        # complex survival analysis comparing the two populations
        
        # For demonstration purposes, we'll create some sample data
        # In a real implementation, this would use actual survival data from populations
        
        # Extract ages from populations
        intervention_ages = [getattr(ind, 'age', 50) for ind in intervention_population.individuals]
        control_ages = [getattr(ind, 'age', 50) for ind in control_population.individuals]
        
        # Create sample survival data (this would come from actual model outputs)
        max_age = max(max(intervention_ages), max(control_ages)) if intervention_ages and control_ages else 100
        ages = list(range(min(min(intervention_ages), min(control_ages)) if intervention_ages and control_ages else 40, 
                          max_age + 1))
        
        # Generate sample survival curves (in practice, these would come from the model)
        # Exponential decay model for demonstration
        intervention_survival = [np.exp(-0.01 * (age - min(ages))) for age in ages]
        control_survival = [np.exp(-0.015 * (age - min(ages))) for age in ages]
        
        # Calculate LYG
        lyg_result = self.calculate_lyg(intervention_survival, control_survival, ages)
        discounted_lyg_result = self.calculate_discounted_lyg(lyg_result)
        
        return {
            'intervention_population_size': len(intervention_population.individuals),
            'control_population_size': len(control_population.individuals),
            'lyg_analysis': discounted_lyg_result
        }