"""
训练监控器模块单元测试
"""

import pytest
import numpy as np
import tempfile
import os
import json
from unittest.mock import Mock, patch, MagicMock
import tensorflow as tf
from tensorflow import keras

from src.calibration.training_monitor import TrainingMonitor


class TestTrainingMonitor:
    """TrainingMonitor测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.monitor = TrainingMonitor(
            patience=5,
            min_delta=0.001,
            monitor='val_loss',
            mode='min',
            restore_best_weights=True,
            verbose=1
        )
    
    def test_initialization(self):
        """测试初始化"""
        assert self.monitor.patience == 5
        assert self.monitor.min_delta == 0.001
        assert self.monitor.monitor == 'val_loss'
        assert self.monitor.mode == 'min'
        assert self.monitor.restore_best_weights is True
        assert self.monitor.verbose == 1
        
        # 检查初始状态
        assert self.monitor.best_score == float('inf')
        assert self.monitor.wait == 0
        assert self.monitor.stopped_epoch == 0
        assert self.monitor.best_weights is None
        
        # 检查历史记录
        assert self.monitor.training_history == []
        assert self.monitor.epoch_logs == []
        assert self.monitor.training_start_time is None
        assert self.monitor.training_end_time is None
        
        # 检查回调列表
        assert self.monitor.callbacks == []
        assert self.monitor.logger is not None
    
    def test_initialization_max_mode(self):
        """测试最大化模式初始化"""
        monitor = TrainingMonitor(mode='max')
        assert monitor.best_score == float('-inf')
    
    def test_create_callbacks(self):
        """测试创建回调函数"""
        with tempfile.TemporaryDirectory() as temp_dir:
            model_path = os.path.join(temp_dir, "model.h5")
            log_dir = os.path.join(temp_dir, "logs")
            
            callbacks = self.monitor.create_callbacks(
                model_save_path=model_path,
                log_dir=log_dir,
                checkpoint_freq=5,
                reduce_lr_patience=3,
                reduce_lr_factor=0.5,
                min_lr=1e-7
            )
            
            assert len(callbacks) > 0
            
            # 检查回调类型
            callback_types = [type(cb).__name__ for cb in callbacks]
            assert 'EarlyStopping' in callback_types
            assert 'ModelCheckpoint' in callback_types
            assert 'ReduceLROnPlateau' in callback_types
    
    def test_start_monitoring(self):
        """测试开始监控"""
        self.monitor.start_monitoring()
        
        assert self.monitor.training_start_time is not None
        assert self.monitor.training_end_time is None
    
    def test_stop_monitoring(self):
        """测试停止监控"""
        self.monitor.start_monitoring()
        self.monitor.stop_monitoring()
        
        assert self.monitor.training_start_time is not None
        assert self.monitor.training_end_time is not None
    
    def test_log_epoch(self):
        """测试记录epoch"""
        logs = {'loss': 0.5, 'val_loss': 0.6, 'accuracy': 0.8}
        
        self.monitor.log_epoch(1, logs)
        
        assert len(self.monitor.epoch_logs) == 1
        assert self.monitor.epoch_logs[0]['epoch'] == 1
        assert self.monitor.epoch_logs[0]['logs'] == logs
        assert 'timestamp' in self.monitor.epoch_logs[0]
    
    def test_should_stop_improvement(self):
        """测试改进时不应停止"""
        # 模拟改进的情况
        logs = {'val_loss': 0.4}  # 比初始的inf要好
        
        should_stop = self.monitor.should_stop(1, logs)
        
        assert should_stop is False
        assert self.monitor.wait == 0
        assert self.monitor.best_score == 0.4
    
    def test_should_stop_no_improvement(self):
        """测试无改进时的等待"""
        # 先设置一个好的分数
        self.monitor.best_score = 0.3
        
        # 然后提供一个更差的分数
        logs = {'val_loss': 0.5}
        
        should_stop = self.monitor.should_stop(1, logs)
        
        assert should_stop is False
        assert self.monitor.wait == 1
        assert self.monitor.best_score == 0.3  # 保持不变
    
    def test_should_stop_patience_exceeded(self):
        """测试超过耐心值时停止"""
        # 设置等待次数接近耐心值
        self.monitor.wait = 4
        self.monitor.best_score = 0.3
        
        # 提供更差的分数
        logs = {'val_loss': 0.5}
        
        should_stop = self.monitor.should_stop(5, logs)
        
        assert should_stop is True
        assert self.monitor.stopped_epoch == 5
    
    def test_should_stop_missing_metric(self):
        """测试缺失监控指标"""
        logs = {'loss': 0.5}  # 缺少val_loss
        
        should_stop = self.monitor.should_stop(1, logs)
        
        # 应该不停止，但会记录警告
        assert should_stop is False
    
    def test_get_training_summary_no_training(self):
        """测试未开始训练时的摘要"""
        summary = self.monitor.get_training_summary()
        
        assert summary['total_epochs'] == 0
        assert summary['total_time_minutes'] == 0
        assert summary['best_score'] == float('inf')
        assert summary['best_epoch'] == 0
        assert summary['stopped_early'] is False
    
    def test_get_training_summary_with_training(self):
        """测试训练后的摘要"""
        self.monitor.start_monitoring()
        
        # 模拟一些epoch
        self.monitor.log_epoch(1, {'val_loss': 0.5})
        self.monitor.log_epoch(2, {'val_loss': 0.4})
        self.monitor.best_score = 0.4
        
        self.monitor.stop_monitoring()
        
        summary = self.monitor.get_training_summary()
        
        assert summary['total_epochs'] == 2
        assert summary['total_time_minutes'] > 0
        assert summary['best_score'] == 0.4
        assert summary['best_epoch'] == 0  # 默认值
        assert summary['stopped_early'] is False
    
    def test_save_training_log(self):
        """测试保存训练日志"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_path = os.path.join(temp_dir, "training.json")
            
            # 添加一些训练数据
            self.monitor.start_monitoring()
            self.monitor.log_epoch(1, {'val_loss': 0.5})
            self.monitor.stop_monitoring()
            
            # 保存日志
            self.monitor.save_training_log(log_path)
            
            # 验证文件存在
            assert os.path.exists(log_path)
            
            # 验证内容
            with open(log_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            assert 'summary' in data
            assert 'epoch_logs' in data
            assert 'config' in data
    
    def test_load_training_log(self):
        """测试加载训练日志"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_path = os.path.join(temp_dir, "training.json")
            
            # 创建测试数据
            test_data = {
                'summary': {'total_epochs': 2},
                'epoch_logs': [{'epoch': 1, 'logs': {'val_loss': 0.5}}],
                'config': {'patience': 5}
            }
            
            with open(log_path, 'w', encoding='utf-8') as f:
                json.dump(test_data, f)
            
            # 加载日志
            loaded_data = self.monitor.load_training_log(log_path)
            
            assert loaded_data['summary']['total_epochs'] == 2
            assert len(loaded_data['epoch_logs']) == 1
            assert loaded_data['config']['patience'] == 5
    
    def test_load_training_log_not_found(self):
        """测试加载不存在的日志文件"""
        result = self.monitor.load_training_log("nonexistent.json")
        assert result is None
    
    @patch('matplotlib.pyplot.savefig')
    @patch('matplotlib.pyplot.show')
    def test_plot_training_history(self, mock_show, mock_savefig):
        """测试绘制训练历史"""
        # 添加一些训练数据
        self.monitor.log_epoch(1, {'loss': 0.6, 'val_loss': 0.5, 'accuracy': 0.7})
        self.monitor.log_epoch(2, {'loss': 0.4, 'val_loss': 0.4, 'accuracy': 0.8})
        
        # 绘制历史
        self.monitor.plot_training_history(save_path="test_plot.png")
        
        # 验证保存被调用
        mock_savefig.assert_called_once_with("test_plot.png", dpi=300, bbox_inches='tight')
    
    def test_plot_training_history_no_data(self):
        """测试无数据时的绘图"""
        # 应该不抛出异常
        self.monitor.plot_training_history()
    
    def test_generate_training_report(self):
        """测试生成训练报告"""
        # 添加一些训练数据
        self.monitor.start_monitoring()
        self.monitor.log_epoch(1, {'loss': 0.6, 'val_loss': 0.5})
        self.monitor.log_epoch(2, {'loss': 0.4, 'val_loss': 0.4})
        self.monitor.best_score = 0.4
        self.monitor.stop_monitoring()
        
        report = self.monitor.generate_training_report()
        
        assert isinstance(report, str)
        assert "训练配置" in report
        assert "训练摘要" in report
        assert "最佳性能" in report
    
    def test_reset_monitor(self):
        """测试重置监控器"""
        # 设置一些状态
        self.monitor.best_score = 0.5
        self.monitor.wait = 3
        self.monitor.stopped_epoch = 10
        self.monitor.log_epoch(1, {'val_loss': 0.5})
        
        # 重置
        self.monitor.reset()
        
        # 验证状态已重置
        assert self.monitor.best_score == float('inf')
        assert self.monitor.wait == 0
        assert self.monitor.stopped_epoch == 0
        assert len(self.monitor.epoch_logs) == 0
        assert len(self.monitor.training_history) == 0
    
    def test_early_stopping_callback_integration(self):
        """测试早停回调集成"""
        # 创建一个简单的模型用于测试
        model = keras.Sequential([
            keras.layers.Dense(10, activation='relu', input_shape=(5,)),
            keras.layers.Dense(1)
        ])
        model.compile(optimizer='adam', loss='mse')
        
        # 创建回调
        callbacks = self.monitor.create_callbacks(
            model_save_path="test_model.h5",
            checkpoint_freq=1
        )
        
        # 创建测试数据
        X = np.random.randn(100, 5)
        y = np.random.randn(100, 1)
        
        # 训练模型（应该很快停止）
        history = model.fit(
            X, y,
            validation_split=0.2,
            epochs=100,  # 设置很多epoch
            callbacks=callbacks,
            verbose=0
        )
        
        # 验证早停生效（实际训练的epoch数应该少于100）
        assert len(history.history['loss']) < 100
    
    def test_performance_monitor_callback(self):
        """测试性能监控回调"""
        # 这个测试需要访问内部类，所以我们测试其存在性
        assert hasattr(self.monitor, 'PerformanceMonitor')
        
        # 创建性能监控实例
        perf_monitor = self.monitor.PerformanceMonitor(self.monitor, checkpoint_freq=5)
        
        assert perf_monitor.monitor == self.monitor
        assert perf_monitor.checkpoint_freq == 5
        assert perf_monitor.performance_history == []


if __name__ == "__main__":
    pytest.main([__file__])
