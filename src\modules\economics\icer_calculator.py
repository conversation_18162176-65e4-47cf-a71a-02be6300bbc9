"""
增量成本效益比(ICER)计算引擎

该模块实现了健康经济学分析中的ICER计算功能，包括：
- 增量成本效益比计算
- 增量成本和增量效益计算  
- ICER统计显著性检验
- ICER置信区间计算
- ICER结果解释和分类
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Union
from enum import Enum
import numpy as np
from scipy import stats
import logging

logger = logging.getLogger(__name__)


class ICERInterpretation(Enum):
    """ICER结果解释枚举"""
    DOMINANT = "dominant"                    # 占优（成本更低，效果更好）
    COST_EFFECTIVE = "cost_effective"       # 成本效益可接受
    NOT_COST_EFFECTIVE = "not_cost_effective"  # 成本效益不可接受
    DOMINATED = "dominated"                  # 被占优（成本更高，效果更差）
    UNCERTAIN = "uncertain"                  # 不确定（位于西北或东南象限）


@dataclass
class ICERResult:
    """ICER计算结果数据类"""
    strategy_name: str
    comparator_name: str
    total_cost: float
    total_qalys: float
    comparator_cost: float
    comparator_qalys: float
    incremental_cost: float
    incremental_qalys: float
    icer: Optional[float]
    icer_interpretation: ICERInterpretation
    confidence_interval: Optional[Tuple[float, float]] = None
    probability_cost_effective: Optional[float] = None
    net_monetary_benefit: Optional[float] = None


class ICERCalculator:
    """增量成本效益比计算器"""
    
    def __init__(self, wtp_threshold: float = 150000, reference_strategy: str = "no_screening"):
        """
        初始化ICER计算器
        
        Args:
            wtp_threshold: 支付意愿阈值（元/QALY），默认150,000元
            reference_strategy: 参考策略名称，默认为"no_screening"
        """
        self.wtp_threshold = wtp_threshold
        self.reference_strategy = reference_strategy
        logger.info(f"ICER计算器初始化完成，支付意愿阈值: {wtp_threshold:,.0f}元/QALY")
    
    def calculate_icer(
        self, 
        intervention_cost: float, 
        intervention_qalys: float,
        comparator_cost: float, 
        comparator_qalys: float,
        strategy_name: str = "intervention",
        comparator_name: str = "comparator"
    ) -> ICERResult:
        """
        计算增量成本效益比
        
        Args:
            intervention_cost: 干预策略总成本
            intervention_qalys: 干预策略总QALYs
            comparator_cost: 对照策略总成本
            comparator_qalys: 对照策略总QALYs
            strategy_name: 干预策略名称
            comparator_name: 对照策略名称
            
        Returns:
            ICERResult: ICER计算结果
        """
        logger.debug(f"开始计算ICER: {strategy_name} vs {comparator_name}")
        
        # 计算增量值
        incremental_cost = intervention_cost - comparator_cost
        incremental_qalys = intervention_qalys - comparator_qalys
        
        # 计算ICER
        icer = self._calculate_icer_value(incremental_cost, incremental_qalys)
        
        # 解释ICER结果
        interpretation = self._interpret_icer(icer, incremental_cost, incremental_qalys)
        
        # 计算净货币效益
        nmb = self._calculate_net_monetary_benefit(
            incremental_cost, incremental_qalys, self.wtp_threshold
        )
        
        result = ICERResult(
            strategy_name=strategy_name,
            comparator_name=comparator_name,
            total_cost=intervention_cost,
            total_qalys=intervention_qalys,
            comparator_cost=comparator_cost,
            comparator_qalys=comparator_qalys,
            incremental_cost=incremental_cost,
            incremental_qalys=incremental_qalys,
            icer=icer,
            icer_interpretation=interpretation,
            net_monetary_benefit=nmb
        )
        
        logger.info(f"ICER计算完成: {icer:,.0f if icer else 'N/A'}元/QALY, 解释: {interpretation.value}")
        return result
    
    def _calculate_icer_value(self, incremental_cost: float, incremental_qalys: float) -> Optional[float]:
        """计算ICER数值"""
        if abs(incremental_qalys) < 1e-10:  # 避免除零错误
            return None
        return incremental_cost / incremental_qalys
    
    def _interpret_icer(
        self, 
        icer: Optional[float], 
        incremental_cost: float, 
        incremental_qalys: float
    ) -> ICERInterpretation:
        """
        解释ICER结果
        
        Args:
            icer: ICER值
            incremental_cost: 增量成本
            incremental_qalys: 增量QALYs
            
        Returns:
            ICERInterpretation: ICER解释结果
        """
        # 东南象限：成本降低，效果降低
        if incremental_cost < 0 and incremental_qalys < 0:
            return ICERInterpretation.UNCERTAIN
        
        # 西北象限：成本增加，效果降低
        if incremental_cost > 0 and incremental_qalys < 0:
            return ICERInterpretation.DOMINATED
        
        # 西南象限：成本降低，效果增加
        if incremental_cost < 0 and incremental_qalys > 0:
            return ICERInterpretation.DOMINANT
        
        # 东北象限：成本增加，效果增加
        if incremental_cost > 0 and incremental_qalys > 0:
            if icer is None:
                return ICERInterpretation.UNCERTAIN
            if icer <= self.wtp_threshold:
                return ICERInterpretation.COST_EFFECTIVE
            else:
                return ICERInterpretation.NOT_COST_EFFECTIVE
        
        # 边界情况
        if abs(incremental_cost) < 1e-10 and abs(incremental_qalys) < 1e-10:
            return ICERInterpretation.UNCERTAIN
        
        return ICERInterpretation.UNCERTAIN
    
    def _calculate_net_monetary_benefit(
        self, 
        incremental_cost: float, 
        incremental_qalys: float, 
        wtp_threshold: float
    ) -> float:
        """
        计算净货币效益
        
        Args:
            incremental_cost: 增量成本
            incremental_qalys: 增量QALYs
            wtp_threshold: 支付意愿阈值
            
        Returns:
            float: 净货币效益
        """
        return incremental_qalys * wtp_threshold - incremental_cost
    
    def calculate_icer_confidence_interval(
        self, 
        cost_samples: np.ndarray, 
        qaly_samples: np.ndarray,
        reference_cost: float, 
        reference_qalys: float,
        confidence_level: float = 0.95,
        strategy_name: str = "intervention"
    ) -> Tuple[Optional[float], Optional[float]]:
        """
        计算ICER置信区间
        
        Args:
            cost_samples: 成本样本数组
            qaly_samples: QALY样本数组
            reference_cost: 参考策略成本
            reference_qalys: 参考策略QALYs
            confidence_level: 置信水平，默认0.95
            strategy_name: 策略名称
            
        Returns:
            Tuple[Optional[float], Optional[float]]: ICER置信区间下限和上限
        """
        logger.debug(f"计算{strategy_name}的ICER置信区间，置信水平: {confidence_level}")
        
        if len(cost_samples) != len(qaly_samples):
            raise ValueError("成本样本和QALY样本数量必须相等")
        
        # 计算增量值
        incremental_costs = cost_samples - reference_cost
        incremental_qalys = qaly_samples - reference_qalys
        
        # 计算ICER样本
        icer_samples = []
        for i in range(len(incremental_costs)):
            if abs(incremental_qalys[i]) > 1e-10:  # 避免除零
                icer_samples.append(incremental_costs[i] / incremental_qalys[i])
        
        if len(icer_samples) == 0:
            logger.warning("无有效ICER样本，无法计算置信区间")
            return (None, None)
        
        # 计算置信区间
        alpha = 1 - confidence_level
        lower_percentile = (alpha / 2) * 100
        upper_percentile = (1 - alpha / 2) * 100
        
        ci_lower = np.percentile(icer_samples, lower_percentile)
        ci_upper = np.percentile(icer_samples, upper_percentile)
        
        logger.info(f"ICER {confidence_level*100}%置信区间: [{ci_lower:,.0f}, {ci_upper:,.0f}]元/QALY")
        return (ci_lower, ci_upper)
    
    def calculate_probability_cost_effective(
        self, 
        cost_samples: np.ndarray, 
        qaly_samples: np.ndarray,
        reference_cost: float, 
        reference_qalys: float,
        wtp_threshold: Optional[float] = None
    ) -> float:
        """
        计算策略具有成本效益的概率
        
        Args:
            cost_samples: 成本样本数组
            qaly_samples: QALY样本数组
            reference_cost: 参考策略成本
            reference_qalys: 参考策略QALYs
            wtp_threshold: 支付意愿阈值，默认使用实例阈值
            
        Returns:
            float: 具有成本效益的概率 (0-1)
        """
        if wtp_threshold is None:
            wtp_threshold = self.wtp_threshold
        
        # 计算净货币效益样本
        incremental_costs = cost_samples - reference_cost
        incremental_qalys = qaly_samples - reference_qalys
        nmb_samples = incremental_qalys * wtp_threshold - incremental_costs
        
        # 计算NMB > 0的概率
        probability = np.mean(nmb_samples > 0)
        
        logger.info(f"在阈值{wtp_threshold:,.0f}元/QALY下，策略具有成本效益的概率: {probability:.3f}")
        return probability
    
    def perform_statistical_test(
        self, 
        cost_samples: np.ndarray, 
        qaly_samples: np.ndarray,
        reference_cost: float, 
        reference_qalys: float,
        alpha: float = 0.05
    ) -> Dict[str, Union[float, bool]]:
        """
        对ICER进行统计显著性检验
        
        Args:
            cost_samples: 成本样本数组
            qaly_samples: QALY样本数组
            reference_cost: 参考策略成本
            reference_qalys: 参考策略QALYs
            alpha: 显著性水平，默认0.05
            
        Returns:
            Dict: 统计检验结果
        """
        logger.debug("开始ICER统计显著性检验")
        
        # 计算增量值
        incremental_costs = cost_samples - reference_cost
        incremental_qalys = qaly_samples - reference_qalys
        
        # 对增量成本进行t检验
        cost_t_stat, cost_p_value = stats.ttest_1samp(incremental_costs, 0)
        cost_significant = cost_p_value < alpha
        
        # 对增量QALYs进行t检验
        qaly_t_stat, qaly_p_value = stats.ttest_1samp(incremental_qalys, 0)
        qaly_significant = qaly_p_value < alpha
        
        result = {
            'cost_t_statistic': cost_t_stat,
            'cost_p_value': cost_p_value,
            'cost_significant': cost_significant,
            'qaly_t_statistic': qaly_t_stat,
            'qaly_p_value': qaly_p_value,
            'qaly_significant': qaly_significant,
            'alpha': alpha
        }
        
        logger.info(f"统计检验完成 - 成本显著性: {cost_significant}, QALYs显著性: {qaly_significant}")
        return result
    
    def batch_calculate_icer(
        self, 
        strategies: List[Dict[str, Union[str, float]]], 
        reference_strategy_name: Optional[str] = None
    ) -> List[ICERResult]:
        """
        批量计算多个策略的ICER
        
        Args:
            strategies: 策略列表，每个策略包含name, cost, qalys字段
            reference_strategy_name: 参考策略名称，默认使用第一个策略
            
        Returns:
            List[ICERResult]: ICER计算结果列表
        """
        if len(strategies) < 2:
            raise ValueError("至少需要2个策略进行比较")
        
        # 确定参考策略
        if reference_strategy_name is None:
            reference_strategy = strategies[0]
        else:
            reference_strategy = next(
                (s for s in strategies if s['name'] == reference_strategy_name), 
                strategies[0]
            )
        
        results = []
        for strategy in strategies:
            if strategy['name'] != reference_strategy['name']:
                result = self.calculate_icer(
                    intervention_cost=strategy['cost'],
                    intervention_qalys=strategy['qalys'],
                    comparator_cost=reference_strategy['cost'],
                    comparator_qalys=reference_strategy['qalys'],
                    strategy_name=strategy['name'],
                    comparator_name=reference_strategy['name']
                )
                results.append(result)
        
        logger.info(f"批量ICER计算完成，共计算{len(results)}个策略对比")
        return results
    
    def update_wtp_threshold(self, new_threshold: float) -> None:
        """
        更新支付意愿阈值
        
        Args:
            new_threshold: 新的支付意愿阈值
        """
        old_threshold = self.wtp_threshold
        self.wtp_threshold = new_threshold
        logger.info(f"支付意愿阈值已更新: {old_threshold:,.0f} -> {new_threshold:,.0f}元/QALY")