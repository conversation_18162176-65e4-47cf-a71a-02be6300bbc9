"""
成本效益阈值分析系统

该模块实现了健康经济学分析中的阈值分析功能，包括：
- 支付意愿阈值配置和管理
- 阈值敏感性分析
- 阈值变化对决策的影响分析
- 国际阈值标准比较
- 净货币效益计算
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Union
from enum import Enum
import numpy as np
import pandas as pd
from scipy import stats
import logging

logger = logging.getLogger(__name__)


class ThresholdStandard(Enum):
    """国际阈值标准枚举"""
    WHO_1GDP = "who_1gdp"           # WHO标准：1倍人均GDP
    WHO_3GDP = "who_3gdp"           # WHO标准：3倍人均GDP
    NICE_UK = "nice_uk"             # 英国NICE标准：20,000-30,000英镑/QALY
    CHINA_OFFICIAL = "china_official"  # 中国官方标准：3倍人均GDP
    CUSTOM = "custom"               # 自定义阈值


@dataclass
class ThresholdConfig:
    """阈值配置数据类"""
    name: str
    value: float
    currency: str = "CNY"
    standard: ThresholdStandard = ThresholdStandard.CUSTOM
    description: str = ""
    year: int = 2024


@dataclass
class ThresholdAnalysisResult:
    """阈值分析结果数据类"""
    strategy_name: str
    threshold_range: List[float]
    probability_cost_effective: List[float]
    net_monetary_benefit: List[float]
    optimal_threshold: Optional[float] = None
    decision_uncertainty: Optional[float] = None


class ThresholdAnalyzer:
    """成本效益阈值分析器"""
    
    def __init__(self, base_threshold: float = 150000):
        """
        初始化阈值分析器
        
        Args:
            base_threshold: 基础支付意愿阈值（元/QALY）
        """
        self.base_threshold = base_threshold
        self.threshold_configs = self._initialize_standard_thresholds()
        logger.info(f"阈值分析器初始化完成，基础阈值: {base_threshold:,.0f}元/QALY")
    
    def _initialize_standard_thresholds(self) -> Dict[str, ThresholdConfig]:
        """初始化标准阈值配置"""
        china_gdp_per_capita = 85000  # 2024年中国人均GDP估算值
        
        configs = {
            "who_1gdp": ThresholdConfig(
                name="WHO 1倍GDP标准",
                value=china_gdp_per_capita,
                standard=ThresholdStandard.WHO_1GDP,
                description="世界卫生组织推荐的1倍人均GDP阈值"
            ),
            "who_3gdp": ThresholdConfig(
                name="WHO 3倍GDP标准", 
                value=china_gdp_per_capita * 3,
                standard=ThresholdStandard.WHO_3GDP,
                description="世界卫生组织推荐的3倍人均GDP阈值"
            ),
            "china_official": ThresholdConfig(
                name="中国官方标准",
                value=china_gdp_per_capita * 3,
                standard=ThresholdStandard.CHINA_OFFICIAL,
                description="中国卫生技术评估指南推荐的3倍人均GDP阈值"
            ),
            "nice_uk": ThresholdConfig(
                name="英国NICE标准",
                value=200000,  # 约合人民币20万元
                standard=ThresholdStandard.NICE_UK,
                description="英国国家卫生与临床优化研究所标准（转换为人民币）"
            )
        }
        
        return configs
    
    def add_custom_threshold(
        self, 
        name: str, 
        value: float, 
        description: str = ""
    ) -> None:
        """
        添加自定义阈值配置
        
        Args:
            name: 阈值名称
            value: 阈值数值
            description: 阈值描述
        """
        config = ThresholdConfig(
            name=name,
            value=value,
            standard=ThresholdStandard.CUSTOM,
            description=description
        )
        self.threshold_configs[name] = config
        logger.info(f"添加自定义阈值: {name} = {value:,.0f}元/QALY")
    
    def perform_threshold_sensitivity_analysis(
        self,
        cost_samples: np.ndarray,
        qaly_samples: np.ndarray,
        reference_cost: float,
        reference_qalys: float,
        threshold_range: Optional[Tuple[float, float]] = None,
        n_points: int = 50,
        strategy_name: str = "intervention"
    ) -> ThresholdAnalysisResult:
        """
        执行阈值敏感性分析
        
        Args:
            cost_samples: 成本样本数组
            qaly_samples: QALY样本数组
            reference_cost: 参考策略成本
            reference_qalys: 参考策略QALYs
            threshold_range: 阈值范围，默认为(0, 500000)
            n_points: 分析点数
            strategy_name: 策略名称
            
        Returns:
            ThresholdAnalysisResult: 阈值分析结果
        """
        logger.debug(f"开始{strategy_name}的阈值敏感性分析")
        
        if threshold_range is None:
            threshold_range = (0, 500000)
        
        # 生成阈值范围
        thresholds = np.linspace(threshold_range[0], threshold_range[1], n_points)
        
        # 计算增量值
        incremental_costs = cost_samples - reference_cost
        incremental_qalys = qaly_samples - reference_qalys
        
        probabilities = []
        nmb_values = []
        
        for threshold in thresholds:
            # 计算净货币效益
            nmb_samples = incremental_qalys * threshold - incremental_costs
            
            # 计算成本效益概率
            prob_cost_effective = np.mean(nmb_samples > 0)
            probabilities.append(prob_cost_effective)
            
            # 计算平均净货币效益
            mean_nmb = np.mean(nmb_samples)
            nmb_values.append(mean_nmb)
        
        # 找到最优阈值（概率最接近0.5的点）
        optimal_idx = np.argmin(np.abs(np.array(probabilities) - 0.5))
        optimal_threshold = thresholds[optimal_idx]
        
        # 计算决策不确定性（概率在0.2-0.8之间的阈值范围）
        uncertain_indices = np.where(
            (np.array(probabilities) >= 0.2) & (np.array(probabilities) <= 0.8)
        )[0]
        
        if len(uncertain_indices) > 0:
            decision_uncertainty = thresholds[uncertain_indices[-1]] - thresholds[uncertain_indices[0]]
        else:
            decision_uncertainty = 0.0
        
        result = ThresholdAnalysisResult(
            strategy_name=strategy_name,
            threshold_range=thresholds.tolist(),
            probability_cost_effective=probabilities,
            net_monetary_benefit=nmb_values,
            optimal_threshold=optimal_threshold,
            decision_uncertainty=decision_uncertainty
        )
        
        logger.info(f"阈值敏感性分析完成，最优阈值: {optimal_threshold:,.0f}元/QALY")
        return result
    
    def compare_with_international_standards(
        self,
        cost_samples: np.ndarray,
        qaly_samples: np.ndarray,
        reference_cost: float,
        reference_qalys: float,
        strategy_name: str = "intervention"
    ) -> Dict[str, Dict[str, Union[float, bool]]]:
        """
        与国际标准阈值进行比较
        
        Args:
            cost_samples: 成本样本数组
            qaly_samples: QALY样本数组
            reference_cost: 参考策略成本
            reference_qalys: 参考策略QALYs
            strategy_name: 策略名称
            
        Returns:
            Dict: 各标准下的比较结果
        """
        logger.debug(f"开始{strategy_name}与国际标准的比较分析")
        
        # 计算增量值
        incremental_costs = cost_samples - reference_cost
        incremental_qalys = qaly_samples - reference_qalys
        
        comparison_results = {}
        
        for config_name, config in self.threshold_configs.items():
            # 计算净货币效益
            nmb_samples = incremental_qalys * config.value - incremental_costs
            
            # 计算统计指标
            prob_cost_effective = np.mean(nmb_samples > 0)
            mean_nmb = np.mean(nmb_samples)
            std_nmb = np.std(nmb_samples)
            
            # 计算置信区间
            ci_lower, ci_upper = np.percentile(nmb_samples, [2.5, 97.5])
            
            comparison_results[config_name] = {
                'threshold_name': config.name,
                'threshold_value': config.value,
                'probability_cost_effective': prob_cost_effective,
                'mean_nmb': mean_nmb,
                'std_nmb': std_nmb,
                'nmb_ci_lower': ci_lower,
                'nmb_ci_upper': ci_upper,
                'is_cost_effective': prob_cost_effective > 0.5,
                'description': config.description
            }
        
        logger.info(f"国际标准比较完成，共比较{len(comparison_results)}个标准")
        return comparison_results
    
    def calculate_threshold_impact_on_decision(
        self,
        strategies_data: List[Dict[str, Union[str, np.ndarray, float]]],
        threshold_range: Tuple[float, float] = (0, 500000),
        n_points: int = 100
    ) -> Dict[str, List[float]]:
        """
        计算阈值变化对决策的影响
        
        Args:
            strategies_data: 策略数据列表，包含name, cost_samples, qaly_samples
            threshold_range: 阈值范围
            n_points: 分析点数
            
        Returns:
            Dict: 各策略在不同阈值下成为最优选择的概率
        """
        logger.debug("开始计算阈值变化对决策的影响")
        
        thresholds = np.linspace(threshold_range[0], threshold_range[1], n_points)
        strategy_names = [s['name'] for s in strategies_data]
        
        # 初始化结果字典
        decision_probabilities = {name: [] for name in strategy_names}
        decision_probabilities['thresholds'] = thresholds.tolist()
        
        for threshold in thresholds:
            # 计算每个策略的净货币效益
            strategy_nmb = {}
            n_samples = len(strategies_data[0]['cost_samples'])
            
            for strategy in strategies_data:
                nmb_samples = strategy['qaly_samples'] * threshold - strategy['cost_samples']
                strategy_nmb[strategy['name']] = nmb_samples
            
            # 计算每个策略成为最优选择的概率
            for strategy_name in strategy_names:
                optimal_count = 0
                
                for i in range(n_samples):
                    # 检查该策略在第i次模拟中是否最优
                    strategy_nmb_i = strategy_nmb[strategy_name][i]
                    is_optimal = all(
                        strategy_nmb_i >= strategy_nmb[other_name][i]
                        for other_name in strategy_names
                    )
                    
                    if is_optimal:
                        optimal_count += 1
                
                probability = optimal_count / n_samples
                decision_probabilities[strategy_name].append(probability)
        
        logger.info("阈值对决策影响分析完成")
        return decision_probabilities
    
    def find_cost_effectiveness_frontier(
        self,
        strategies_data: List[Dict[str, Union[str, float]]],
        sort_by_cost: bool = True
    ) -> List[Dict[str, Union[str, float, bool]]]:
        """
        找到成本效益前沿
        
        Args:
            strategies_data: 策略数据列表，包含name, cost, qalys
            sort_by_cost: 是否按成本排序
            
        Returns:
            List: 前沿策略列表
        """
        logger.debug("开始计算成本效益前沿")
        
        # 复制数据并排序
        strategies = strategies_data.copy()
        if sort_by_cost:
            strategies.sort(key=lambda x: x['cost'])
        else:
            strategies.sort(key=lambda x: x['qalys'])
        
        frontier_strategies = []
        max_qalys = -float('inf')
        
        for strategy in strategies:
            if strategy['qalys'] > max_qalys:
                # 该策略在前沿上
                strategy_copy = strategy.copy()
                strategy_copy['on_frontier'] = True
                frontier_strategies.append(strategy_copy)
                max_qalys = strategy['qalys']
            else:
                # 该策略被占优
                strategy_copy = strategy.copy()
                strategy_copy['on_frontier'] = False
                frontier_strategies.append(strategy_copy)
        
        logger.info(f"成本效益前沿计算完成，前沿策略数: {sum(1 for s in frontier_strategies if s['on_frontier'])}")
        return frontier_strategies
    
    def generate_threshold_analysis_report(
        self,
        analysis_results: List[ThresholdAnalysisResult],
        international_comparison: Dict[str, Dict],
        decision_impact: Dict[str, List[float]]
    ) -> Dict[str, Union[str, Dict, List]]:
        """
        生成阈值分析报告
        
        Args:
            analysis_results: 阈值分析结果列表
            international_comparison: 国际标准比较结果
            decision_impact: 决策影响分析结果
            
        Returns:
            Dict: 阈值分析报告
        """
        logger.debug("开始生成阈值分析报告")
        
        # 生成执行摘要
        summary = self._generate_threshold_summary(analysis_results, international_comparison)
        
        # 生成详细结果
        detailed_results = {
            'sensitivity_analysis': [
                {
                    'strategy_name': result.strategy_name,
                    'optimal_threshold': result.optimal_threshold,
                    'decision_uncertainty': result.decision_uncertainty,
                    'threshold_range': result.threshold_range,
                    'probabilities': result.probability_cost_effective
                }
                for result in analysis_results
            ],
            'international_standards': international_comparison,
            'decision_impact': decision_impact
        }
        
        # 生成建议
        recommendations = self._generate_threshold_recommendations(
            analysis_results, international_comparison
        )
        
        report = {
            'executive_summary': summary,
            'detailed_results': detailed_results,
            'recommendations': recommendations,
            'methodology': {
                'description': '阈值敏感性分析采用净货币效益方法，通过改变支付意愿阈值来评估策略的成本效益性',
                'threshold_standards': [
                    {
                        'name': config.name,
                        'value': config.value,
                        'description': config.description
                    }
                    for config in self.threshold_configs.values()
                ]
            }
        }
        
        logger.info("阈值分析报告生成完成")
        return report
    
    def _generate_threshold_summary(
        self,
        analysis_results: List[ThresholdAnalysisResult],
        international_comparison: Dict[str, Dict]
    ) -> str:
        """生成阈值分析摘要"""
        
        if not analysis_results:
            return "未进行阈值敏感性分析。"
        
        # 找到决策不确定性最小的策略
        best_strategy = min(analysis_results, key=lambda x: x.decision_uncertainty or float('inf'))
        
        # 统计在各国际标准下具有成本效益的策略数
        cost_effective_count = sum(
            1 for comp in international_comparison.values()
            if comp.get('is_cost_effective', False)
        )
        
        summary = f"""
        阈值敏感性分析摘要：
        
        - 分析了{len(analysis_results)}个策略的阈值敏感性
        - 决策不确定性最小的策略：{best_strategy.strategy_name}
        - 最优阈值：{best_strategy.optimal_threshold:,.0f}元/QALY
        - 决策不确定性范围：{best_strategy.decision_uncertainty:,.0f}元/QALY
        
        国际标准比较：
        - 在{len(international_comparison)}个国际标准中，有{cost_effective_count}个标准下策略具有成本效益
        
        建议：基于阈值敏感性分析，推荐在支付意愿阈值为{best_strategy.optimal_threshold:,.0f}元/QALY时采用{best_strategy.strategy_name}策略。
        """
        
        return summary.strip()
    
    def _generate_threshold_recommendations(
        self,
        analysis_results: List[ThresholdAnalysisResult],
        international_comparison: Dict[str, Dict]
    ) -> List[str]:
        """生成阈值分析建议"""
        
        recommendations = []
        
        # 基于决策不确定性的建议
        if analysis_results:
            high_uncertainty_strategies = [
                r for r in analysis_results 
                if r.decision_uncertainty and r.decision_uncertainty > 100000
            ]
            
            if high_uncertainty_strategies:
                recommendations.append(
                    f"以下策略存在较高的决策不确定性，建议进行进一步的价值信息研究：" +
                    "、".join([s.strategy_name for s in high_uncertainty_strategies])
                )
        
        # 基于国际标准的建议
        consistent_standards = [
            name for name, comp in international_comparison.items()
            if comp.get('is_cost_effective', False)
        ]
        
        if len(consistent_standards) >= 3:
            recommendations.append(
                f"该策略在多个国际标准下均具有成本效益，包括：" +
                "、".join([international_comparison[name]['threshold_name'] for name in consistent_standards[:3]])
            )
        elif len(consistent_standards) == 0:
            recommendations.append("该策略在所有国际标准下均不具有成本效益，建议重新评估策略设计")
        
        # 阈值政策建议
        china_official = international_comparison.get('china_official', {})
        if china_official.get('is_cost_effective', False):
            recommendations.append("该策略符合中国官方成本效益评估标准，建议纳入医保考虑")
        
        return recommendations