"""
参数抽样器单元测试
"""

import unittest
import numpy as np
import tempfile
import os
from unittest.mock import patch, MagicMock

from src.calibration.parameter_sampler import (
    ParameterDefinition, SamplingConfig, SamplingResult,
    LatinHypercubeSampler, BatchSampler
)


class TestParameterDefinition(unittest.TestCase):
    """参数定义测试类"""
    
    def test_parameter_definition_creation(self):
        """测试参数定义创建"""
        param = ParameterDefinition(
            name="test_param",
            min_value=0.0,
            max_value=1.0,
            distribution="uniform",
            description="测试参数"
        )
        
        self.assertEqual(param.name, "test_param")
        self.assertEqual(param.min_value, 0.0)
        self.assertEqual(param.max_value, 1.0)
        self.assertEqual(param.distribution, "uniform")
        self.assertEqual(param.description, "测试参数")
        self.assertIsNone(param.constraints)
    
    def test_parameter_definition_with_constraints(self):
        """测试带约束的参数定义"""
        param = ParameterDefinition(
            name="constrained_param",
            min_value=0.0,
            max_value=10.0,
            constraints=["x > 0", "x < 5"]
        )
        
        self.assertEqual(len(param.constraints), 2)
        self.assertIn("x > 0", param.constraints)


class TestSamplingConfig(unittest.TestCase):
    """抽样配置测试类"""
    
    def setUp(self):
        """设置测试数据"""
        self.params = [
            ParameterDefinition("param1", 0.0, 1.0),
            ParameterDefinition("param2", -1.0, 1.0),
            ParameterDefinition("param3", 0.1, 10.0, distribution="lognormal")
        ]
    
    def test_sampling_config_creation(self):
        """测试抽样配置创建"""
        config = SamplingConfig(
            parameters=self.params,
            n_samples=1000,
            random_seed=42,
            sampling_method="lhs",
            optimization_criterion="maximin"
        )
        
        self.assertEqual(len(config.parameters), 3)
        self.assertEqual(config.n_samples, 1000)
        self.assertEqual(config.random_seed, 42)
        self.assertEqual(config.sampling_method, "lhs")
        self.assertEqual(config.optimization_criterion, "maximin")


class TestLatinHypercubeSampler(unittest.TestCase):
    """拉丁超立方抽样器测试类"""
    
    def setUp(self):
        """设置测试数据"""
        self.params = [
            ParameterDefinition("param1", 0.0, 1.0, distribution="uniform"),
            ParameterDefinition("param2", -2.0, 2.0, distribution="uniform"),
            ParameterDefinition("param3", 0.1, 10.0, distribution="normal")
        ]
        
        self.config = SamplingConfig(
            parameters=self.params,
            n_samples=100,
            random_seed=42,
            sampling_method="lhs",
            optimization_criterion="maximin"
        )
        
        self.sampler = LatinHypercubeSampler(self.config)
    
    def test_sampler_initialization(self):
        """测试抽样器初始化"""
        self.assertEqual(self.sampler.n_dimensions, 3)
        self.assertEqual(len(self.sampler.parameters), 3)
        self.assertIn("param1", self.sampler.parameters)
        self.assertEqual(self.sampler.config.random_seed, 42)
    
    def test_generate_samples_basic(self):
        """测试基本抽样生成"""
        result = self.sampler.generate_samples()
        
        # 检查结果类型和结构
        self.assertIsInstance(result, SamplingResult)
        self.assertEqual(result.samples.shape, (100, 3))
        self.assertEqual(len(result.parameter_names), 3)
        self.assertEqual(result.parameter_names, ["param1", "param2", "param3"])
        
        # 检查样本范围
        self.assertTrue(np.all(result.samples[:, 0] >= 0.0))
        self.assertTrue(np.all(result.samples[:, 0] <= 1.0))
        self.assertTrue(np.all(result.samples[:, 1] >= -2.0))
        self.assertTrue(np.all(result.samples[:, 1] <= 2.0))
        
        # 检查质量指标
        self.assertIn('min_distance', result.quality_metrics)
        self.assertIn('max_correlation', result.quality_metrics)
        self.assertIn('min_coverage', result.quality_metrics)
        
        # 检查哈希签名
        self.assertIsInstance(result.hash_signature, str)
        self.assertEqual(len(result.hash_signature), 64)  # SHA256长度
    
    def test_generate_samples_reproducibility(self):
        """测试抽样可重现性"""
        result1 = self.sampler.generate_samples()
        
        # 创建相同配置的新抽样器
        sampler2 = LatinHypercubeSampler(self.config)
        result2 = sampler2.generate_samples()
        
        # 检查结果一致性
        np.testing.assert_array_equal(result1.samples, result2.samples)
        self.assertEqual(result1.hash_signature, result2.hash_signature)
    
    def test_scale_samples_uniform(self):
        """测试均匀分布样本缩放"""
        unit_samples = np.array([[0.0, 0.5, 1.0], [0.25, 0.75, 0.1]])
        scaled_samples = self.sampler._scale_samples(unit_samples)

        # 检查第一个参数（0-1范围）
        np.testing.assert_array_almost_equal(scaled_samples[:, 0], [0.0, 0.25])

        # 检查第二个参数（-2到2范围）- 修正期望值
        # 0.5 -> -2 + 0.5 * 4 = 0.0, 0.75 -> -2 + 0.75 * 4 = 1.0
        np.testing.assert_array_almost_equal(scaled_samples[:, 1], [0.0, 1.0])
    
    def test_scale_samples_normal(self):
        """测试正态分布样本缩放"""
        # 创建正态分布参数
        normal_param = ParameterDefinition("normal_param", 0.0, 10.0, distribution="normal")
        config = SamplingConfig([normal_param], 10, 42)
        sampler = LatinHypercubeSampler(config)
        
        unit_samples = np.array([[0.5], [0.1], [0.9]])
        scaled_samples = sampler._scale_samples(unit_samples)
        
        # 检查缩放后的样本不等于原始样本（因为使用了正态分布变换）
        self.assertFalse(np.array_equal(scaled_samples, unit_samples))
        self.assertEqual(scaled_samples.shape, (3, 1))
    
    def test_scale_samples_lognormal(self):
        """测试对数正态分布样本缩放"""
        # 创建对数正态分布参数
        lognormal_param = ParameterDefinition("lognormal_param", 0.1, 10.0, distribution="lognormal")
        config = SamplingConfig([lognormal_param], 10, 42)
        sampler = LatinHypercubeSampler(config)
        
        unit_samples = np.array([[0.5], [0.1], [0.9]])
        scaled_samples = sampler._scale_samples(unit_samples)
        
        # 检查所有值都为正（对数正态分布特性）
        self.assertTrue(np.all(scaled_samples > 0))
        self.assertEqual(scaled_samples.shape, (3, 1))
    
    def test_calculate_quality_metrics(self):
        """测试质量指标计算"""
        # 创建简单的测试样本
        samples = np.array([
            [0.1, 0.2],
            [0.3, 0.4],
            [0.5, 0.6],
            [0.7, 0.8],
            [0.9, 1.0]
        ])
        
        metrics = self.sampler._calculate_quality_metrics(samples)
        
        # 检查指标存在性
        required_metrics = ['min_distance', 'mean_distance', 'max_correlation', 
                          'mean_correlation', 'min_coverage', 'mean_coverage']
        for metric in required_metrics:
            self.assertIn(metric, metrics)
            self.assertIsInstance(metrics[metric], float)
        
        # 检查指标合理性
        self.assertGreater(metrics['min_distance'], 0)
        self.assertGreater(metrics['mean_distance'], metrics['min_distance'])
        self.assertGreaterEqual(metrics['max_correlation'], 0)
        self.assertLessEqual(metrics['max_correlation'], 1)
    
    def test_generate_hash_signature(self):
        """测试哈希签名生成"""
        samples = np.random.random((10, 3))
        hash1 = self.sampler._generate_hash_signature(samples)
        hash2 = self.sampler._generate_hash_signature(samples)
        
        # 相同样本应产生相同哈希
        self.assertEqual(hash1, hash2)
        
        # 不同样本应产生不同哈希
        different_samples = np.random.random((10, 3))
        hash3 = self.sampler._generate_hash_signature(different_samples)
        self.assertNotEqual(hash1, hash3)
        
        # 检查哈希格式
        self.assertEqual(len(hash1), 64)  # SHA256长度
        self.assertTrue(all(c in '0123456789abcdef' for c in hash1))
    
    def test_validate_orthogonality(self):
        """测试正交性验证"""
        result = self.sampler.generate_samples()
        orthogonality_result = self.sampler.validate_orthogonality(result.samples)
        
        # 检查返回结构
        self.assertIn('orthogonality_score', orthogonality_result)
        self.assertIn('is_orthogonal', orthogonality_result)
        self.assertIn('dimension_results', orthogonality_result)
        
        # 检查分数范围
        score = orthogonality_result['orthogonality_score']
        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 1.0)
        
        # 检查维度结果
        dim_results = orthogonality_result['dimension_results']
        self.assertEqual(len(dim_results), 3)  # 3个参数
        
        for dim_key, dim_result in dim_results.items():
            self.assertIn('layer_counts', dim_result)
            self.assertIn('is_stratified', dim_result)
            self.assertIn('max_deviation', dim_result)
    
    def test_small_sample_size(self):
        """测试小样本量抽样"""
        small_config = SamplingConfig(
            parameters=self.params[:2],  # 只用前两个参数
            n_samples=5,
            random_seed=42
        )
        small_sampler = LatinHypercubeSampler(small_config)
        result = small_sampler.generate_samples()
        
        self.assertEqual(result.samples.shape, (5, 2))
        self.assertGreater(result.generation_time, 0)
    
    def test_large_sample_size(self):
        """测试大样本量抽样"""
        large_config = SamplingConfig(
            parameters=self.params[:2],
            n_samples=5000,
            random_seed=42
        )
        large_sampler = LatinHypercubeSampler(large_config)
        result = large_sampler.generate_samples()
        
        self.assertEqual(result.samples.shape, (5000, 2))
        self.assertGreater(result.generation_time, 0)
        
        # 大样本应该有更好的质量指标 - 调整阈值为更合理的值
        self.assertGreater(result.quality_metrics['min_coverage'], 0.3)
        # 检查其他质量指标
        self.assertGreater(result.quality_metrics['min_distance'], 0.0)
        self.assertLess(result.quality_metrics['max_correlation'], 0.5)


class TestBatchSampler(unittest.TestCase):
    """批量抽样器测试类"""
    
    def setUp(self):
        """设置测试数据"""
        self.params = [
            ParameterDefinition("param1", 0.0, 1.0),
            ParameterDefinition("param2", -1.0, 1.0)
        ]
        
        self.config = SamplingConfig(
            parameters=self.params,
            n_samples=100,
            random_seed=42
        )
        
        self.base_sampler = LatinHypercubeSampler(self.config)
        self.batch_sampler = BatchSampler(self.base_sampler, batch_size=50)
    
    def test_batch_sampler_initialization(self):
        """测试批量抽样器初始化"""
        self.assertEqual(self.batch_sampler.batch_size, 50)
        self.assertIs(self.batch_sampler.base_sampler, self.base_sampler)
    
    def test_generate_large_samples_basic(self):
        """测试基本大规模抽样"""
        result = self.batch_sampler.generate_large_samples(200)
        
        self.assertEqual(result.samples.shape, (200, 2))
        self.assertEqual(result.config.n_samples, 200)
        self.assertGreater(result.generation_time, 0)
        self.assertIsInstance(result.hash_signature, str)
    
    def test_generate_large_samples_with_callback(self):
        """测试带进度回调的大规模抽样"""
        progress_calls = []
        
        def progress_callback(progress, current_batch, total_batches):
            progress_calls.append((progress, current_batch, total_batches))
        
        result = self.batch_sampler.generate_large_samples(
            150, progress_callback=progress_callback
        )
        
        self.assertEqual(result.samples.shape, (150, 2))
        self.assertGreater(len(progress_calls), 0)
        
        # 检查进度回调参数
        final_call = progress_calls[-1]
        self.assertEqual(final_call[0], 1.0)  # 最终进度应为100%
        self.assertEqual(final_call[1], final_call[2])  # 当前批次应等于总批次
    
    def test_batch_size_handling(self):
        """测试批次大小处理"""
        # 测试样本数不能被批次大小整除的情况
        result = self.batch_sampler.generate_large_samples(175)  # 175 = 3*50 + 25
        
        self.assertEqual(result.samples.shape, (175, 2))
        
        # 测试样本数小于批次大小的情况
        result_small = self.batch_sampler.generate_large_samples(25)
        self.assertEqual(result_small.samples.shape, (25, 2))
    
    def test_different_random_seeds(self):
        """测试不同随机种子的批次"""
        result1 = self.batch_sampler.generate_large_samples(100)
        
        # 创建不同种子的抽样器
        different_config = SamplingConfig(
            parameters=self.params,
            n_samples=100,
            random_seed=123  # 不同的种子
        )
        different_base_sampler = LatinHypercubeSampler(different_config)
        different_batch_sampler = BatchSampler(different_base_sampler, batch_size=50)
        
        result2 = different_batch_sampler.generate_large_samples(100)
        
        # 不同种子应产生不同结果
        self.assertFalse(np.array_equal(result1.samples, result2.samples))
        self.assertNotEqual(result1.hash_signature, result2.hash_signature)


class TestSamplingResult(unittest.TestCase):
    """抽样结果测试类"""
    
    def test_sampling_result_creation(self):
        """测试抽样结果创建"""
        samples = np.random.random((100, 3))
        parameter_names = ["param1", "param2", "param3"]
        config = SamplingConfig(
            parameters=[ParameterDefinition(name, 0, 1) for name in parameter_names],
            n_samples=100,
            random_seed=42
        )
        quality_metrics = {
            'min_distance': 0.05,
            'max_correlation': 0.02,
            'min_coverage': 0.95
        }
        
        result = SamplingResult(
            samples=samples,
            parameter_names=parameter_names,
            config=config,
            quality_metrics=quality_metrics,
            generation_time=1.5,
            hash_signature="test_hash"
        )
        
        self.assertEqual(result.samples.shape, (100, 3))
        self.assertEqual(len(result.parameter_names), 3)
        self.assertEqual(result.config.n_samples, 100)
        self.assertEqual(result.generation_time, 1.5)
        self.assertEqual(result.hash_signature, "test_hash")


class TestEdgeCases(unittest.TestCase):
    """边界情况测试类"""
    
    def test_single_parameter(self):
        """测试单参数抽样"""
        param = ParameterDefinition("single_param", 0.0, 1.0)
        config = SamplingConfig([param], 50, 42)
        sampler = LatinHypercubeSampler(config)
        
        result = sampler.generate_samples()
        
        self.assertEqual(result.samples.shape, (50, 1))
        self.assertEqual(len(result.parameter_names), 1)
    
    def test_zero_range_parameter(self):
        """测试零范围参数"""
        param = ParameterDefinition("zero_range", 5.0, 5.0)  # min == max
        config = SamplingConfig([param], 10, 42)
        sampler = LatinHypercubeSampler(config)
        
        result = sampler.generate_samples()
        
        # 所有样本应该都等于5.0
        np.testing.assert_array_almost_equal(result.samples[:, 0], 5.0)
    
    def test_very_small_sample_size(self):
        """测试极小样本量"""
        params = [ParameterDefinition("param1", 0, 1)]
        config = SamplingConfig(params, 1, 42)  # 只有1个样本
        sampler = LatinHypercubeSampler(config)
        
        result = sampler.generate_samples()
        
        self.assertEqual(result.samples.shape, (1, 1))
        self.assertGreater(result.generation_time, 0)
    
    def test_invalid_distribution(self):
        """测试无效分布类型"""
        param = ParameterDefinition("invalid_param", 0, 1, distribution="invalid_dist")
        config = SamplingConfig([param], 10, 42)
        sampler = LatinHypercubeSampler(config)
        
        # 应该默认使用均匀分布或抛出异常
        try:
            result = sampler.generate_samples()
            # 如果没有抛出异常，检查结果
            self.assertEqual(result.samples.shape, (10, 1))
        except (ValueError, KeyError):
            # 如果抛出异常，这也是可接受的行为
            pass


if __name__ == '__main__':
    unittest.main()