"""
性能优化模块
实现抽样算法的各种性能优化技术
"""

import numpy as np
import time
import gc
import psutil
import threading
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp
from functools import partial
import numba
from numba import jit, prange
import logging

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    execution_time: float
    memory_usage_mb: float
    cpu_usage_percent: float
    cache_hit_rate: float
    throughput_samples_per_second: float
    memory_efficiency: float  # 样本数/内存使用量

class OptimizedLHSSampler:
    """优化的拉丁超立方抽样器"""
    
    def __init__(self, n_dimensions: int, optimization_method: str = "vectorized"):
        self.n_dimensions = n_dimensions
        self.optimization_method = optimization_method
        self.cache = {}
        self.cache_hits = 0
        self.cache_misses = 0
        
    @jit(nopython=True, parallel=True)
    def _vectorized_lhs_core(self, n_samples: int, n_dimensions: int, seed: int) -> np.ndarray:
        """使用Numba优化的核心LHS算法"""
        np.random.seed(seed)
        
        # 创建基础网格
        samples = np.zeros((n_samples, n_dimensions))
        
        for dim in prange(n_dimensions):
            # 为每个维度创建等间隔的区间
            intervals = np.arange(n_samples, dtype=np.float64)
            
            # 在每个区间内随机抽样
            random_offsets = np.random.random(n_samples)
            samples[:, dim] = (intervals + random_offsets) / n_samples
            
            # 随机打乱顺序
            np.random.shuffle(samples[:, dim])
        
        return samples
    
    def generate_optimized_samples(self, n_samples: int, seed: int = None) -> Tuple[np.ndarray, PerformanceMetrics]:
        """生成优化的LHS样本"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # 检查缓存
        cache_key = f"{n_samples}_{self.n_dimensions}_{seed}"
        if cache_key in self.cache:
            self.cache_hits += 1
            samples = self.cache[cache_key]
            logger.info(f"从缓存获取样本: {cache_key}")
        else:
            self.cache_misses += 1
            
            if seed is None:
                seed = int(time.time() * 1000) % 2**32
            
            if self.optimization_method == "vectorized":
                samples = self._vectorized_lhs_core(n_samples, self.n_dimensions, seed)
            elif self.optimization_method == "parallel":
                samples = self._parallel_lhs_generation(n_samples, seed)
            else:
                samples = self._standard_lhs_generation(n_samples, seed)
            
            # 缓存结果（如果样本数不太大）
            if n_samples <= 50000:
                self.cache[cache_key] = samples.copy()
        
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # 计算性能指标
        execution_time = end_time - start_time
        memory_usage = end_memory - start_memory
        cache_hit_rate = self.cache_hits / (self.cache_hits + self.cache_misses)
        throughput = n_samples / execution_time if execution_time > 0 else 0
        memory_efficiency = n_samples / max(memory_usage, 0.1)
        
        metrics = PerformanceMetrics(
            execution_time=execution_time,
            memory_usage_mb=memory_usage,
            cpu_usage_percent=psutil.cpu_percent(),
            cache_hit_rate=cache_hit_rate,
            throughput_samples_per_second=throughput,
            memory_efficiency=memory_efficiency
        )
        
        return samples, metrics
    
    def _parallel_lhs_generation(self, n_samples: int, seed: int) -> np.ndarray:
        """并行LHS生成"""
        n_cores = mp.cpu_count()
        samples_per_core = n_samples // n_cores
        remainder = n_samples % n_cores
        
        # 分配任务
        tasks = []
        current_seed = seed
        for i in range(n_cores):
            core_samples = samples_per_core + (1 if i < remainder else 0)
            if core_samples > 0:
                tasks.append((core_samples, current_seed + i))
        
        # 并行执行
        with ProcessPoolExecutor(max_workers=n_cores) as executor:
            futures = [
                executor.submit(self._generate_partial_samples, task[0], task[1])
                for task in tasks
            ]
            
            partial_results = [future.result() for future in futures]
        
        # 合并结果
        samples = np.vstack(partial_results)
        
        # 重新打乱以保持LHS特性
        for dim in range(self.n_dimensions):
            np.random.shuffle(samples[:, dim])
        
        return samples
    
    def _generate_partial_samples(self, n_samples: int, seed: int) -> np.ndarray:
        """生成部分样本（用于并行处理）"""
        return self._vectorized_lhs_core(n_samples, self.n_dimensions, seed)
    
    def _standard_lhs_generation(self, n_samples: int, seed: int) -> np.ndarray:
        """标准LHS生成（作为对比基准）"""
        from scipy.stats import qmc
        sampler = qmc.LatinHypercube(d=self.n_dimensions, seed=seed)
        return sampler.random(n=n_samples)

class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self, max_memory_mb: float = 1000):
        self.max_memory_mb = max_memory_mb
        self.memory_monitor = MemoryMonitor()
    
    def optimize_memory_usage(self, samples: np.ndarray) -> np.ndarray:
        """优化内存使用"""
        # 检查当前内存使用
        current_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        if current_memory > self.max_memory_mb:
            logger.warning(f"内存使用超限: {current_memory:.1f}MB > {self.max_memory_mb}MB")
            
            # 强制垃圾回收
            gc.collect()
            
            # 如果仍然超限，考虑数据压缩
            if psutil.Process().memory_info().rss / 1024 / 1024 > self.max_memory_mb:
                samples = self._compress_samples(samples)
        
        return samples
    
    def _compress_samples(self, samples: np.ndarray) -> np.ndarray:
        """压缩样本数据"""
        # 使用float32而不是float64来节省内存
        if samples.dtype == np.float64:
            samples = samples.astype(np.float32)
            logger.info("将样本数据类型从float64转换为float32")
        
        return samples
    
    def batch_process_large_samples(self, n_samples: int, n_dimensions: int, 
                                  batch_size: int = 10000) -> List[np.ndarray]:
        """批量处理大规模样本"""
        batches = []
        sampler = OptimizedLHSSampler(n_dimensions)
        
        for i in range(0, n_samples, batch_size):
            current_batch_size = min(batch_size, n_samples - i)
            batch_samples, _ = sampler.generate_optimized_samples(
                current_batch_size, seed=i
            )
            
            # 内存优化
            batch_samples = self.optimize_memory_usage(batch_samples)
            batches.append(batch_samples)
            
            # 监控内存使用
            self.memory_monitor.log_memory_usage(f"批次 {i//batch_size + 1}")
        
        return batches

class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self):
        self.memory_history = []
    
    def log_memory_usage(self, label: str = ""):
        """记录内存使用情况"""
        memory_mb = psutil.Process().memory_info().rss / 1024 / 1024
        self.memory_history.append({
            'timestamp': time.time(),
            'memory_mb': memory_mb,
            'label': label
        })
        
        logger.info(f"内存使用 {label}: {memory_mb:.1f}MB")
    
    def get_peak_memory_usage(self) -> float:
        """获取峰值内存使用"""
        if not self.memory_history:
            return 0.0
        return max(entry['memory_mb'] for entry in self.memory_history)
    
    def get_memory_trend(self) -> Dict[str, float]:
        """获取内存使用趋势"""
        if len(self.memory_history) < 2:
            return {'trend': 0.0, 'peak': 0.0, 'current': 0.0}
        
        current = self.memory_history[-1]['memory_mb']
        peak = self.get_peak_memory_usage()
        start = self.memory_history[0]['memory_mb']
        trend = (current - start) / len(self.memory_history)
        
        return {
            'trend': trend,
            'peak': peak,
            'current': current
        }

class SamplingCache:
    """抽样缓存系统"""
    
    def __init__(self, max_cache_size_mb: float = 500):
        self.cache = {}
        self.access_times = {}
        self.max_cache_size_mb = max_cache_size_mb
        self.current_cache_size_mb = 0
    
    def get(self, key: str) -> Optional[np.ndarray]:
        """从缓存获取样本"""
        if key in self.cache:
            self.access_times[key] = time.time()
            return self.cache[key]
        return None
    
    def put(self, key: str, samples: np.ndarray):
        """将样本放入缓存"""
        sample_size_mb = samples.nbytes / 1024 / 1024
        
        # 检查是否需要清理缓存
        if self.current_cache_size_mb + sample_size_mb > self.max_cache_size_mb:
            self._evict_lru_entries(sample_size_mb)
        
        self.cache[key] = samples.copy()
        self.access_times[key] = time.time()
        self.current_cache_size_mb += sample_size_mb
    
    def _evict_lru_entries(self, required_space_mb: float):
        """清理最近最少使用的缓存条目"""
        # 按访问时间排序
        sorted_keys = sorted(self.access_times.keys(), 
                           key=lambda k: self.access_times[k])
        
        freed_space = 0
        for key in sorted_keys:
            if freed_space >= required_space_mb:
                break
            
            sample_size_mb = self.cache[key].nbytes / 1024 / 1024
            del self.cache[key]
            del self.access_times[key]
            freed_space += sample_size_mb
            self.current_cache_size_mb -= sample_size_mb
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_times.clear()
        self.current_cache_size_mb = 0
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'cache_entries': len(self.cache),
            'cache_size_mb': self.current_cache_size_mb,
            'cache_utilization': self.current_cache_size_mb / self.max_cache_size_mb,
            'oldest_entry_age': time.time() - min(self.access_times.values()) if self.access_times else 0
        }

class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.profiles = {}
    
    def profile_sampling_operation(self, operation_name: str, operation_func, *args, **kwargs):
        """分析抽样操作的性能"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        start_cpu = psutil.cpu_percent()
        
        # 执行操作
        result = operation_func(*args, **kwargs)
        
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024
        end_cpu = psutil.cpu_percent()
        
        # 记录性能数据
        profile_data = {
            'execution_time': end_time - start_time,
            'memory_delta_mb': end_memory - start_memory,
            'cpu_usage_percent': (start_cpu + end_cpu) / 2,
            'timestamp': time.time()
        }
        
        if operation_name not in self.profiles:
            self.profiles[operation_name] = []
        self.profiles[operation_name].append(profile_data)
        
        return result
    
    def get_performance_summary(self) -> Dict[str, Dict[str, float]]:
        """获取性能摘要"""
        summary = {}
        
        for operation_name, profiles in self.profiles.items():
            if profiles:
                execution_times = [p['execution_time'] for p in profiles]
                memory_deltas = [p['memory_delta_mb'] for p in profiles]
                cpu_usages = [p['cpu_usage_percent'] for p in profiles]
                
                summary[operation_name] = {
                    'avg_execution_time': np.mean(execution_times),
                    'max_execution_time': np.max(execution_times),
                    'min_execution_time': np.min(execution_times),
                    'avg_memory_delta': np.mean(memory_deltas),
                    'max_memory_delta': np.max(memory_deltas),
                    'avg_cpu_usage': np.mean(cpu_usages),
                    'operation_count': len(profiles)
                }
        
        return summary
    
    def identify_bottlenecks(self) -> List[Dict[str, Any]]:
        """识别性能瓶颈"""
        bottlenecks = []
        summary = self.get_performance_summary()
        
        for operation_name, stats in summary.items():
            # 识别执行时间瓶颈
            if stats['avg_execution_time'] > 10.0:  # 超过10秒
                bottlenecks.append({
                    'type': 'execution_time',
                    'operation': operation_name,
                    'severity': 'high' if stats['avg_execution_time'] > 30 else 'medium',
                    'value': stats['avg_execution_time'],
                    'recommendation': '考虑并行化或算法优化'
                })
            
            # 识别内存使用瓶颈
            if stats['avg_memory_delta'] > 500:  # 超过500MB
                bottlenecks.append({
                    'type': 'memory_usage',
                    'operation': operation_name,
                    'severity': 'high' if stats['avg_memory_delta'] > 1000 else 'medium',
                    'value': stats['avg_memory_delta'],
                    'recommendation': '考虑批量处理或内存优化'
                })
            
            # 识别CPU使用瓶颈
            if stats['avg_cpu_usage'] > 80:  # 超过80%
                bottlenecks.append({
                    'type': 'cpu_usage',
                    'operation': operation_name,
                    'severity': 'medium',
                    'value': stats['avg_cpu_usage'],
                    'recommendation': '考虑负载均衡或优化算法'
                })
        
        return bottlenecks