#!/usr/bin/env python3
"""
深度神经网络性能优化演示
展示内存优化、智能批大小调整和性能基准测试功能
"""

import numpy as np
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.calibration.neural_network import CalibrationDNN, NetworkConfig
from src.calibration.training_monitor import (
    TrainingMonitor, MemoryOptimizer, AdaptiveBatchSize, PerformanceBenchmarker
)
from src.calibration.training_data_generator import (
    TrainingDataGenerator, DataGenerationConfig
)


def demo_memory_optimization():
    """演示内存优化功能"""
    print("=" * 60)
    print("内存优化演示")
    print("=" * 60)
    
    # 创建一个大型网络配置
    large_config = NetworkConfig(
        input_dim=100,
        output_dim=50,
        layers=[2048, 1024, 512, 256, 128],
        architecture='feedforward'
    )
    
    print("原始配置:")
    print(f"  输入维度: {large_config.input_dim}")
    print(f"  输出维度: {large_config.output_dim}")
    print(f"  网络层: {large_config.layers}")
    
    # 创建DNN并估算内存使用
    dnn = CalibrationDNN(large_config)
    original_memory = dnn._estimate_memory_usage()
    print(f"  估算内存使用: {original_memory:.2f} GB")
    
    # 内存优化
    print("\n执行内存优化...")
    optimized_config = dnn.optimize_for_memory(target_memory_gb=2.0)
    optimized_memory = dnn._estimate_memory_usage(optimized_config)
    
    print("优化后配置:")
    print(f"  网络层: {optimized_config.layers}")
    print(f"  估算内存使用: {optimized_memory:.2f} GB")
    print(f"  内存减少: {((original_memory - optimized_memory) / original_memory * 100):.1f}%")


def demo_batch_size_optimization():
    """演示批大小优化功能"""
    print("\n" + "=" * 60)
    print("批大小优化演示")
    print("=" * 60)
    
    # 不同的数据集大小和内存限制
    scenarios = [
        {"dataset_size": 1000, "memory_gb": 4.0, "name": "小数据集，低内存"},
        {"dataset_size": 50000, "memory_gb": 8.0, "name": "中等数据集，中等内存"},
        {"dataset_size": 200000, "memory_gb": 16.0, "name": "大数据集，高内存"},
    ]
    
    config = NetworkConfig(input_dim=50, output_dim=20, layers=[256, 128, 64])
    
    for scenario in scenarios:
        print(f"\n场景: {scenario['name']}")
        print(f"  数据集大小: {scenario['dataset_size']:,}")
        print(f"  可用内存: {scenario['memory_gb']} GB")
        
        suggestion = CalibrationDNN.suggest_optimal_batch_size(
            model_config=config,
            dataset_size=scenario['dataset_size'],
            available_memory_gb=scenario['memory_gb']
        )
        
        print(f"  建议批大小: {suggestion['suggested_batch_size']}")
        print(f"  保守选择: {suggestion['alternatives']['conservative']}")
        print(f"  激进选择: {suggestion['alternatives']['aggressive']}")
        print(f"  每样本内存: {suggestion['memory_per_sample_mb']:.2f} MB")


def demo_adaptive_batch_size():
    """演示自适应批大小调整"""
    print("\n" + "=" * 60)
    print("自适应批大小调整演示")
    print("=" * 60)
    
    # 创建自适应批大小调整器
    adaptive_batch = AdaptiveBatchSize(initial_batch_size=32)
    
    # 模拟训练过程中的性能变化
    print("模拟训练过程中的批大小调整:")
    print("Epoch | 批大小 | 内存使用% | 训练时间(s) | 损失")
    print("-" * 50)
    
    for epoch in range(1, 21):
        # 模拟性能指标
        memory_usage = np.random.uniform(40, 90)
        training_time = np.random.uniform(0.5, 2.0)
        loss = max(0.1, 2.0 - epoch * 0.08 + np.random.normal(0, 0.1))
        
        # 调整批大小
        new_batch_size = adaptive_batch.adjust_batch_size(
            epoch=epoch,
            memory_usage=memory_usage,
            training_time=training_time,
            loss=loss
        )
        
        print(f"{epoch:5d} | {new_batch_size:6d} | {memory_usage:8.1f} | {training_time:10.2f} | {loss:.3f}")
    
    # 显示调整摘要
    summary = adaptive_batch.get_adjustment_summary()
    print(f"\n调整摘要:")
    print(f"  初始批大小: {summary['initial_batch_size']}")
    print(f"  最终批大小: {summary['current_batch_size']}")
    print(f"  总调整次数: {summary['total_adjustments']}")
    
    if summary['adjustment_history']:
        print("  调整历史:")
        for adj in summary['adjustment_history'][-3:]:  # 显示最后3次调整
            print(f"    Epoch {adj['epoch']}: {adj['old_batch_size']} → {adj['new_batch_size']} ({adj['reason']})")


def demo_performance_benchmarking():
    """演示性能基准测试"""
    print("\n" + "=" * 60)
    print("性能基准测试演示")
    print("=" * 60)
    
    # 创建测试数据
    print("准备测试数据...")
    X_sample = np.random.randn(1000, 20)
    y_sample = np.random.randn(1000, 5)
    
    # 创建简单模型
    config = NetworkConfig(input_dim=20, output_dim=5, layers=[64, 32])
    dnn = CalibrationDNN(config)
    model = dnn.build_model()
    
    # 创建性能基准测试器
    benchmarker = PerformanceBenchmarker()
    
    print("运行性能基准测试...")
    print("注意: 这是一个简化的演示，实际测试可能需要更长时间")
    
    # 运行基准测试（使用较少的批大小以加快演示）
    benchmark_results = benchmarker.run_benchmark(
        model=model,
        X_sample=X_sample,
        y_sample=y_sample,
        batch_sizes=[16, 32, 64],  # 简化的批大小列表
        n_runs=2  # 减少运行次数以加快演示
    )
    
    # 显示结果
    print("\n基准测试结果:")
    for result in benchmark_results['batch_results']:
        print(f"批大小 {result['batch_size']:3d}: "
              f"训练 {result['avg_train_time']:.3f}s, "
              f"推理 {result['avg_inference_time']:.3f}s, "
              f"吞吐量 {result['throughput']:.1f} samples/s, "
              f"效率分数 {result['efficiency_score']:.1f}")
    
    # 最优配置
    optimal = benchmark_results['optimal_batch_size']
    if optimal:
        print(f"\n最优配置:")
        print(f"  批大小: {optimal['batch_size']}")
        print(f"  效率分数: {optimal['efficiency_score']:.2f}")
        print(f"  吞吐量: {optimal['throughput']:.1f} samples/s")


def demo_integrated_optimization():
    """演示集成优化流程"""
    print("\n" + "=" * 60)
    print("集成优化流程演示")
    print("=" * 60)
    
    # 创建训练监控器（包含所有优化组件）
    monitor = TrainingMonitor(patience=10, verbose=1)
    
    print("训练监控器已初始化，包含以下优化组件:")
    print("  ✓ 内存优化器")
    print("  ✓ 自适应批大小调整器")
    print("  ✓ 性能基准测试器")
    
    # 模拟优化过程
    print("\n模拟训练过程中的集成优化:")
    
    # 内存优化
    print("\n1. 内存优化:")
    memory_info = monitor.memory_optimizer.get_memory_usage()
    print(f"   当前内存使用: {memory_info['percent']:.1f}%")
    
    optimized_memory = monitor.memory_optimizer.optimize_memory(epoch=1, force=True)
    print(f"   优化后内存使用: {optimized_memory['percent']:.1f}%")
    
    # 自适应批大小
    print("\n2. 自适应批大小调整:")
    new_batch_size = monitor.adaptive_batch_size.adjust_batch_size(
        epoch=1,
        memory_usage=optimized_memory['percent'],
        training_time=1.5,
        loss=0.8
    )
    print(f"   调整后批大小: {new_batch_size}")
    
    print("\n集成优化演示完成！")


def main():
    """主演示函数"""
    print("深度神经网络性能优化演示")
    print("本演示将展示以下优化功能:")
    print("1. 内存优化")
    print("2. 批大小优化")
    print("3. 自适应批大小调整")
    print("4. 性能基准测试")
    print("5. 集成优化流程")
    
    try:
        demo_memory_optimization()
        demo_batch_size_optimization()
        demo_adaptive_batch_size()
        demo_performance_benchmarking()
        demo_integrated_optimization()
        
        print("\n" + "=" * 60)
        print("🎉 所有演示完成！")
        print("=" * 60)
        print("\n这些优化功能可以显著提升深度神经网络训练的效率和稳定性。")
        print("在实际使用中，建议根据具体的硬件配置和数据集特点进行调整。")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
