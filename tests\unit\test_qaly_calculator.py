"""
Unit tests for QALY calculator module.
"""
import pytest
import numpy as np
from src.modules.economics.qaly_calculator import (
    QALYCalculator, HealthState, QALYResult, UtilityScale
)
from src.core.individual import Individual
from src.core.population import Population


class TestQALYCalculator:
    """Test suite for QALYCalculator class."""
    
    def test_init(self):
        """Test QALYCalculator initialization."""
        calculator = QALYCalculator()
        assert calculator.discount_rate == 0.03
        assert isinstance(calculator.utility_values, dict)
        assert isinstance(calculator.age_adjustments, dict)
        
        calculator_custom = QALYCalculator(discount_rate=0.05)
        assert calculator_custom.discount_rate == 0.05
    
    def test_get_age_group(self):
        """Test age group determination."""
        calculator = QALYCalculator()
        
        assert calculator._get_age_group(25) == '18_29'
        assert calculator._get_age_group(35) == '30_39'
        assert calculator._get_age_group(45) == '40_49'
        assert calculator._get_age_group(55) == '50_59'
        assert calculator._get_age_group(65) == '60_69'
        assert calculator._get_age_group(75) == '70_79'
        assert calculator._get_age_group(85) == '80_plus'
    
    def test_get_age_adjusted_utility(self):
        """Test age-adjusted utility calculation."""
        calculator = QALYCalculator()
        
        base_utility = 0.8
        adjusted_utility = calculator._get_age_adjusted_utility(base_utility, 25)
        expected_adjustment = calculator.age_adjustments['18_29']
        expected_utility = base_utility * expected_adjustment
        
        assert adjusted_utility == expected_utility
    
    def test_extract_health_states(self):
        """Test health state extraction from individual."""
        calculator = QALYCalculator()
        
        # Create a mock individual with required parameters
        from src.core.enums import Gender
        individual = Individual(birth_year=1980, gender=Gender.FEMALE)
        individual.age = 55
        individual.baseline_age = 50
        individual.disease_state = 'clinical_cancer_stage_ii'
        
        health_states = calculator._extract_health_states(individual)
        assert isinstance(health_states, list)
        assert len(health_states) >= 1
        # At least one health state should be extracted
        assert isinstance(health_states[0], HealthState)
    
    def test_calculate_individual_qalys(self):
        """Test individual QALY calculation."""
        calculator = QALYCalculator()
        
        # Create a mock individual with required parameters
        from src.core.enums import Gender
        individual = Individual(birth_year=1980, gender=Gender.FEMALE)
        individual.age = 55
        individual.baseline_age = 50
        
        # Create sample health states
        health_states = [
            HealthState(
                state_name='normal',
                utility_value=0.95,
                duration_years=2.0,
                age_at_start=50
            ),
            HealthState(
                state_name='clinical_cancer_stage_ii',
                utility_value=0.78,
                duration_years=3.0,
                age_at_start=52
            )
        ]
        
        result = calculator.calculate_individual_qalys(individual, health_states)
        
        assert isinstance(result, QALYResult)
        assert result.total_qalys >= 0
        assert result.undiscounted_qalys >= 0
        assert result.discounted_qalys >= 0
        assert isinstance(result.qaly_by_age_group, dict)
    
    def test_calculate_population_qalys(self):
        """Test population QALY calculation."""
        calculator = QALYCalculator()
        
        # Create a mock population
        population = Population()
        population.individuals = []
        
        # Add mock individuals
        from src.core.enums import Gender
        for i in range(3):
            individual = Individual(birth_year=1980, gender=Gender.FEMALE)
            individual.age = 50 + i * 5
            individual.baseline_age = 45 + i * 5
            population.individuals.append(individual)
        
        result = calculator.calculate_population_qalys(population, "test_scenario")
        
        assert isinstance(result, dict)
        assert result['scenario_name'] == "test_scenario"
        assert 'total_population_qalys' in result
        assert 'mean_individual_qalys' in result
        assert 'individual_results' in result
        assert 'qaly_distribution' in result


if __name__ == "__main__":
    pytest.main([__file__])