"""
并行抽样模块
实现多线程和多进程并行抽样处理
"""

import numpy as np
import time
import threading
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from typing import Dict, List, Optional, Tuple, Any, Callable, Union
from dataclasses import dataclass
import queue
import logging
from functools import partial
import psutil

from .parameter_sampler import (
    ParameterDefinition, SamplingConfig, SamplingResult, 
    LatinHypercubeSampler
)
from .performance_optimizer import PerformanceMetrics, MemoryMonitor

logger = logging.getLogger(__name__)

@dataclass
class ParallelConfig:
    """并行配置数据类"""
    n_workers: int = None  # None表示自动检测
    execution_mode: str = "thread"  # "thread" 或 "process"
    chunk_size: int = 1000
    max_memory_per_worker_mb: float = 500
    timeout_seconds: float = 300
    load_balancing: bool = True

@dataclass
class WorkerTask:
    """工作任务数据类"""
    task_id: str
    samples_count: int
    random_seed: int
    parameters: List[ParameterDefinition]
    sampling_config: Dict[str, Any]

@dataclass
class WorkerResult:
    """工作结果数据类"""
    task_id: str
    samples: np.ndarray
    execution_time: float
    memory_usage_mb: float
    worker_id: int
    success: bool
    error_message: Optional[str] = None

class WorkerPool:
    """工作池管理器"""
    
    def __init__(self, config: ParallelConfig):
        self.config = config
        self.n_workers = config.n_workers or mp.cpu_count()
        self.active_workers = 0
        self.completed_tasks = 0
        self.failed_tasks = 0
        self.worker_stats = {}
        self.memory_monitor = MemoryMonitor()
        
    def execute_parallel_sampling(self, tasks: List[WorkerTask], 
                                 progress_callback: Optional[Callable] = None) -> List[WorkerResult]:
        """
        执行并行抽样任务
        
        Args:
            tasks: 任务列表
            progress_callback: 进度回调函数
            
        Returns:
            List[WorkerResult]: 工作结果列表
        """
        start_time = time.time()
        results = []
        
        if self.config.execution_mode == "thread":
            results = self._execute_with_threads(tasks, progress_callback)
        elif self.config.execution_mode == "process":
            results = self._execute_with_processes(tasks, progress_callback)
        else:
            raise ValueError(f"不支持的执行模式: {self.config.execution_mode}")
        
        execution_time = time.time() - start_time
        
        # 记录统计信息
        successful_results = [r for r in results if r.success]
        failed_results = [r for r in results if not r.success]
        
        logger.info(f"并行抽样完成: {len(successful_results)}成功, {len(failed_results)}失败, "
                   f"总耗时: {execution_time:.2f}秒")
        
        return results
    
    def _execute_with_threads(self, tasks: List[WorkerTask], 
                            progress_callback: Optional[Callable] = None) -> List[WorkerResult]:
        """使用线程池执行任务"""
        results = []
        
        with ThreadPoolExecutor(max_workers=self.n_workers) as executor:
            # 提交所有任务
            future_to_task = {
                executor.submit(self._execute_single_task, task, worker_id): task
                for worker_id, task in enumerate(tasks)
            }
            
            # 收集结果
            for future in as_completed(future_to_task, timeout=self.config.timeout_seconds):
                task = future_to_task[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result.success:
                        self.completed_tasks += 1
                    else:
                        self.failed_tasks += 1
                    
                    # 调用进度回调
                    if progress_callback:
                        progress = len(results) / len(tasks)
                        progress_callback(progress, len(results), len(tasks))
                        
                except Exception as e:
                    logger.error(f"任务 {task.task_id} 执行失败: {e}")
                    results.append(WorkerResult(
                        task_id=task.task_id,
                        samples=np.array([]),
                        execution_time=0,
                        memory_usage_mb=0,
                        worker_id=-1,
                        success=False,
                        error_message=str(e)
                    ))
                    self.failed_tasks += 1
        
        return results
    
    def _execute_with_processes(self, tasks: List[WorkerTask], 
                              progress_callback: Optional[Callable] = None) -> List[WorkerResult]:
        """使用进程池执行任务"""
        results = []
        
        # 创建进程池
        with ProcessPoolExecutor(max_workers=self.n_workers) as executor:
            # 提交所有任务
            future_to_task = {
                executor.submit(_execute_task_in_process, task, worker_id): task
                for worker_id, task in enumerate(tasks)
            }
            
            # 收集结果
            for future in as_completed(future_to_task, timeout=self.config.timeout_seconds):
                task = future_to_task[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result.success:
                        self.completed_tasks += 1
                    else:
                        self.failed_tasks += 1
                    
                    # 调用进度回调
                    if progress_callback:
                        progress = len(results) / len(tasks)
                        progress_callback(progress, len(results), len(tasks))
                        
                except Exception as e:
                    logger.error(f"任务 {task.task_id} 执行失败: {e}")
                    results.append(WorkerResult(
                        task_id=task.task_id,
                        samples=np.array([]),
                        execution_time=0,
                        memory_usage_mb=0,
                        worker_id=-1,
                        success=False,
                        error_message=str(e)
                    ))
                    self.failed_tasks += 1
        
        return results
    
    def _execute_single_task(self, task: WorkerTask, worker_id: int) -> WorkerResult:
        """执行单个任务"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        try:
            # 创建抽样配置
            config = SamplingConfig(
                parameters=task.parameters,
                n_samples=task.samples_count,
                random_seed=task.random_seed,
                sampling_method=task.sampling_config.get('sampling_method', 'lhs'),
                optimization_criterion=task.sampling_config.get('optimization_criterion', 'maximin')
            )
            
            # 创建抽样器并生成样本
            sampler = LatinHypercubeSampler(config)
            result = sampler.generate_samples()
            
            execution_time = time.time() - start_time
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024
            memory_usage = end_memory - start_memory
            
            return WorkerResult(
                task_id=task.task_id,
                samples=result.samples,
                execution_time=execution_time,
                memory_usage_mb=memory_usage,
                worker_id=worker_id,
                success=True
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024
            memory_usage = end_memory - start_memory
            
            logger.error(f"工作任务 {task.task_id} 执行失败: {e}")
            
            return WorkerResult(
                task_id=task.task_id,
                samples=np.array([]),
                execution_time=execution_time,
                memory_usage_mb=memory_usage,
                worker_id=worker_id,
                success=False,
                error_message=str(e)
            )
    
    def get_worker_stats(self) -> Dict[str, Any]:
        """获取工作统计信息"""
        return {
            'n_workers': self.n_workers,
            'execution_mode': self.config.execution_mode,
            'completed_tasks': self.completed_tasks,
            'failed_tasks': self.failed_tasks,
            'success_rate': self.completed_tasks / (self.completed_tasks + self.failed_tasks) if (self.completed_tasks + self.failed_tasks) > 0 else 0,
            'worker_stats': self.worker_stats
        }

def _execute_task_in_process(task: WorkerTask, worker_id: int) -> WorkerResult:
    """在独立进程中执行任务（用于进程池）"""
    start_time = time.time()
    start_memory = psutil.Process().memory_info().rss / 1024 / 1024
    
    try:
        # 创建抽样配置
        config = SamplingConfig(
            parameters=task.parameters,
            n_samples=task.samples_count,
            random_seed=task.random_seed,
            sampling_method=task.sampling_config.get('sampling_method', 'lhs'),
            optimization_criterion=task.sampling_config.get('optimization_criterion', 'maximin')
        )
        
        # 创建抽样器并生成样本
        sampler = LatinHypercubeSampler(config)
        result = sampler.generate_samples()
        
        execution_time = time.time() - start_time
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024
        memory_usage = end_memory - start_memory
        
        return WorkerResult(
            task_id=task.task_id,
            samples=result.samples,
            execution_time=execution_time,
            memory_usage_mb=memory_usage,
            worker_id=worker_id,
            success=True
        )
        
    except Exception as e:
        execution_time = time.time() - start_time
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024
        memory_usage = end_memory - start_memory
        
        return WorkerResult(
            task_id=task.task_id,
            samples=np.array([]),
            execution_time=execution_time,
            memory_usage_mb=memory_usage,
            worker_id=worker_id,
            success=False,
            error_message=str(e)
        )

class LoadBalancer:
    """负载均衡器"""
    
    def __init__(self, n_workers: int):
        self.n_workers = n_workers
        self.worker_loads = [0] * n_workers
        self.worker_performance = [1.0] * n_workers  # 性能权重
    
    def distribute_tasks(self, total_samples: int, chunk_size: int) -> List[Tuple[int, int, int]]:
        """
        分配任务到工作进程
        
        Args:
            total_samples: 总样本数
            chunk_size: 块大小
            
        Returns:
            List[Tuple[int, int, int]]: (worker_id, samples_count, start_seed) 列表
        """
        tasks = []
        remaining_samples = total_samples
        current_seed = 0
        
        while remaining_samples > 0:
            # 选择负载最轻的工作进程
            worker_id = self._select_best_worker()
            
            # 计算分配给该工作进程的样本数
            samples_for_worker = min(chunk_size, remaining_samples)
            
            # 根据工作进程性能调整样本数
            performance_factor = self.worker_performance[worker_id]
            adjusted_samples = int(samples_for_worker * performance_factor)
            adjusted_samples = min(adjusted_samples, remaining_samples)
            adjusted_samples = max(adjusted_samples, 1)  # 至少1个样本
            
            tasks.append((worker_id, adjusted_samples, current_seed))
            
            # 更新状态
            self.worker_loads[worker_id] += adjusted_samples
            remaining_samples -= adjusted_samples
            current_seed += 1000  # 为每个任务使用不同的种子范围
        
        return tasks
    
    def _select_best_worker(self) -> int:
        """选择最佳工作进程"""
        # 计算每个工作进程的有效负载（考虑性能权重）
        effective_loads = [
            load / performance for load, performance 
            in zip(self.worker_loads, self.worker_performance)
        ]
        
        # 选择有效负载最小的工作进程
        return int(np.argmin(effective_loads))
    
    def update_worker_performance(self, worker_id: int, execution_time: float, samples_count: int):
        """更新工作进程性能"""
        if execution_time > 0 and samples_count > 0:
            # 计算样本处理速度
            samples_per_second = samples_count / execution_time
            
            # 更新性能权重（使用指数移动平均）
            alpha = 0.3  # 学习率
            new_performance = samples_per_second / 1000  # 归一化
            self.worker_performance[worker_id] = (
                alpha * new_performance + 
                (1 - alpha) * self.worker_performance[worker_id]
            )
    
    def get_load_distribution(self) -> Dict[str, Any]:
        """获取负载分布信息"""
        total_load = sum(self.worker_loads)
        
        return {
            'worker_loads': self.worker_loads,
            'worker_performance': self.worker_performance,
            'total_load': total_load,
            'load_balance_ratio': max(self.worker_loads) / max(min(self.worker_loads), 1),
            'average_load': total_load / self.n_workers if self.n_workers > 0 else 0
        }

class ParallelSampler:
    """并行抽样器主类"""
    
    def __init__(self, parameters: List[ParameterDefinition], 
                 parallel_config: Optional[ParallelConfig] = None):
        """
        初始化并行抽样器
        
        Args:
            parameters: 参数定义列表
            parallel_config: 并行配置
        """
        self.parameters = parameters
        self.config = parallel_config or ParallelConfig()
        
        # 自动检测最优工作进程数
        if self.config.n_workers is None:
            self.config.n_workers = min(mp.cpu_count(), 8)  # 限制最大进程数
        
        self.load_balancer = LoadBalancer(self.config.n_workers)
        self.worker_pool = WorkerPool(self.config)
        
        # 性能统计
        self.performance_history = []
    
    def generate_parallel_samples(self, n_samples: int, 
                                random_seed: int = 42,
                                progress_callback: Optional[Callable] = None) -> SamplingResult:
        """
        生成并行抽样
        
        Args:
            n_samples: 样本数量
            random_seed: 随机种子
            progress_callback: 进度回调函数
            
        Returns:
            SamplingResult: 抽样结果
        """
        start_time = time.time()
        
        # 分配任务
        task_assignments = self.load_balancer.distribute_tasks(
            n_samples, self.config.chunk_size
        )
        
        # 创建工作任务
        tasks = []
        for i, (worker_id, samples_count, start_seed) in enumerate(task_assignments):
            task = WorkerTask(
                task_id=f"task_{i:04d}",
                samples_count=samples_count,
                random_seed=random_seed + start_seed,
                parameters=self.parameters,
                sampling_config={
                    'sampling_method': 'lhs',
                    'optimization_criterion': 'maximin'
                }
            )
            tasks.append(task)
        
        # 执行并行任务
        worker_results = self.worker_pool.execute_parallel_sampling(
            tasks, progress_callback
        )
        
        # 处理结果
        successful_results = [r for r in worker_results if r.success]
        failed_results = [r for r in worker_results if not r.success]
        
        if not successful_results:
            raise RuntimeError("所有并行任务都失败了")
        
        if failed_results:
            logger.warning(f"{len(failed_results)} 个任务失败")
        
        # 合并样本
        all_samples = []
        for result in successful_results:
            all_samples.append(result.samples)
            
            # 更新工作进程性能
            self.load_balancer.update_worker_performance(
                result.worker_id, result.execution_time, len(result.samples)
            )
        
        combined_samples = np.vstack(all_samples)
        
        # 重新打乱以保持LHS特性
        for dim in range(len(self.parameters)):
            np.random.shuffle(combined_samples[:, dim])
        
        # 计算质量指标
        quality_metrics = self._calculate_quality_metrics(combined_samples)
        
        # 生成哈希签名
        hash_signature = self._generate_hash_signature(combined_samples, random_seed)
        
        total_time = time.time() - start_time
        
        # 记录性能
        performance_record = {
            'timestamp': time.time(),
            'n_samples': n_samples,
            'n_workers': self.config.n_workers,
            'execution_mode': self.config.execution_mode,
            'total_time': total_time,
            'successful_tasks': len(successful_results),
            'failed_tasks': len(failed_results),
            'samples_per_second': n_samples / total_time if total_time > 0 else 0,
            'parallel_efficiency': self._calculate_parallel_efficiency(worker_results)
        }
        self.performance_history.append(performance_record)
        
        # 创建配置
        final_config = SamplingConfig(
            parameters=self.parameters,
            n_samples=n_samples,
            random_seed=random_seed,
            sampling_method="lhs_parallel",
            optimization_criterion="maximin"
        )
        
        return SamplingResult(
            samples=combined_samples,
            parameter_names=[p.name for p in self.parameters],
            config=final_config,
            quality_metrics=quality_metrics,
            generation_time=total_time,
            hash_signature=hash_signature
        )
    
    def _calculate_quality_metrics(self, samples: np.ndarray) -> Dict[str, float]:
        """计算质量指标"""
        from scipy.spatial.distance import pdist
        from scipy.stats import kstest
        
        metrics = {}
        
        # 计算最小距离
        distances = pdist(samples)
        metrics['min_distance'] = float(np.min(distances))
        metrics['mean_distance'] = float(np.mean(distances))
        
        # 计算相关性
        correlation_matrix = np.corrcoef(samples.T)
        off_diagonal = correlation_matrix[np.triu_indices_from(correlation_matrix, k=1)]
        metrics['max_correlation'] = float(np.max(np.abs(off_diagonal)))
        metrics['mean_correlation'] = float(np.mean(np.abs(off_diagonal)))
        
        # 计算覆盖度
        coverage_scores = []
        for i in range(samples.shape[1]):
            ks_stat, _ = kstest(samples[:, i], 'uniform')
            coverage_scores.append(1 - ks_stat)
        
        metrics['mean_coverage'] = float(np.mean(coverage_scores))
        metrics['min_coverage'] = float(np.min(coverage_scores))
        
        return metrics
    
    def _generate_hash_signature(self, samples: np.ndarray, random_seed: int) -> str:
        """生成哈希签名"""
        import hashlib
        import json
        
        config_str = json.dumps({
            'n_samples': len(samples),
            'random_seed': random_seed,
            'sampling_method': 'lhs_parallel',
            'n_workers': self.config.n_workers,
            'parameters': [
                {
                    'name': p.name,
                    'min_value': p.min_value,
                    'max_value': p.max_value,
                    'distribution': p.distribution
                } for p in self.parameters
            ]
        }, sort_keys=True)
        
        samples_hash = hashlib.md5(samples.tobytes()).hexdigest()
        combined_str = config_str + samples_hash
        return hashlib.sha256(combined_str.encode()).hexdigest()
    
    def _calculate_parallel_efficiency(self, worker_results: List[WorkerResult]) -> float:
        """计算并行效率"""
        if not worker_results:
            return 0.0
        
        successful_results = [r for r in worker_results if r.success]
        if not successful_results:
            return 0.0
        
        # 计算总执行时间和理论最优时间
        total_samples = sum(len(r.samples) for r in successful_results)
        total_execution_time = sum(r.execution_time for r in successful_results)
        
        # 理论上单线程执行时间（基于平均执行速度）
        avg_samples_per_second = total_samples / total_execution_time if total_execution_time > 0 else 0
        theoretical_single_thread_time = total_samples / avg_samples_per_second if avg_samples_per_second > 0 else 0
        
        # 实际并行执行时间（最长的任务时间）
        actual_parallel_time = max(r.execution_time for r in successful_results)
        
        # 并行效率 = 理论单线程时间 / (实际并行时间 * 工作进程数)
        if actual_parallel_time > 0:
            efficiency = theoretical_single_thread_time / (actual_parallel_time * self.config.n_workers)
            return min(efficiency, 1.0)  # 效率不能超过100%
        
        return 0.0
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.performance_history:
            return {}
        
        recent_records = self.performance_history[-10:]  # 最近10次记录
        
        return {
            'total_runs': len(self.performance_history),
            'recent_average_efficiency': np.mean([r['parallel_efficiency'] for r in recent_records]),
            'recent_average_samples_per_second': np.mean([r['samples_per_second'] for r in recent_records]),
            'best_efficiency': max(r['parallel_efficiency'] for r in self.performance_history),
            'worker_pool_stats': self.worker_pool.get_worker_stats(),
            'load_balancer_stats': self.load_balancer.get_load_distribution(),
            'config': {
                'n_workers': self.config.n_workers,
                'execution_mode': self.config.execution_mode,
                'chunk_size': self.config.chunk_size
            }
        }
    
    def optimize_configuration(self) -> ParallelConfig:
        """自动优化并行配置"""
        if len(self.performance_history) < 3:
            return self.config
        
        # 分析历史性能数据
        recent_records = self.performance_history[-5:]
        avg_efficiency = np.mean([r['parallel_efficiency'] for r in recent_records])
        
        new_config = ParallelConfig(
            n_workers=self.config.n_workers,
            execution_mode=self.config.execution_mode,
            chunk_size=self.config.chunk_size,
            max_memory_per_worker_mb=self.config.max_memory_per_worker_mb,
            timeout_seconds=self.config.timeout_seconds,
            load_balancing=self.config.load_balancing
        )
        
        # 如果效率较低，尝试调整配置
        if avg_efficiency < 0.6:
            # 减少工作进程数以减少开销
            new_config.n_workers = max(2, self.config.n_workers - 1)
            # 增加块大小以减少任务切换开销
            new_config.chunk_size = min(5000, int(self.config.chunk_size * 1.5))
        elif avg_efficiency > 0.8:
            # 效率较高，可以尝试增加并行度
            max_workers = min(mp.cpu_count(), 12)
            if self.config.n_workers < max_workers:
                new_config.n_workers = self.config.n_workers + 1
        
        return new_config