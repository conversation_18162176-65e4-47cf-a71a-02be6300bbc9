"""
GPU加速的深度神经网络模块
实现用于校准的深度神经网络架构，支持GPU加速
"""

import tensorflow as tf
from tensorflow import keras
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
import json
import logging
from dataclasses import dataclass
import time

# 导入GPU加速器
from .gpu_accelerator import GPUAccelerator, setup_gpu_acceleration


@dataclass
class NetworkConfig:
    """网络配置数据类"""
    input_dim: int
    output_dim: int
    architecture: str = 'feedforward'  # feedforward, residual, ensemble
    layers: List[int] = None
    activation: str = 'relu'
    dropout_rate: float = 0.2
    learning_rate: float = 0.001
    batch_normalization: bool = True
    l2_regularization: float = 0.001
    target_weights: List[float] = None
    # GPU相关配置
    enable_gpu: bool = True
    mixed_precision: bool = True
    xla_acceleration: bool = True
    auto_batch_size: bool = True


class GPUCalibrationDNN:
    """GPU加速的校准深度神经网络类"""
    
    def __init__(self, config: NetworkConfig):
        """
        初始化深度神经网络
        
        Args:
            config: 网络配置
        """
        self.config = config
        self.model = None
        self.input_dim = config.input_dim
        self.output_dim = config.output_dim
        self.architecture = config.architecture
        
        # 设置默认层配置
        if config.layers is None:
            self.config.layers = [512, 256, 128, 64]
        
        # 设置默认目标权重
        if config.target_weights is None:
            self.config.target_weights = [1.0] * config.output_dim
            
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 初始化GPU加速器
        self.gpu_accelerator = None
        self.distribution_strategy = None
        self.optimal_batch_size = 32
        
        if config.enable_gpu:
            try:
                self.gpu_accelerator = setup_gpu_acceleration(
                    memory_limit=None,
                    allow_growth=True,
                    mixed_precision=config.mixed_precision,
                    xla_acceleration=config.xla_acceleration
                )
                self.distribution_strategy = self.gpu_accelerator.distribute_strategy()
                self.logger.info("GPU加速已启用")
            except Exception as e:
                self.logger.warning(f"GPU加速初始化失败，将使用CPU: {e}")
                self.gpu_accelerator = None
                self.distribution_strategy = None
    
    def build_model(self) -> keras.Model:
        """
        构建深度神经网络模型
        
        Returns:
            keras.Model: 构建的模型
        """
        # 在分布式策略范围内构建模型
        if self.distribution_strategy:
            with self.distribution_strategy.scope():
                model = self._create_model()
        else:
            model = self._create_model()
        
        self.model = model
        self.logger.info(f"成功构建 {self.architecture} 网络架构")
        return model
    
    def _create_model(self) -> keras.Model:
        """
        创建模型的内部方法
        
        Returns:
            keras.Model: 创建的模型
        """
        if self.architecture == 'feedforward':
            return self._build_feedforward_network()
        elif self.architecture == 'residual':
            return self._build_residual_network()
        elif self.architecture == 'ensemble':
            return self._build_ensemble_network()
        else:
            raise ValueError(f"不支持的网络架构: {self.architecture}")
    
    def _build_feedforward_network(self) -> keras.Model:
        """
        构建前馈神经网络
        
        Returns:
            keras.Model: 前馈网络模型
        """
        inputs = keras.Input(shape=(self.input_dim,), name='parameters')
        x = inputs
        
        # 输入标准化层
        if self.config.batch_normalization:
            x = keras.layers.BatchNormalization(name='input_norm')(x)
        
        # 隐藏层
        for i, units in enumerate(self.config.layers):
            x = keras.layers.Dense(
                units,
                activation=self.config.activation,
                kernel_regularizer=keras.regularizers.l2(self.config.l2_regularization),
                name=f'dense_{i+1}'
            )(x)
            
            if self.config.batch_normalization:
                x = keras.layers.BatchNormalization(name=f'bn_{i+1}')(x)
            
            if self.config.dropout_rate > 0:
                x = keras.layers.Dropout(self.config.dropout_rate, name=f'dropout_{i+1}')(x)
        
        # 输出层
        outputs = keras.layers.Dense(
            self.output_dim,
            activation='linear',
            name='outputs'
        )(x)
        
        model = keras.Model(inputs=inputs, outputs=outputs, name='gpu_calibration_dnn')
        return model
    
    def _build_residual_network(self) -> keras.Model:
        """
        构建残差神经网络
        
        Returns:
            keras.Model: 残差网络模型
        """
        inputs = keras.Input(shape=(self.input_dim,), name='parameters')
        x = inputs
        
        # 输入投影层
        x = keras.layers.Dense(self.config.layers[0], activation=self.config.activation)(x)
        if self.config.batch_normalization:
            x = keras.layers.BatchNormalization()(x)
        
        # 残差块
        for i, units in enumerate(self.config.layers[1:], 1):
            residual = x
            
            # 第一个全连接层
            x = keras.layers.Dense(units, activation=self.config.activation)(x)
            if self.config.batch_normalization:
                x = keras.layers.BatchNormalization()(x)
            if self.config.dropout_rate > 0:
                x = keras.layers.Dropout(self.config.dropout_rate)(x)
            
            # 第二个全连接层
            x = keras.layers.Dense(units, activation=None)(x)
            if self.config.batch_normalization:
                x = keras.layers.BatchNormalization()(x)
            
            # 残差连接（如果维度不匹配，需要投影）
            if residual.shape[-1] != units:
                residual = keras.layers.Dense(units, activation=None)(residual)
            
            x = keras.layers.Add()([x, residual])
            x = keras.layers.Activation(self.config.activation)(x)
        
        # 输出层
        outputs = keras.layers.Dense(self.output_dim, activation='linear')(x)
        
        model = keras.Model(inputs=inputs, outputs=outputs, name='gpu_residual_calibration_dnn')
        return model
    
    def _build_ensemble_network(self) -> keras.Model:
        """
        构建集成神经网络
        
        Returns:
            keras.Model: 集成网络模型
        """
        inputs = keras.Input(shape=(self.input_dim,), name='parameters')
        
        # 创建多个子网络
        ensemble_outputs = []
        n_models = 3  # 集成3个模型
        
        for i in range(n_models):
            # 每个子网络使用不同的架构
            x = inputs
            
            # 子网络特定的层配置
            sub_layers = [int(units * (0.8 + 0.4 * i / n_models)) for units in self.config.layers]
            
            for j, units in enumerate(sub_layers):
                x = keras.layers.Dense(
                    units,
                    activation=self.config.activation,
                    kernel_regularizer=keras.regularizers.l2(self.config.l2_regularization),
                    name=f'ensemble_{i}_dense_{j}'
                )(x)
                
                if self.config.batch_normalization:
                    x = keras.layers.BatchNormalization(name=f'ensemble_{i}_bn_{j}')(x)
                
                if self.config.dropout_rate > 0:
                    x = keras.layers.Dropout(self.config.dropout_rate, name=f'ensemble_{i}_dropout_{j}')(x)
            
            # 子网络输出
            sub_output = keras.layers.Dense(
                self.output_dim,
                activation='linear',
                name=f'ensemble_{i}_output'
            )(x)
            
            ensemble_outputs.append(sub_output)
        
        # 集成输出（平均）
        if len(ensemble_outputs) > 1:
            outputs = keras.layers.Average(name='ensemble_average')(ensemble_outputs)
        else:
            outputs = ensemble_outputs[0]
        
        model = keras.Model(inputs=inputs, outputs=outputs, name='gpu_ensemble_calibration_dnn')
        return model
    
    def compile_model(self, learning_rate: Optional[float] = None):
        """
        编译模型
        
        Args:
            learning_rate: 学习率，如果为None则使用配置中的值
        """
        if self.model is None:
            raise ValueError("模型尚未构建，请先调用 build_model()")
        
        lr = learning_rate if learning_rate is not None else self.config.learning_rate
        
        # 在分布式策略范围内编译模型
        if self.distribution_strategy:
            with self.distribution_strategy.scope():
                self._compile_model_internal(lr)
        else:
            self._compile_model_internal(lr)
        
        self.logger.info("模型编译完成")
    
    def _compile_model_internal(self, learning_rate: float):
        """
        内部模型编译方法
        
        Args:
            learning_rate: 学习率
        """
        optimizer = keras.optimizers.Adam(learning_rate=learning_rate)
        
        # 自定义损失函数（加权MSE）
        def weighted_mse_loss(y_true, y_pred):
            weights = tf.constant(self.config.target_weights, dtype=tf.float32)
            squared_diff = tf.square(y_true - y_pred)
            weighted_squared_diff = squared_diff * weights
            return tf.reduce_mean(weighted_squared_diff)
        
        self.model.compile(
            optimizer=optimizer,
            loss=weighted_mse_loss,
            metrics=['mae', 'mse', self._r_squared_metric]
        )
    
    def _r_squared_metric(self, y_true, y_pred):
        """
        R²决定系数指标
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            R²分数
        """
        ss_res = tf.reduce_sum(tf.square(y_true - y_pred))
        ss_tot = tf.reduce_sum(tf.square(y_true - tf.reduce_mean(y_true)))
        return 1 - ss_res / (ss_tot + tf.keras.backend.epsilon())
    
    def optimize_batch_size(self, X_sample: np.ndarray, y_sample: np.ndarray) -> int:
        """
        自动优化批处理大小
        
        Args:
            X_sample: 输入样本
            y_sample: 输出样本
            
        Returns:
            int: 优化后的批处理大小
        """
        if not self.gpu_accelerator or not self.config.auto_batch_size:
            return 32
        
        try:
            input_shape = (None,) + X_sample.shape[1:]
            optimal_batch_size = self.gpu_accelerator.optimize_batch_size(
                self.model,
                input_shape,
                starting_batch_size=32,
                max_batch_size=1024
            )
            
            self.optimal_batch_size = optimal_batch_size
            self.logger.info(f"优化后的批处理大小: {optimal_batch_size}")
            return optimal_batch_size
            
        except Exception as e:
            self.logger.warning(f"批处理大小优化失败: {e}")
            return 32
    
    def train(
        self,
        X_train: np.ndarray,
        y_train: np.ndarray,
        X_val: Optional[np.ndarray] = None,
        y_val: Optional[np.ndarray] = None,
        epochs: int = 100,
        batch_size: Optional[int] = None,
        callbacks: Optional[List] = None,
        verbose: int = 1
    ) -> keras.callbacks.History:
        """
        训练模型
        
        Args:
            X_train: 训练输入数据
            y_train: 训练目标数据
            X_val: 验证输入数据
            y_val: 验证目标数据
            epochs: 训练轮数
            batch_size: 批处理大小
            callbacks: 回调函数列表
            verbose: 详细程度
            
        Returns:
            keras.callbacks.History: 训练历史
        """
        if self.model is None:
            raise ValueError("模型尚未构建和编译")
        
        # 自动优化批处理大小
        if batch_size is None:
            if self.config.auto_batch_size:
                batch_size = self.optimize_batch_size(X_train, y_train)
            else:
                batch_size = self.optimal_batch_size
        
        # 准备验证数据
        validation_data = None
        if X_val is not None and y_val is not None:
            validation_data = (X_val, y_val)
        
        # 默认回调函数
        if callbacks is None:
            callbacks = []
            
            # 早停回调
            early_stopping = keras.callbacks.EarlyStopping(
                monitor='val_loss' if validation_data else 'loss',
                patience=20,
                restore_best_weights=True,
                verbose=1
            )
            callbacks.append(early_stopping)
            
            # 学习率调度
            lr_scheduler = keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss' if validation_data else 'loss',
                factor=0.5,
                patience=10,
                min_lr=1e-7,
                verbose=1
            )
            callbacks.append(lr_scheduler)
        
        # 记录训练开始时间
        start_time = time.time()
        
        # 训练模型
        history = self.model.fit(
            X_train,
            y_train,
            validation_data=validation_data,
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=verbose
        )
        
        # 记录训练时间
        training_time = time.time() - start_time
        self.logger.info(f"训练完成，耗时: {training_time:.2f} 秒")
        
        return history
    
    def predict(self, X: np.ndarray, batch_size: Optional[int] = None) -> np.ndarray:
        """
        预测
        
        Args:
            X: 输入数据
            batch_size: 批处理大小
            
        Returns:
            np.ndarray: 预测结果
        """
        if self.model is None:
            raise ValueError("模型尚未构建")
        
        if batch_size is None:
            batch_size = self.optimal_batch_size
        
        return self.model.predict(X, batch_size=batch_size)
    
    def profile_performance(self, X_sample: np.ndarray, batch_size: int = 32) -> Dict[str, float]:
        """
        性能分析
        
        Args:
            X_sample: 样本数据
            batch_size: 批处理大小
            
        Returns:
            Dict[str, float]: 性能指标
        """
        if not self.gpu_accelerator:
            self.logger.warning("GPU加速器未启用，无法进行性能分析")
            return {}
        
        input_shape = (None,) + X_sample.shape[1:]
        return self.gpu_accelerator.profile_model(
            self.model,
            input_shape,
            batch_size=batch_size
        )
    
    def get_gpu_info(self) -> Dict[str, Any]:
        """
        获取GPU信息
        
        Returns:
            Dict[str, Any]: GPU信息
        """
        if not self.gpu_accelerator:
            return {'gpu_enabled': False, 'message': 'GPU加速器未启用'}
        
        gpu_info = self.gpu_accelerator.get_gpu_info()
        gpu_info['optimal_batch_size'] = self.optimal_batch_size
        return gpu_info
    
    def get_memory_usage(self) -> Dict[str, Any]:
        """
        获取内存使用情况
        
        Returns:
            Dict[str, Any]: 内存使用报告
        """
        if not self.gpu_accelerator:
            return {'gpu_enabled': False, 'message': 'GPU加速器未启用'}
        
        return self.gpu_accelerator.memory_usage_report()
    
    def save_model(self, filepath: str, save_format: str = 'tf'):
        """
        保存模型
        
        Args:
            filepath: 保存路径
            save_format: 保存格式 ('tf', 'h5')
        """
        if self.model is None:
            raise ValueError("模型尚未构建")
        
        self.model.save(filepath, save_format=save_format)
        
        # 保存配置
        config_path = filepath + '_config.json'
        config_dict = {
            'input_dim': self.input_dim,
            'output_dim': self.output_dim,
            'architecture': self.architecture,
            'layers': self.config.layers,
            'activation': self.config.activation,
            'dropout_rate': self.config.dropout_rate,
            'learning_rate': self.config.learning_rate,
            'batch_normalization': self.config.batch_normalization,
            'l2_regularization': self.config.l2_regularization,
            'target_weights': self.config.target_weights,
            'optimal_batch_size': self.optimal_batch_size
        }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"模型已保存到: {filepath}")
    
    def load_model(self, filepath: str):
        """
        加载模型
        
        Args:
            filepath: 模型路径
        """
        self.model = keras.models.load_model(filepath)
        
        # 加载配置
        config_path = filepath + '_config.json'
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            
            self.optimal_batch_size = config_dict.get('optimal_batch_size', 32)
            self.logger.info(f"模型已从 {filepath} 加载")
            
        except FileNotFoundError:
            self.logger.warning(f"配置文件 {config_path} 未找到")
    
    def get_model_summary(self) -> str:
        """
        获取模型摘要
        
        Returns:
            str: 模型摘要字符串
        """
        if self.model is None:
            return "模型尚未构建"
        
        import io
        stream = io.StringIO()
        self.model.summary(print_fn=lambda x: stream.write(x + '\n'))
        return stream.getvalue()


class GPUNetworkArchitectureFactory:
    """GPU网络架构工厂类"""
    
    @staticmethod
    def create_gpu_config(
        input_dim: int,
        output_dim: int,
        complexity: str = 'medium',
        enable_gpu: bool = True,
        mixed_precision: bool = True,
        auto_batch_size: bool = True
    ) -> NetworkConfig:
        """
        创建GPU优化的网络配置
        
        Args:
            input_dim: 输入维度
            output_dim: 输出维度
            complexity: 复杂度 ('simple', 'medium', 'complex')
            enable_gpu: 是否启用GPU
            mixed_precision: 是否启用混合精度
            auto_batch_size: 是否自动优化批处理大小
            
        Returns:
            NetworkConfig: 网络配置
        """
        complexity_configs = {
            'simple': {
                'layers': [256, 128],
                'dropout_rate': 0.1,
                'l2_regularization': 0.01
            },
            'medium': {
                'layers': [1024, 512, 256, 128],
                'dropout_rate': 0.2,
                'l2_regularization': 0.001
            },
            'complex': {
                'layers': [2048, 1024, 512, 256, 128],
                'dropout_rate': 0.3,
                'l2_regularization': 0.0001
            }
        }
        
        config_params = complexity_configs.get(complexity, complexity_configs['medium'])
        
        return NetworkConfig(
            input_dim=input_dim,
            output_dim=output_dim,
            enable_gpu=enable_gpu,
            mixed_precision=mixed_precision,
            auto_batch_size=auto_batch_size,
            xla_acceleration=True,
            **config_params
        )
    
    @staticmethod
    def create_calibration_gpu_config(
        parameter_count: int,
        target_count: int,
        enable_gpu: bool = True
    ) -> NetworkConfig:
        """
        为校准任务创建GPU优化配置
        
        Args:
            parameter_count: 参数数量
            target_count: 目标数量
            enable_gpu: 是否启用GPU
            
        Returns:
            NetworkConfig: 校准专用GPU配置
        """
        # 根据参数和目标数量调整架构
        if parameter_count <= 10:
            layers = [256, 128, 64]
        elif parameter_count <= 50:
            layers = [1024, 512, 256, 128]
        else:
            layers = [2048, 1024, 512, 256, 128]
        
        return NetworkConfig(
            input_dim=parameter_count,
            output_dim=target_count,
            architecture='feedforward',
            layers=layers,
            activation='relu',
            dropout_rate=0.2,
            learning_rate=0.001,
            batch_normalization=True,
            l2_regularization=0.001,
            enable_gpu=enable_gpu,
            mixed_precision=True,
            xla_acceleration=True,
            auto_batch_size=True
        )


if __name__ == "__main__":
    # 示例用法
    config = GPUNetworkArchitectureFactory.create_gpu_config(
        input_dim=20,
        output_dim=5,
        complexity='medium',
        enable_gpu=True
    )
    
    # 创建GPU加速的神经网络
    gpu_dnn = GPUCalibrationDNN(config)
    
    # 构建和编译模型
    model = gpu_dnn.build_model()
    gpu_dnn.compile_model()
    
    # 打印模型摘要
    print(gpu_dnn.get_model_summary())
    
    # 打印GPU信息
    gpu_info = gpu_dnn.get_gpu_info()
    print("GPU信息:", gpu_info)