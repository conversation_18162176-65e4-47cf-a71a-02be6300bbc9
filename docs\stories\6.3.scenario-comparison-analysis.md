# Story 6.3: 场景比较分析

## Status
Draft

## Story
**As a** 政策分析师，
**I want** 并排比较多种筛查策略的结果，
**so that** 识别最优策略和关键差异。

## Acceptance Criteria
1. 实现多策略结果的并排比较窗口
2. 创建差异分析和统计显著性检验
3. 实现比较结果的可视化展示（热图、雷达图等）
4. 添加比较维度的自定义选择功能
5. 创建比较分析报告的自动生成
6. 实现比较结果的导出和保存功能

## Tasks / Subtasks

- [ ] 任务1：实现多策略并排比较窗口 (AC: 1)
  - [ ] 创建src/interfaces/desktop/windows/scenario_comparison.py文件
  - [ ] 实现ScenarioComparisonWindow类，比较界面
  - [ ] 设计多列对比表格和并排视图
  - [ ] 添加策略选择和动态加载功能
  - [ ] 创建比较指标的分类显示
  - [ ] 实现比较结果的排序和筛选

- [ ] 任务2：创建差异分析和统计检验 (AC: 2)
  - [ ] 创建src/analysis/statistical_comparison.py文件
  - [ ] 实现StatisticalComparator类，统计比较分析
  - [ ] 添加t检验、卡方检验等统计方法
  - [ ] 实现效应量计算和置信区间
  - [ ] 创建多重比较校正功能
  - [ ] 添加非参数检验支持

- [ ] 任务3：实现比较结果可视化展示 (AC: 3)
  - [ ] 创建src/interfaces/desktop/widgets/comparison_charts.py文件
  - [ ] 实现ComparisonChartWidget类，比较图表
  - [ ] 添加热图显示策略间差异
  - [ ] 创建雷达图展示多维度比较
  - [ ] 实现瀑布图显示成本效益分解
  - [ ] 添加森林图显示置信区间比较

- [ ] 任务4：添加比较维度自定义选择 (AC: 4)
  - [ ] 创建src/interfaces/desktop/widgets/dimension_selector.py文件
  - [ ] 实现DimensionSelector类，维度选择器
  - [ ] 添加指标分类和层级选择
  - [ ] 实现自定义权重和重要性设置
  - [ ] 创建维度组合和预设模板
  - [ ] 添加维度选择的保存和加载

- [ ] 任务5：创建比较分析报告自动生成 (AC: 5)
  - [ ] 创建src/services/comparison_report_generator.py文件
  - [ ] 实现ComparisonReportGenerator类，报告生成
  - [ ] 添加标准化比较报告模板
  - [ ] 实现关键发现的自动提取
  - [ ] 创建决策建议的智能生成
  - [ ] 添加报告内容的自定义配置

- [ ] 任务6：实现比较结果导出保存 (AC: 6)
  - [ ] 创建src/services/comparison_export_service.py文件
  - [ ] 实现ComparisonExportService类，导出服务
  - [ ] 添加比较表格的Excel导出
  - [ ] 实现比较图表的高质量图像导出
  - [ ] 创建完整比较报告的PDF导出
  - [ ] 添加比较配置的保存和重用

## Dev Notes

### ⚠️ 重要：功能整合说明

**本故事负责专业的场景比较分析**，包含：

- **成本效益分析图表**：气泡图、象限分析、策略比较（从 Story 1.7 移入）
- **统计比较分析**：t检验、卡方检验、效应量计算
- **比较结果可视化**：热图、雷达图、瀑布图、森林图
- **多策略并排比较**：完整的比较分析工作流

**依赖关系**：使用 Story 1.7 的基础图表组件和 Story 6.2 的交互功能。

### 场景比较数据结构
```python
from dataclasses import dataclass
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import numpy as np
from scipy import stats
from enum import Enum

class ComparisonMetric(Enum):
    TOTAL_COST = "total_cost"
    TOTAL_QALYS = "total_qalys"
    ICER = "icer"
    CANCERS_DETECTED = "cancers_detected"
    CANCER_DEATHS_PREVENTED = "cancer_deaths_prevented"
    SCREENING_PARTICIPATION = "screening_participation"
    FALSE_POSITIVES = "false_positives"
    LIFE_YEARS_GAINED = "life_years_gained"

@dataclass
class ComparisonResult:
    metric: ComparisonMetric
    strategy_values: Dict[str, float]
    statistical_test: Optional[str] = None
    p_value: Optional[float] = None
    effect_size: Optional[float] = None
    confidence_interval: Optional[Tuple[float, float]] = None
    interpretation: Optional[str] = None

@dataclass
class ScenarioComparison:
    comparison_name: str
    strategies: List[str]
    metrics: List[ComparisonMetric]
    results: Dict[ComparisonMetric, ComparisonResult]
    overall_ranking: List[Tuple[str, float]]  # (strategy_name, score)
    created_at: datetime
```

### 场景比较窗口实现
```python
class ScenarioComparisonWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.comparison_data = None
        self.selected_strategies = []
        self.selected_metrics = []
        self.statistical_comparator = StatisticalComparator()
        self.setup_ui()
        
    def setup_ui(self):
        """设置比较窗口界面"""
        self.setWindowTitle("筛查策略场景比较分析")
        self.setMinimumSize(1200, 800)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)
        
        # 主要比较区域
        self.comparison_area = QTabWidget()
        
        # 表格比较标签页
        self.table_tab = self.create_table_comparison_tab()
        self.comparison_area.addTab(self.table_tab, "表格比较")
        
        # 图表比较标签页
        self.chart_tab = self.create_chart_comparison_tab()
        self.comparison_area.addTab(self.chart_tab, "图表比较")
        
        # 统计分析标签页
        self.stats_tab = self.create_statistical_analysis_tab()
        self.comparison_area.addTab(self.stats_tab, "统计分析")
        
        layout.addWidget(self.comparison_area)
        
        # 状态栏
        self.statusBar().showMessage("准备进行场景比较分析")
    
    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QWidget()
        layout = QHBoxLayout(panel)
        
        # 策略选择
        strategy_group = QGroupBox("选择策略")
        strategy_layout = QVBoxLayout(strategy_group)
        
        self.strategy_list = QListWidget()
        self.strategy_list.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
        self.load_available_strategies()
        strategy_layout.addWidget(self.strategy_list)
        
        layout.addWidget(strategy_group)
        
        # 指标选择
        metrics_group = QGroupBox("比较指标")
        metrics_layout = QVBoxLayout(metrics_group)
        
        self.metrics_list = QListWidget()
        self.metrics_list.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
        self.load_comparison_metrics()
        metrics_layout.addWidget(self.metrics_list)
        
        layout.addWidget(metrics_group)
        
        # 控制按钮
        button_group = QGroupBox("操作")
        button_layout = QVBoxLayout(button_group)
        
        compare_btn = QPushButton("开始比较")
        compare_btn.clicked.connect(self.start_comparison)
        button_layout.addWidget(compare_btn)
        
        export_btn = QPushButton("导出结果")
        export_btn.clicked.connect(self.export_comparison)
        button_layout.addWidget(export_btn)
        
        save_btn = QPushButton("保存比较")
        save_btn.clicked.connect(self.save_comparison)
        button_layout.addWidget(save_btn)
        
        layout.addWidget(button_group)
        
        return panel
    
    def create_table_comparison_tab(self) -> QWidget:
        """创建表格比较标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 比较表格
        self.comparison_table = QTableWidget()
        self.comparison_table.setSortingEnabled(True)
        self.comparison_table.setAlternatingRowColors(True)
        layout.addWidget(self.comparison_table)
        
        return widget
    
    def start_comparison(self):
        """开始比较分析"""
        # 获取选中的策略和指标
        selected_strategies = self.get_selected_strategies()
        selected_metrics = self.get_selected_metrics()
        
        if len(selected_strategies) < 2:
            QMessageBox.warning(self, "警告", "请至少选择两个策略进行比较")
            return
        
        if not selected_metrics:
            QMessageBox.warning(self, "警告", "请至少选择一个比较指标")
            return
        
        # 执行比较分析
        self.statusBar().showMessage("正在进行比较分析...")
        
        try:
            comparison_results = self.perform_comparison_analysis(
                selected_strategies, selected_metrics
            )
            
            # 更新界面显示
            self.update_comparison_display(comparison_results)
            
            self.statusBar().showMessage("比较分析完成")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"比较分析失败: {str(e)}")
            self.statusBar().showMessage("比较分析失败")
    
    def perform_comparison_analysis(
        self, 
        strategies: List[str], 
        metrics: List[ComparisonMetric]
    ) -> ScenarioComparison:
        """执行比较分析"""
        
        # 加载策略数据
        strategy_data = {}
        for strategy in strategies:
            strategy_data[strategy] = self.load_strategy_data(strategy)
        
        # 计算比较结果
        comparison_results = {}
        
        for metric in metrics:
            # 提取各策略的指标值
            values = {}
            for strategy, data in strategy_data.items():
                values[strategy] = self.extract_metric_value(data, metric)
            
            # 进行统计比较
            stat_result = self.statistical_comparator.compare_strategies(
                values, metric
            )
            
            comparison_results[metric] = ComparisonResult(
                metric=metric,
                strategy_values=values,
                statistical_test=stat_result.get('test_name'),
                p_value=stat_result.get('p_value'),
                effect_size=stat_result.get('effect_size'),
                confidence_interval=stat_result.get('confidence_interval'),
                interpretation=stat_result.get('interpretation')
            )
        
        # 计算总体排名
        overall_ranking = self.calculate_overall_ranking(
            strategies, comparison_results
        )
        
        return ScenarioComparison(
            comparison_name=f"比较_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            strategies=strategies,
            metrics=metrics,
            results=comparison_results,
            overall_ranking=overall_ranking,
            created_at=datetime.now()
        )
```

### 统计比较分析器
```python
class StatisticalComparator:
    def __init__(self):
        self.alpha = 0.05  # 显著性水平
        
    def compare_strategies(
        self, 
        strategy_values: Dict[str, float], 
        metric: ComparisonMetric
    ) -> Dict[str, Any]:
        """比较策略间的统计差异"""
        
        strategies = list(strategy_values.keys())
        values = list(strategy_values.values())
        
        if len(strategies) == 2:
            # 两组比较
            return self.two_group_comparison(values[0], values[1], metric)
        else:
            # 多组比较
            return self.multiple_group_comparison(strategy_values, metric)
    
    def two_group_comparison(
        self, 
        group1_value: float, 
        group2_value: float, 
        metric: ComparisonMetric
    ) -> Dict[str, Any]:
        """两组比较分析"""
        
        # 这里简化处理，实际应该使用完整的数据分布
        # 假设我们有每个策略的多次模拟结果
        
        # 模拟数据分布（实际应从数据库获取）
        group1_data = np.random.normal(group1_value, group1_value * 0.1, 100)
        group2_data = np.random.normal(group2_value, group2_value * 0.1, 100)
        
        # t检验
        t_stat, p_value = stats.ttest_ind(group1_data, group2_data)
        
        # 效应量（Cohen's d）
        pooled_std = np.sqrt(((len(group1_data) - 1) * np.var(group1_data, ddof=1) + 
                             (len(group2_data) - 1) * np.var(group2_data, ddof=1)) / 
                            (len(group1_data) + len(group2_data) - 2))
        
        cohens_d = (np.mean(group1_data) - np.mean(group2_data)) / pooled_std
        
        # 置信区间
        diff_mean = np.mean(group1_data) - np.mean(group2_data)
        se_diff = pooled_std * np.sqrt(1/len(group1_data) + 1/len(group2_data))
        df = len(group1_data) + len(group2_data) - 2
        t_critical = stats.t.ppf(1 - self.alpha/2, df)
        
        ci_lower = diff_mean - t_critical * se_diff
        ci_upper = diff_mean + t_critical * se_diff
        
        # 解释结果
        interpretation = self.interpret_comparison_result(
            p_value, cohens_d, metric
        )
        
        return {
            'test_name': 't-test',
            'statistic': t_stat,
            'p_value': p_value,
            'effect_size': cohens_d,
            'confidence_interval': (ci_lower, ci_upper),
            'interpretation': interpretation
        }
    
    def multiple_group_comparison(
        self, 
        strategy_values: Dict[str, float], 
        metric: ComparisonMetric
    ) -> Dict[str, Any]:
        """多组比较分析（ANOVA）"""
        
        # 模拟每个策略的数据分布
        groups_data = []
        for strategy, value in strategy_values.items():
            group_data = np.random.normal(value, value * 0.1, 100)
            groups_data.append(group_data)
        
        # 单因素方差分析
        f_stat, p_value = stats.f_oneway(*groups_data)
        
        # 效应量（eta squared）
        ss_between = sum(len(group) * (np.mean(group) - np.mean(np.concatenate(groups_data)))**2 
                        for group in groups_data)
        ss_total = sum(np.sum((group - np.mean(np.concatenate(groups_data)))**2) 
                      for group in groups_data)
        eta_squared = ss_between / ss_total
        
        # 事后检验（Tukey HSD）
        post_hoc_results = self.tukey_hsd_test(strategy_values, groups_data)
        
        interpretation = self.interpret_anova_result(p_value, eta_squared, metric)
        
        return {
            'test_name': 'ANOVA',
            'statistic': f_stat,
            'p_value': p_value,
            'effect_size': eta_squared,
            'post_hoc': post_hoc_results,
            'interpretation': interpretation
        }
    
    def interpret_comparison_result(
        self, 
        p_value: float, 
        effect_size: float, 
        metric: ComparisonMetric
    ) -> str:
        """解释比较结果"""
        
        significance = "显著" if p_value < self.alpha else "不显著"
        
        # 效应量解释
        if abs(effect_size) < 0.2:
            effect_magnitude = "很小"
        elif abs(effect_size) < 0.5:
            effect_magnitude = "小"
        elif abs(effect_size) < 0.8:
            effect_magnitude = "中等"
        else:
            effect_magnitude = "大"
        
        direction = "更高" if effect_size > 0 else "更低"
        
        interpretation = (
            f"统计差异{significance} (p={p_value:.4f})，"
            f"效应量为{effect_magnitude} (d={effect_size:.3f})。"
        )
        
        if p_value < self.alpha:
            interpretation += f"策略间在{metric.value}指标上存在{effect_magnitude}的差异。"
        
        return interpretation
```

### 比较图表组件
```python
class ComparisonChartWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.comparison_data = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置图表界面"""
        layout = QVBoxLayout(self)
        
        # 图表类型选择
        chart_control = QHBoxLayout()
        chart_control.addWidget(QLabel("图表类型:"))
        
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems([
            "热图比较", "雷达图", "瀑布图", "森林图", "散点图矩阵"
        ])
        self.chart_type_combo.currentTextChanged.connect(self.update_chart)
        chart_control.addWidget(self.chart_type_combo)
        
        chart_control.addStretch()
        layout.addLayout(chart_control)
        
        # 图表显示区域
        self.chart_view = QWebEngineView()
        layout.addWidget(self.chart_view)
    
    def create_heatmap_comparison(self, comparison_data: ScenarioComparison) -> go.Figure:
        """创建热图比较"""
        
        # 准备数据矩阵
        strategies = comparison_data.strategies
        metrics = [m.value for m in comparison_data.metrics]
        
        # 标准化数据矩阵
        data_matrix = []
        for metric_enum in comparison_data.metrics:
            metric_result = comparison_data.results[metric_enum]
            row = []
            for strategy in strategies:
                value = metric_result.strategy_values[strategy]
                row.append(value)
            data_matrix.append(row)
        
        # 标准化到0-1范围
        data_matrix = np.array(data_matrix)
        normalized_matrix = (data_matrix - data_matrix.min(axis=1, keepdims=True)) / \
                           (data_matrix.max(axis=1, keepdims=True) - data_matrix.min(axis=1, keepdims=True))
        
        # 创建热图
        fig = go.Figure(data=go.Heatmap(
            z=normalized_matrix,
            x=strategies,
            y=metrics,
            colorscale='RdYlBu_r',
            text=data_matrix,
            texttemplate="%{text:.2f}",
            textfont={"size": 10},
            hoverongaps=False
        ))
        
        fig.update_layout(
            title="策略比较热图",
            xaxis_title="筛查策略",
            yaxis_title="比较指标"
        )
        
        return fig
    
    def create_radar_chart(self, comparison_data: ScenarioComparison) -> go.Figure:
        """创建雷达图比较"""
        
        fig = go.Figure()
        
        metrics = [m.value for m in comparison_data.metrics]
        
        for strategy in comparison_data.strategies:
            # 提取该策略的所有指标值
            values = []
            for metric_enum in comparison_data.metrics:
                metric_result = comparison_data.results[metric_enum]
                value = metric_result.strategy_values[strategy]
                values.append(value)
            
            # 标准化值到0-1范围（用于雷达图显示）
            normalized_values = self.normalize_values_for_radar(values, comparison_data.metrics)
            
            fig.add_trace(go.Scatterpolar(
                r=normalized_values,
                theta=metrics,
                fill='toself',
                name=strategy
            ))
        
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 1]
                )),
            showlegend=True,
            title="策略多维度比较雷达图"
        )
        
        return fig
    
    def create_forest_plot(self, comparison_data: ScenarioComparison) -> go.Figure:
        """创建森林图显示置信区间"""
        
        fig = go.Figure()
        
        y_pos = 0
        y_labels = []
        
        for metric_enum in comparison_data.metrics:
            metric_result = comparison_data.results[metric_enum]
            
            if metric_result.confidence_interval:
                ci_lower, ci_upper = metric_result.confidence_interval
                point_estimate = np.mean(list(metric_result.strategy_values.values()))
                
                # 添加置信区间线
                fig.add_trace(go.Scatter(
                    x=[ci_lower, ci_upper],
                    y=[y_pos, y_pos],
                    mode='lines',
                    line=dict(color='blue', width=3),
                    showlegend=False
                ))
                
                # 添加点估计
                fig.add_trace(go.Scatter(
                    x=[point_estimate],
                    y=[y_pos],
                    mode='markers',
                    marker=dict(color='red', size=8),
                    showlegend=False
                ))
                
                y_labels.append(metric_enum.value)
                y_pos += 1
        
        fig.update_layout(
            title="效应量森林图",
            xaxis_title="效应量",
            yaxis=dict(
                tickmode='array',
                tickvals=list(range(len(y_labels))),
                ticktext=y_labels
            )
        )
        
        return fig
```

### Testing
#### 测试文件位置
- `tests/unit/test_scenario_comparison.py`
- `tests/unit/test_statistical_comparator.py`
- `tests/unit/test_comparison_charts.py`
- `tests/integration/test_comparison_analysis.py`

#### 测试标准
- 多策略比较界面测试
- 统计检验准确性测试
- 比较图表生成测试
- 报告生成功能测试
- 导出功能完整性测试

#### 测试框架和模式
- 使用已知数据验证统计计算
- Mock策略数据测试比较功能
- 参数化测试验证不同比较场景
- 集成测试验证完整比较流程

#### 特定测试要求
- 统计检验准确性: 与标准统计软件结果一致
- 比较界面响应性: 所有操作 < 2秒
- 图表生成性能: 10个策略比较 < 5秒
- 报告生成完整性: 100%内容正确性

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-31 | 1.0 | 初始故事创建 | Scrum Master |

## Dev Agent Record
*此部分将由开发代理在实施过程中填写*

### Agent Model Used
*待填写*

### Debug Log References
*待填写*

### Completion Notes List
*待填写*

### File List
*待填写*

## QA Results
*此部分将由QA代理在完成故事实施的QA审查后填写*
