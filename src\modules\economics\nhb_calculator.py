"""
净健康效益（NHB）计算器

该模块实现了健康经济学分析中的净健康效益计算功能，包括：
- 净健康效益计算
- 货币化健康效益计算
- NHB的不确定性分析
- NHB排序和策略选择功能
- NHB结果可视化准备
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Union
from enum import Enum
import numpy as np
import pandas as pd
from scipy import stats
import logging

logger = logging.getLogger(__name__)


class NHBUnit(Enum):
    """NHB计算单位枚举"""
    QALY = "qaly"           # 质量调整生命年
    LYG = "lyg"             # 生命年获得
    DALY = "daly"           # 伤残调整生命年


@dataclass
class NHBResult:
    """净健康效益计算结果数据类"""
    strategy_name: str
    total_cost: float
    total_qalys: float
    willingness_to_pay_threshold: float
    net_health_benefit: float
    monetized_health_benefit: float
    incremental_nhb: Optional[float] = None
    nhb_rank: Optional[int] = None
    nhb_confidence_interval: Optional[Tuple[float, float]] = None
    probability_best: Optional[float] = None


@dataclass
class NHBComparisonResult:
    """NHB比较结果数据类"""
    strategies: List[NHBResult]
    ranking: List[str]
    best_strategy: str
    nhb_differences: Dict[str, float]
    uncertainty_analysis: Optional[Dict] = None


class NHBCalculator:
    """净健康效益计算器"""
    
    def __init__(self, wtp_threshold: float = 150000, unit: NHBUnit = NHBUnit.QALY):
        """
        初始化NHB计算器
        
        Args:
            wtp_threshold: 支付意愿阈值（元/QALY）
            unit: 健康效益计算单位
        """
        self.wtp_threshold = wtp_threshold
        self.unit = unit
        logger.info(f"NHB计算器初始化完成，阈值: {wtp_threshold:,.0f}元/{unit.value.upper()}")
    
    def calculate_nhb(
        self, 
        total_cost: float, 
        total_qalys: float,
        strategy_name: str = "strategy"
    ) -> NHBResult:
        """
        计算净健康效益
        
        Args:
            total_cost: 总成本
            total_qalys: 总QALYs
            strategy_name: 策略名称
            
        Returns:
            NHBResult: NHB计算结果
        """
        logger.debug(f"计算{strategy_name}的净健康效益")
        
        # 计算货币化健康效益
        monetized_health_benefit = total_qalys * self.wtp_threshold
        
        # 计算净健康效益 (以健康单位表示)
        # NHB = 健康效益 - 成本/支付意愿阈值
        net_health_benefit = total_qalys - (total_cost / self.wtp_threshold)
        
        result = NHBResult(
            strategy_name=strategy_name,
            total_cost=total_cost,
            total_qalys=total_qalys,
            willingness_to_pay_threshold=self.wtp_threshold,
            net_health_benefit=net_health_benefit,
            monetized_health_benefit=monetized_health_benefit
        )
        
        logger.info(f"{strategy_name}的NHB: {net_health_benefit:.4f} {self.unit.value.upper()}")
        return result
    
    def calculate_incremental_nhb(
        self, 
        intervention_cost: float, 
        intervention_qalys: float,
        comparator_cost: float, 
        comparator_qalys: float,
        intervention_name: str = "intervention",
        comparator_name: str = "comparator"
    ) -> Dict[str, Union[NHBResult, float]]:
        """
        计算增量净健康效益
        
        Args:
            intervention_cost: 干预策略成本
            intervention_qalys: 干预策略QALYs
            comparator_cost: 对照策略成本
            comparator_qalys: 对照策略QALYs
            intervention_name: 干预策略名称
            comparator_name: 对照策略名称
            
        Returns:
            Dict: 增量NHB分析结果
        """
        logger.debug(f"计算增量NHB: {intervention_name} vs {comparator_name}")
        
        # 计算各策略的NHB
        intervention_nhb = self.calculate_nhb(
            intervention_cost, intervention_qalys, intervention_name
        )
        comparator_nhb = self.calculate_nhb(
            comparator_cost, comparator_qalys, comparator_name
        )
        
        # 计算增量NHB
        incremental_nhb = intervention_nhb.net_health_benefit - comparator_nhb.net_health_benefit
        
        # 更新干预策略结果
        intervention_nhb.incremental_nhb = incremental_nhb
        
        result = {
            'intervention_nhb': intervention_nhb,
            'comparator_nhb': comparator_nhb,
            'incremental_nhb': incremental_nhb,
            'nhb_positive': incremental_nhb > 0,
            'incremental_cost': intervention_cost - comparator_cost,
            'incremental_qalys': intervention_qalys - comparator_qalys
        }
        
        logger.info(f"增量NHB: {incremental_nhb:.4f} {self.unit.value.upper()}")
        return result
    
    def rank_strategies_by_nhb(
        self, 
        strategies: List[Dict[str, Union[str, float]]]
    ) -> NHBComparisonResult:
        """
        按NHB排序策略
        
        Args:
            strategies: 策略列表，包含name, cost, qalys字段
            
        Returns:
            NHBComparisonResult: NHB比较结果
        """
        logger.debug(f"开始对{len(strategies)}个策略进行NHB排序")
        
        # 计算每个策略的NHB
        nhb_results = []
        for i, strategy in enumerate(strategies):
            nhb_result = self.calculate_nhb(
                strategy['cost'], 
                strategy['qalys'], 
                strategy['name']
            )
            nhb_result.nhb_rank = 0  # 临时设置，将在排序后更新
            nhb_results.append(nhb_result)
        
        # 按NHB降序排序
        nhb_results.sort(key=lambda x: x.net_health_benefit, reverse=True)
        
        # 更新排名
        for i, result in enumerate(nhb_results):
            result.nhb_rank = i + 1
        
        # 生成排名列表和最佳策略
        ranking = [result.strategy_name for result in nhb_results]
        best_strategy = ranking[0] if ranking else ""
        
        # 计算与最佳策略的NHB差异
        best_nhb = nhb_results[0].net_health_benefit if nhb_results else 0
        nhb_differences = {
            result.strategy_name: best_nhb - result.net_health_benefit
            for result in nhb_results
        }
        
        comparison_result = NHBComparisonResult(
            strategies=nhb_results,
            ranking=ranking,
            best_strategy=best_strategy,
            nhb_differences=nhb_differences
        )
        
        logger.info(f"NHB排序完成，最佳策略: {best_strategy}")
        return comparison_result
    
    def perform_nhb_uncertainty_analysis(
        self,
        cost_samples: np.ndarray,
        qaly_samples: np.ndarray,
        strategy_name: str = "strategy",
        confidence_level: float = 0.95
    ) -> Dict[str, Union[float, Tuple[float, float], np.ndarray]]:
        """
        执行NHB不确定性分析
        
        Args:
            cost_samples: 成本样本数组
            qaly_samples: QALY样本数组
            strategy_name: 策略名称
            confidence_level: 置信水平
            
        Returns:
            Dict: 不确定性分析结果
        """
        logger.debug(f"开始{strategy_name}的NHB不确定性分析")
        
        if len(cost_samples) != len(qaly_samples):
            raise ValueError("成本样本和QALY样本数量必须相等")
        
        # 计算NHB样本
        nhb_samples = qaly_samples - (cost_samples / self.wtp_threshold)
        
        # 计算统计指标
        mean_nhb = np.mean(nhb_samples)
        std_nhb = np.std(nhb_samples)
        median_nhb = np.median(nhb_samples)
        
        # 计算置信区间
        alpha = 1 - confidence_level
        ci_lower = np.percentile(nhb_samples, (alpha / 2) * 100)
        ci_upper = np.percentile(nhb_samples, (1 - alpha / 2) * 100)
        
        # 计算NHB > 0的概率
        prob_positive = np.mean(nhb_samples > 0)
        
        # 计算分位数
        percentiles = [5, 10, 25, 75, 90, 95]
        percentile_values = {
            f'p{p}': np.percentile(nhb_samples, p) for p in percentiles
        }
        
        result = {
            'strategy_name': strategy_name,
            'mean_nhb': mean_nhb,
            'std_nhb': std_nhb,
            'median_nhb': median_nhb,
            'confidence_interval': (ci_lower, ci_upper),
            'confidence_level': confidence_level,
            'probability_positive': prob_positive,
            'percentiles': percentile_values,
            'nhb_samples': nhb_samples,
            'sample_size': len(nhb_samples)
        }
        
        logger.info(f"NHB不确定性分析完成，平均NHB: {mean_nhb:.4f} ± {std_nhb:.4f}")
        return result
    
    def compare_strategies_with_uncertainty(
        self,
        strategies_data: List[Dict[str, Union[str, np.ndarray]]],
        confidence_level: float = 0.95
    ) -> Dict[str, Union[List, Dict]]:
        """
        在不确定性条件下比较多个策略
        
        Args:
            strategies_data: 策略数据列表，包含name, cost_samples, qaly_samples
            confidence_level: 置信水平
            
        Returns:
            Dict: 策略比较结果
        """
        logger.debug(f"开始不确定性条件下的策略比较，共{len(strategies_data)}个策略")
        
        # 计算每个策略的NHB不确定性分析
        uncertainty_results = {}
        nhb_samples_dict = {}
        
        for strategy in strategies_data:
            uncertainty_result = self.perform_nhb_uncertainty_analysis(
                strategy['cost_samples'],
                strategy['qaly_samples'],
                strategy['name'],
                confidence_level
            )
            uncertainty_results[strategy['name']] = uncertainty_result
            nhb_samples_dict[strategy['name']] = uncertainty_result['nhb_samples']
        
        # 计算每个策略成为最佳选择的概率
        n_samples = len(list(nhb_samples_dict.values())[0])
        strategy_names = list(nhb_samples_dict.keys())
        
        best_probabilities = {name: 0 for name in strategy_names}
        
        for i in range(n_samples):
            # 找到第i次模拟中NHB最高的策略
            sample_nhb = {name: nhb_samples_dict[name][i] for name in strategy_names}
            best_strategy = max(sample_nhb, key=sample_nhb.get)
            best_probabilities[best_strategy] += 1
        
        # 转换为概率
        for name in strategy_names:
            best_probabilities[name] /= n_samples
        
        # 按平均NHB排序
        mean_nhb_ranking = sorted(
            strategy_names,
            key=lambda x: uncertainty_results[x]['mean_nhb'],
            reverse=True
        )
        
        # 按成为最佳选择概率排序
        prob_ranking = sorted(
            strategy_names,
            key=lambda x: best_probabilities[x],
            reverse=True
        )
        
        result = {
            'uncertainty_results': uncertainty_results,
            'best_probabilities': best_probabilities,
            'mean_nhb_ranking': mean_nhb_ranking,
            'probability_ranking': prob_ranking,
            'most_likely_best': prob_ranking[0] if prob_ranking else "",
            'highest_mean_nhb': mean_nhb_ranking[0] if mean_nhb_ranking else "",
            'confidence_level': confidence_level
        }
        
        logger.info(f"不确定性策略比较完成，最可能最佳策略: {result['most_likely_best']}")
        return result
    
    def calculate_expected_value_of_perfect_information(
        self,
        strategies_data: List[Dict[str, Union[str, np.ndarray]]]
    ) -> Dict[str, float]:
        """
        计算完全信息的期望价值 (EVPI)
        
        Args:
            strategies_data: 策略数据列表
            
        Returns:
            Dict: EVPI分析结果
        """
        logger.debug("开始计算完全信息的期望价值")
        
        # 计算每个策略的NHB样本
        nhb_samples_dict = {}
        for strategy in strategies_data:
            nhb_samples = strategy['qaly_samples'] - (strategy['cost_samples'] / self.wtp_threshold)
            nhb_samples_dict[strategy['name']] = nhb_samples
        
        n_samples = len(list(nhb_samples_dict.values())[0])
        strategy_names = list(nhb_samples_dict.keys())
        
        # 计算当前最佳策略的期望NHB
        mean_nhb = {name: np.mean(nhb_samples_dict[name]) for name in strategy_names}
        current_best = max(mean_nhb, key=mean_nhb.get)
        expected_nhb_current = mean_nhb[current_best]
        
        # 计算完全信息下的期望NHB
        perfect_info_nhb = 0
        for i in range(n_samples):
            # 在第i次模拟中找到最佳策略
            sample_nhb = {name: nhb_samples_dict[name][i] for name in strategy_names}
            best_nhb_i = max(sample_nhb.values())
            perfect_info_nhb += best_nhb_i
        
        expected_nhb_perfect = perfect_info_nhb / n_samples
        
        # 计算EVPI
        evpi_health = expected_nhb_perfect - expected_nhb_current
        evpi_monetary = evpi_health * self.wtp_threshold
        
        result = {
            'evpi_health_units': evpi_health,
            'evpi_monetary': evpi_monetary,
            'expected_nhb_current': expected_nhb_current,
            'expected_nhb_perfect': expected_nhb_perfect,
            'current_best_strategy': current_best,
            'wtp_threshold': self.wtp_threshold
        }
        
        logger.info(f"EVPI计算完成: {evpi_health:.4f} {self.unit.value.upper()} ({evpi_monetary:,.0f}元)")
        return result
    
    def generate_nhb_visualization_data(
        self,
        comparison_result: NHBComparisonResult,
        uncertainty_results: Optional[Dict] = None
    ) -> Dict[str, Union[List, Dict]]:
        """
        生成NHB可视化数据
        
        Args:
            comparison_result: NHB比较结果
            uncertainty_results: 不确定性分析结果
            
        Returns:
            Dict: 可视化数据
        """
        logger.debug("生成NHB可视化数据")
        
        # 基础条形图数据
        bar_chart_data = {
            'strategy_names': [r.strategy_name for r in comparison_result.strategies],
            'nhb_values': [r.net_health_benefit for r in comparison_result.strategies],
            'costs': [r.total_cost for r in comparison_result.strategies],
            'qalys': [r.total_qalys for r in comparison_result.strategies],
            'ranks': [r.nhb_rank for r in comparison_result.strategies]
        }
        
        # 散点图数据（成本vs效果，气泡大小表示NHB）
        scatter_data = {
            'x_costs': bar_chart_data['costs'],
            'y_qalys': bar_chart_data['qalys'],
            'bubble_sizes': [abs(nhb) * 100 for nhb in bar_chart_data['nhb_values']],
            'colors': ['green' if nhb > 0 else 'red' for nhb in bar_chart_data['nhb_values']],
            'labels': bar_chart_data['strategy_names']
        }
        
        visualization_data = {
            'bar_chart': bar_chart_data,
            'scatter_plot': scatter_data,
            'ranking_table': {
                'strategies': comparison_result.ranking,
                'nhb_values': [
                    next(r.net_health_benefit for r in comparison_result.strategies 
                         if r.strategy_name == name)
                    for name in comparison_result.ranking
                ],
                'nhb_differences': [
                    comparison_result.nhb_differences[name]
                    for name in comparison_result.ranking
                ]
            }
        }
        
        # 添加不确定性数据
        if uncertainty_results:
            uncertainty_viz = {
                'error_bars': {
                    'strategy_names': list(uncertainty_results.keys()),
                    'mean_values': [r['mean_nhb'] for r in uncertainty_results.values()],
                    'std_values': [r['std_nhb'] for r in uncertainty_results.values()],
                    'ci_lower': [r['confidence_interval'][0] for r in uncertainty_results.values()],
                    'ci_upper': [r['confidence_interval'][1] for r in uncertainty_results.values()]
                },
                'probability_chart': uncertainty_results.get('best_probabilities', {})
            }
            visualization_data['uncertainty'] = uncertainty_viz
        
        logger.info("NHB可视化数据生成完成")
        return visualization_data
    
    def update_wtp_threshold(self, new_threshold: float) -> None:
        """
        更新支付意愿阈值
        
        Args:
            new_threshold: 新的支付意愿阈值
        """
        old_threshold = self.wtp_threshold
        self.wtp_threshold = new_threshold
        logger.info(f"支付意愿阈值已更新: {old_threshold:,.0f} -> {new_threshold:,.0f}元/{self.unit.value.upper()}")
    
    def export_nhb_results(
        self,
        comparison_result: NHBComparisonResult,
        uncertainty_results: Optional[Dict] = None,
        file_format: str = "csv"
    ) -> Dict[str, Union[str, pd.DataFrame]]:
        """
        导出NHB分析结果
        
        Args:
            comparison_result: NHB比较结果
            uncertainty_results: 不确定性分析结果
            file_format: 导出格式 ("csv", "excel", "json")
            
        Returns:
            Dict: 导出结果
        """
        logger.debug(f"导出NHB结果，格式: {file_format}")
        
        # 创建主要结果DataFrame
        main_results = pd.DataFrame([
            {
                '策略名称': r.strategy_name,
                '总成本': r.total_cost,
                '总QALYs': r.total_qalys,
                '净健康效益': r.net_health_benefit,
                '货币化健康效益': r.monetized_health_benefit,
                '排名': r.nhb_rank,
                '支付意愿阈值': r.willingness_to_pay_threshold
            }
            for r in comparison_result.strategies
        ])
        
        export_data = {
            'main_results': main_results,
            'ranking': pd.DataFrame({
                '排名': range(1, len(comparison_result.ranking) + 1),
                '策略名称': comparison_result.ranking,
                'NHB差异': [comparison_result.nhb_differences[name] for name in comparison_result.ranking]
            })
        }
        
        # 添加不确定性结果
        if uncertainty_results:
            uncertainty_df = pd.DataFrame([
                {
                    '策略名称': name,
                    '平均NHB': results['mean_nhb'],
                    'NHB标准差': results['std_nhb'],
                    'NHB中位数': results['median_nhb'],
                    '置信区间下限': results['confidence_interval'][0],
                    '置信区间上限': results['confidence_interval'][1],
                    'NHB>0概率': results['probability_positive']
                }
                for name, results in uncertainty_results.get('uncertainty_results', {}).items()
            ])
            export_data['uncertainty_analysis'] = uncertainty_df
        
        logger.info(f"NHB结果导出完成，包含{len(export_data)}个数据表")
        return export_data