"""
模型管理器模块单元测试
"""

import pytest
import numpy as np
import tempfile
import os
import json
import shutil
from unittest.mock import Mock, patch, MagicMock
import tensorflow as tf
from tensorflow import keras
from datetime import datetime

from src.calibration.model_manager import (
    Model<PERSON>anager, ModelManagerConfig, ModelMetadata
)


class TestModelMetadata:
    """ModelMetadata测试类"""
    
    def test_default_metadata(self):
        """测试默认元数据"""
        metadata = ModelMetadata(
            model_name="test_model",
            version="1.0",
            created_date="2025-08-12"
        )
        
        assert metadata.model_name == "test_model"
        assert metadata.version == "1.0"
        assert metadata.created_date == "2025-08-12"
        assert metadata.framework == "TensorFlow/Keras"
        assert metadata.model_type == "深度神经网络"
        assert metadata.total_params == 0
        assert metadata.performance_metrics == {}
        assert metadata.tags == []
    
    def test_custom_metadata(self):
        """测试自定义元数据"""
        metadata = ModelMetadata(
            model_name="custom_model",
            version="2.1",
            created_date="2025-08-12",
            input_shape=(None, 10),
            output_shape=(None, 5),
            total_params=1000,
            performance_metrics={"mse": 0.1, "mae": 0.05},
            tags=["calibration", "production"]
        )
        
        assert metadata.input_shape == (None, 10)
        assert metadata.output_shape == (None, 5)
        assert metadata.total_params == 1000
        assert metadata.performance_metrics["mse"] == 0.1
        assert "calibration" in metadata.tags


class TestModelManagerConfig:
    """ModelManagerConfig测试类"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = ModelManagerConfig()
        
        assert config.base_dir == "models"
        assert config.backup_dir == "models/backups"
        assert config.temp_dir == "models/temp"
        assert config.max_versions == 10
        assert config.auto_backup is True
        assert config.compression is True
        assert config.checksum_validation is True
        assert config.metadata_format == "json"
        assert config.model_format == "h5"
        assert config.include_optimizer is False
        assert config.save_weights_only is False
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = ModelManagerConfig(
            base_dir="custom_models",
            max_versions=5,
            auto_backup=False,
            model_format="savedmodel"
        )
        
        assert config.base_dir == "custom_models"
        assert config.max_versions == 5
        assert config.auto_backup is False
        assert config.model_format == "savedmodel"


class TestModelManager:
    """ModelManager测试类"""
    
    def setup_method(self):
        """测试前设置"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建配置
        self.config = ModelManagerConfig(
            base_dir=os.path.join(self.temp_dir, "models"),
            backup_dir=os.path.join(self.temp_dir, "backups"),
            temp_dir=os.path.join(self.temp_dir, "temp")
        )
        
        # 创建管理器
        self.manager = ModelManager(self.config)
        
        # 创建测试模型
        self.model = keras.Sequential([
            keras.layers.Dense(10, activation='relu', input_shape=(5,)),
            keras.layers.Dense(3, activation='linear')
        ])
        self.model.compile(optimizer='adam', loss='mse', metrics=['mae'])
    
    def teardown_method(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_initialization(self):
        """测试初始化"""
        assert self.manager.config == self.config
        assert self.manager.models_registry == {}
        
        # 检查目录是否创建
        assert os.path.exists(self.config.base_dir)
        assert os.path.exists(self.config.backup_dir)
        assert os.path.exists(self.config.temp_dir)
    
    def test_save_model_basic(self):
        """测试基本模型保存"""
        model_info = self.manager.save_model(
            model=self.model,
            model_name="test_model",
            version="1.0"
        )
        
        assert model_info is not None
        assert model_info['model_name'] == "test_model"
        assert model_info['version'] == "1.0"
        assert 'model_path' in model_info
        assert 'metadata_path' in model_info
        
        # 检查文件是否存在
        assert os.path.exists(model_info['model_path'])
        assert os.path.exists(model_info['metadata_path'])
    
    def test_save_model_with_metadata(self):
        """测试带元数据的模型保存"""
        metadata = {
            'description': 'Test model for calibration',
            'author': 'Test User',
            'performance_metrics': {'mse': 0.1, 'mae': 0.05}
        }
        
        model_info = self.manager.save_model(
            model=self.model,
            model_name="test_model_meta",
            version="1.0",
            metadata=metadata
        )
        
        assert model_info is not None
        
        # 加载元数据验证
        with open(model_info['metadata_path'], 'r', encoding='utf-8') as f:
            saved_metadata = json.load(f)
        
        assert saved_metadata['description'] == 'Test model for calibration'
        assert saved_metadata['author'] == 'Test User'
        assert saved_metadata['performance_metrics']['mse'] == 0.1
    
    def test_save_model_auto_version(self):
        """测试自动版本号"""
        # 保存第一个版本
        model_info1 = self.manager.save_model(
            model=self.model,
            model_name="auto_version_model"
        )
        
        # 保存第二个版本
        model_info2 = self.manager.save_model(
            model=self.model,
            model_name="auto_version_model"
        )
        
        assert model_info1['version'] == "1.0"
        assert model_info2['version'] == "1.1"
    
    def test_save_model_overwrite_protection(self):
        """测试覆盖保护"""
        # 保存第一次
        self.manager.save_model(
            model=self.model,
            model_name="protected_model",
            version="1.0"
        )
        
        # 尝试再次保存相同版本（应该失败）
        with pytest.raises(ValueError, match="模型版本已存在"):
            self.manager.save_model(
                model=self.model,
                model_name="protected_model",
                version="1.0",
                overwrite=False
            )
    
    def test_save_model_with_overwrite(self):
        """测试允许覆盖"""
        # 保存第一次
        model_info1 = self.manager.save_model(
            model=self.model,
            model_name="overwrite_model",
            version="1.0"
        )
        
        # 覆盖保存
        model_info2 = self.manager.save_model(
            model=self.model,
            model_name="overwrite_model",
            version="1.0",
            overwrite=True
        )
        
        assert model_info1['model_path'] == model_info2['model_path']
    
    def test_load_model_basic(self):
        """测试基本模型加载"""
        # 先保存模型
        model_info = self.manager.save_model(
            model=self.model,
            model_name="load_test_model",
            version="1.0"
        )
        
        # 加载模型
        loaded_result = self.manager.load_model(
            model_name="load_test_model",
            version="1.0"
        )
        
        assert loaded_result is not None
        assert 'model' in loaded_result
        assert 'metadata' in loaded_result
        assert 'scalers' in loaded_result
        
        # 验证模型可以预测
        loaded_model = loaded_result['model']
        test_input = np.random.randn(1, 5)
        prediction = loaded_model.predict(test_input, verbose=0)
        assert prediction.shape == (1, 3)
    
    def test_load_model_latest_version(self):
        """测试加载最新版本"""
        # 保存多个版本
        self.manager.save_model(self.model, "multi_version_model", "1.0")
        self.manager.save_model(self.model, "multi_version_model", "1.1")
        self.manager.save_model(self.model, "multi_version_model", "2.0")
        
        # 加载最新版本
        loaded_result = self.manager.load_model("multi_version_model")
        
        assert loaded_result is not None
        # 应该加载版本2.0
        metadata = loaded_result['metadata']
        assert metadata.version == "2.0"
    
    def test_load_model_not_found(self):
        """测试加载不存在的模型"""
        result = self.manager.load_model("nonexistent_model")
        assert result is None
    
    def test_list_models(self):
        """测试列出模型"""
        # 保存几个模型
        self.manager.save_model(self.model, "model_a", "1.0")
        self.manager.save_model(self.model, "model_b", "1.0")
        self.manager.save_model(self.model, "model_a", "1.1")
        
        models_list = self.manager.list_models()
        
        assert len(models_list) >= 2
        model_names = [m['model_name'] for m in models_list]
        assert "model_a" in model_names
        assert "model_b" in model_names
    
    def test_list_model_versions(self):
        """测试列出模型版本"""
        # 保存多个版本
        self.manager.save_model(self.model, "versioned_model", "1.0")
        self.manager.save_model(self.model, "versioned_model", "1.1")
        self.manager.save_model(self.model, "versioned_model", "2.0")
        
        versions = self.manager.list_model_versions("versioned_model")
        
        assert len(versions) == 3
        version_numbers = [v['version'] for v in versions]
        assert "1.0" in version_numbers
        assert "1.1" in version_numbers
        assert "2.0" in version_numbers
    
    def test_delete_model(self):
        """测试删除模型"""
        # 保存模型
        model_info = self.manager.save_model(
            self.model, "delete_test_model", "1.0"
        )
        
        # 验证文件存在
        assert os.path.exists(model_info['model_path'])
        
        # 删除模型
        success = self.manager.delete_model("delete_test_model", "1.0")
        
        assert success is True
        # 验证文件已删除
        assert not os.path.exists(model_info['model_path'])
    
    def test_delete_model_not_found(self):
        """测试删除不存在的模型"""
        success = self.manager.delete_model("nonexistent_model", "1.0")
        assert success is False
    
    def test_backup_model(self):
        """测试模型备份"""
        # 保存模型
        model_info = self.manager.save_model(
            self.model, "backup_test_model", "1.0"
        )
        
        # 备份模型
        backup_path = self.manager.backup_model("backup_test_model", "1.0")
        
        assert backup_path is not None
        assert os.path.exists(backup_path)
        assert backup_path.endswith('.zip')
    
    def test_restore_model(self):
        """测试模型恢复"""
        # 保存和备份模型
        self.manager.save_model(self.model, "restore_test_model", "1.0")
        backup_path = self.manager.backup_model("restore_test_model", "1.0")
        
        # 删除原模型
        self.manager.delete_model("restore_test_model", "1.0")
        
        # 恢复模型
        success = self.manager.restore_model(backup_path)
        
        assert success is True
        
        # 验证模型可以加载
        loaded_result = self.manager.load_model("restore_test_model", "1.0")
        assert loaded_result is not None
    
    def test_get_model_info(self):
        """测试获取模型信息"""
        # 保存模型
        self.manager.save_model(
            self.model, "info_test_model", "1.0",
            metadata={'description': 'Test model info'}
        )
        
        # 获取信息
        info = self.manager.get_model_info("info_test_model", "1.0")
        
        assert info is not None
        assert info.model_name == "info_test_model"
        assert info.version == "1.0"
        assert info.description == "Test model info"
    
    def test_compare_models(self):
        """测试模型比较"""
        # 保存两个模型
        self.manager.save_model(self.model, "compare_model_1", "1.0")
        self.manager.save_model(self.model, "compare_model_2", "1.0")
        
        # 比较模型
        comparison = self.manager.compare_models(
            ("compare_model_1", "1.0"),
            ("compare_model_2", "1.0")
        )
        
        assert comparison is not None
        assert 'model1_info' in comparison
        assert 'model2_info' in comparison
        assert 'comparison_metrics' in comparison
    
    def test_cleanup_old_versions(self):
        """测试清理旧版本"""
        # 保存超过最大版本数的模型
        for i in range(15):  # 超过默认的max_versions=10
            self.manager.save_model(
                self.model, "cleanup_test_model", f"1.{i}"
            )
        
        # 清理旧版本
        cleaned_count = self.manager.cleanup_old_versions("cleanup_test_model")
        
        assert cleaned_count > 0
        
        # 验证剩余版本数不超过限制
        versions = self.manager.list_model_versions("cleanup_test_model")
        assert len(versions) <= self.config.max_versions
    
    def test_export_model(self):
        """测试模型导出"""
        # 保存模型
        self.manager.save_model(self.model, "export_test_model", "1.0")
        
        # 导出模型
        export_path = os.path.join(self.temp_dir, "exported_model.zip")
        success = self.manager.export_model(
            "export_test_model", "1.0", export_path
        )
        
        assert success is True
        assert os.path.exists(export_path)
    
    def test_import_model(self):
        """测试模型导入"""
        # 先导出一个模型
        self.manager.save_model(self.model, "import_source_model", "1.0")
        export_path = os.path.join(self.temp_dir, "import_test.zip")
        self.manager.export_model("import_source_model", "1.0", export_path)
        
        # 导入模型
        success = self.manager.import_model(
            export_path, "import_target_model", "1.0"
        )
        
        assert success is True
        
        # 验证导入的模型可以加载
        loaded_result = self.manager.load_model("import_target_model", "1.0")
        assert loaded_result is not None


if __name__ == "__main__":
    pytest.main([__file__])
