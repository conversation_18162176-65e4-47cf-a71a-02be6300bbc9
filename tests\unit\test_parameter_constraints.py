"""
参数约束系统单元测试
"""

import unittest
import numpy as np
import tempfile
import json
import os
from unittest.mock import patch, MagicMock

from src.calibration.parameter_sampler import ParameterDefinition, SamplingConfig
from src.calibration.parameter_constraints import (
    ConstraintDefinition, ParameterConstraints, ConstraintValidator
)


class TestConstraintDefinition(unittest.TestCase):
    """约束定义测试类"""
    
    def test_constraint_definition_creation(self):
        """测试约束定义创建"""
        constraint = ConstraintDefinition(
            name="linear_constraint",
            constraint_type="linear",
            parameters=["param1", "param2"],
            coefficients=[1.0, 2.0],
            bound=5.0,
            operator="le",
            description="线性约束测试"
        )
        
        self.assertEqual(constraint.name, "linear_constraint")
        self.assertEqual(constraint.constraint_type, "linear")
        self.assertEqual(constraint.parameters, ["param1", "param2"])
        self.assertEqual(constraint.coefficients, [1.0, 2.0])
        self.assertEqual(constraint.bound, 5.0)
        self.assertEqual(constraint.operator, "le")
        self.assertEqual(constraint.description, "线性约束测试")
    
    def test_ratio_constraint_definition(self):
        """测试比例约束定义"""
        constraint = ConstraintDefinition(
            name="ratio_constraint",
            constraint_type="ratio",
            parameters=["param1", "param2"],
            bound=2.0,
            operator="le"
        )
        
        self.assertEqual(constraint.constraint_type, "ratio")
        self.assertEqual(len(constraint.parameters), 2)
        self.assertEqual(constraint.bound, 2.0)
    
    def test_custom_constraint_definition(self):
        """测试自定义约束定义"""
        function_code = """
def constraint_function(samples):
    return samples[:, 0] + samples[:, 1] <= 1.0
"""
        
        constraint = ConstraintDefinition(
            name="custom_constraint",
            constraint_type="custom",
            parameters=["param1", "param2"],
            function_code=function_code
        )
        
        self.assertEqual(constraint.constraint_type, "custom")
        self.assertIsNotNone(constraint.function_code)


class TestParameterConstraints(unittest.TestCase):
    """参数约束管理器测试类"""
    
    def setUp(self):
        """设置测试数据"""
        # 创建参数定义
        self.params = [
            ParameterDefinition("param1", 0.0, 10.0),
            ParameterDefinition("param2", 0.0, 10.0),
            ParameterDefinition("param3", 1.0, 5.0)
        ]
        
        self.config = SamplingConfig(
            parameters=self.params,
            n_samples=100,
            random_seed=42
        )
        
        # 创建约束定义
        self.constraints = [
            ConstraintDefinition(
                name="linear_constraint",
                constraint_type="linear",
                parameters=["param1", "param2"],
                coefficients=[1.0, 1.0],
                bound=15.0,
                operator="le"
            ),
            ConstraintDefinition(
                name="ratio_constraint",
                constraint_type="ratio",
                parameters=["param1", "param2"],
                bound=3.0,
                operator="le"
            ),
            ConstraintDefinition(
                name="boundary_constraint",
                constraint_type="boundary",
                parameters=["param1", "param2", "param3"]
            )
        ]
        
        self.constraint_manager = ParameterConstraints(self.constraints, self.config)
    
    def test_constraint_manager_initialization(self):
        """测试约束管理器初始化"""
        self.assertEqual(len(self.constraint_manager.constraints), 3)
        self.assertEqual(len(self.constraint_manager.parameter_names), 3)
        self.assertEqual(len(self.constraint_manager.constraint_functions), 3)
        
        # 检查参数索引映射
        self.assertEqual(self.constraint_manager.parameter_indices["param1"], 0)
        self.assertEqual(self.constraint_manager.parameter_indices["param2"], 1)
        self.assertEqual(self.constraint_manager.parameter_indices["param3"], 2)
    
    def test_linear_constraint_creation(self):
        """测试线性约束创建"""
        linear_constraint = self.constraints[0]
        constraint_func = self.constraint_manager._create_linear_constraint(linear_constraint)
        
        # 测试样本：param1=3, param2=4 -> 3+4=7 <= 15 (满足)
        test_samples = np.array([[3.0, 4.0, 2.0]])
        result = constraint_func(test_samples)
        self.assertTrue(result[0])
        
        # 测试样本：param1=10, param2=8 -> 10+8=18 > 15 (不满足)
        test_samples = np.array([[10.0, 8.0, 2.0]])
        result = constraint_func(test_samples)
        self.assertFalse(result[0])
    
    def test_linear_constraint_operators(self):
        """测试线性约束不同操作符"""
        # 测试 >= 操作符
        ge_constraint = ConstraintDefinition(
            name="ge_constraint",
            constraint_type="linear",
            parameters=["param1", "param2"],
            coefficients=[1.0, 1.0],
            bound=5.0,
            operator="ge"
        )
        
        constraint_func = self.constraint_manager._create_linear_constraint(ge_constraint)
        
        # 测试样本：3+4=7 >= 5 (满足)
        test_samples = np.array([[3.0, 4.0, 2.0]])
        result = constraint_func(test_samples)
        self.assertTrue(result[0])
        
        # 测试样本：1+2=3 < 5 (不满足)
        test_samples = np.array([[1.0, 2.0, 2.0]])
        result = constraint_func(test_samples)
        self.assertFalse(result[0])
    
    def test_ratio_constraint_creation(self):
        """测试比例约束创建"""
        ratio_constraint = self.constraints[1]
        constraint_func = self.constraint_manager._create_ratio_constraint(ratio_constraint)
        
        # 测试样本：param1/param2 = 6/3 = 2 <= 3 (满足)
        test_samples = np.array([[6.0, 3.0, 2.0]])
        result = constraint_func(test_samples)
        self.assertTrue(result[0])
        
        # 测试样本：param1/param2 = 10/2 = 5 > 3 (不满足)
        test_samples = np.array([[10.0, 2.0, 2.0]])
        result = constraint_func(test_samples)
        self.assertFalse(result[0])
    
    def test_ratio_constraint_zero_division(self):
        """测试比例约束除零处理"""
        ratio_constraint = self.constraints[1]
        constraint_func = self.constraint_manager._create_ratio_constraint(ratio_constraint)
        
        # 测试除零情况
        test_samples = np.array([[5.0, 0.0, 2.0]])
        result = constraint_func(test_samples)
        
        # 应该能处理除零情况而不抛出异常
        self.assertIsInstance(result[0], (bool, np.bool_))
    
    def test_boundary_constraint_creation(self):
        """测试边界约束创建"""
        boundary_constraint = self.constraints[2]
        constraint_func = self.constraint_manager._create_boundary_constraint(boundary_constraint)
        
        # 测试在边界内的样本
        test_samples = np.array([[5.0, 5.0, 3.0]])
        result = constraint_func(test_samples)
        self.assertTrue(result[0])
        
        # 测试超出边界的样本
        test_samples = np.array([[15.0, 5.0, 3.0]])  # param1 > 10.0
        result = constraint_func(test_samples)
        self.assertFalse(result[0])
        
        test_samples = np.array([[5.0, 5.0, 6.0]])  # param3 > 5.0
        result = constraint_func(test_samples)
        self.assertFalse(result[0])
    
    def test_custom_constraint_creation(self):
        """测试自定义约束创建"""
        function_code = """
def constraint_function(samples):
    # 自定义约束：param1 + param2 <= 8
    return samples[:, 0] + samples[:, 1] <= 8.0
"""
        
        custom_constraint = ConstraintDefinition(
            name="custom_constraint",
            constraint_type="custom",
            parameters=["param1", "param2"],
            function_code=function_code
        )
        
        constraint_func = self.constraint_manager._create_custom_constraint(custom_constraint)
        
        # 测试样本：3+4=7 <= 8 (满足)
        test_samples = np.array([[3.0, 4.0, 2.0]])
        result = constraint_func(test_samples)
        self.assertTrue(result[0])
        
        # 测试样本：5+5=10 > 8 (不满足)
        test_samples = np.array([[5.0, 5.0, 2.0]])
        result = constraint_func(test_samples)
        self.assertFalse(result[0])
    
    def test_custom_constraint_invalid_function(self):
        """测试无效自定义约束函数"""
        # 没有函数代码
        invalid_constraint = ConstraintDefinition(
            name="invalid_constraint",
            constraint_type="custom",
            parameters=["param1"],
            function_code=None
        )
        
        with self.assertRaises(ValueError):
            self.constraint_manager._create_custom_constraint(invalid_constraint)
        
        # 函数名错误
        wrong_function_code = """
def wrong_function_name(samples):
    return samples[:, 0] <= 5.0
"""
        
        wrong_constraint = ConstraintDefinition(
            name="wrong_constraint",
            constraint_type="custom",
            parameters=["param1"],
            function_code=wrong_function_code
        )
        
        with self.assertRaises(ValueError):
            self.constraint_manager._create_custom_constraint(wrong_constraint)
    
    def test_check_constraints(self):
        """测试约束检查"""
        # 创建测试样本
        test_samples = np.array([
            [3.0, 4.0, 2.0],  # 满足所有约束
            [8.0, 8.0, 3.0],  # 不满足线性约束 (8+8=16 > 15)
            [9.0, 2.0, 3.0],  # 不满足比例约束 (9/2=4.5 > 3)
            [15.0, 5.0, 3.0]  # 不满足边界约束 (param1=15 > 10)
        ])
        
        valid_mask = self.constraint_manager.check_constraints(test_samples)
        
        # 只有第一个样本应该满足所有约束
        expected_mask = np.array([True, False, False, False])
        np.testing.assert_array_equal(valid_mask, expected_mask)
    
    def test_get_constraint_violations(self):
        """测试获取约束违反情况"""
        test_samples = np.array([
            [3.0, 4.0, 2.0],  # 满足所有约束
            [8.0, 8.0, 3.0],  # 违反线性约束
            [9.0, 2.0, 3.0],  # 违反比例约束
        ])
        
        violations = self.constraint_manager.get_constraint_violations(test_samples)
        
        self.assertIn("linear_constraint", violations)
        self.assertIn("ratio_constraint", violations)
        self.assertIn("boundary_constraint", violations)
        
        # 检查线性约束违反情况
        linear_violations = violations["linear_constraint"]
        expected_linear = np.array([False, True, False])  # 只有第二个样本违反
        np.testing.assert_array_equal(linear_violations, expected_linear)
        
        # 检查比例约束违反情况
        ratio_violations = violations["ratio_constraint"]
        expected_ratio = np.array([False, False, True])  # 只有第三个样本违反
        np.testing.assert_array_equal(ratio_violations, expected_ratio)
    
    def test_repair_samples(self):
        """测试样本修复"""
        # 创建违反约束的样本
        invalid_samples = np.array([
            [15.0, 5.0, 3.0],  # 违反边界约束
            [8.0, 8.0, 3.0],   # 违反线性约束
        ])
        
        repaired_samples = self.constraint_manager.repair_samples(invalid_samples, max_iterations=10)
        
        # 检查修复后的样本形状
        self.assertEqual(repaired_samples.shape, invalid_samples.shape)
        
        # 检查修复后的样本是否满足边界约束
        for i, param in enumerate(self.params):
            self.assertTrue(np.all(repaired_samples[:, i] >= param.min_value))
            self.assertTrue(np.all(repaired_samples[:, i] <= param.max_value))
    
    def test_generate_constraint_report(self):
        """测试生成约束报告"""
        test_samples = np.array([
            [3.0, 4.0, 2.0],  # 满足所有约束
            [8.0, 8.0, 3.0],  # 违反线性约束
            [9.0, 2.0, 3.0],  # 违反比例约束
            [5.0, 5.0, 3.0],  # 满足所有约束
        ])
        
        report = self.constraint_manager.generate_constraint_report(test_samples)
        
        # 检查报告结构
        self.assertIn('total_samples', report)
        self.assertIn('valid_samples', report)
        self.assertIn('invalid_samples', report)
        self.assertIn('validity_rate', report)
        self.assertIn('constraint_details', report)
        
        # 检查报告内容
        self.assertEqual(report['total_samples'], 4)
        self.assertEqual(report['valid_samples'], 2)  # 第1和第4个样本有效
        self.assertEqual(report['invalid_samples'], 2)
        self.assertEqual(report['validity_rate'], 0.5)
        
        # 检查约束详情
        constraint_details = report['constraint_details']
        self.assertIn('linear_constraint', constraint_details)
        self.assertIn('ratio_constraint', constraint_details)
    
    def test_save_and_load_constraints_config(self):
        """测试保存和加载约束配置"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_path = f.name
        
        try:
            # 保存配置
            self.constraint_manager.save_constraints_config(config_path)
            
            # 检查文件是否存在
            self.assertTrue(os.path.exists(config_path))
            
            # 加载配置
            loaded_manager = ParameterConstraints.load_constraints_config(
                config_path, self.config
            )
            
            # 检查加载的配置
            self.assertEqual(len(loaded_manager.constraints), len(self.constraints))
            self.assertEqual(
                loaded_manager.constraints[0].name,
                self.constraints[0].name
            )
            
        finally:
            # 清理临时文件
            if os.path.exists(config_path):
                os.unlink(config_path)
    
    def test_unknown_constraint_type(self):
        """测试未知约束类型"""
        unknown_constraint = ConstraintDefinition(
            name="unknown_constraint",
            constraint_type="unknown_type",
            parameters=["param1"]
        )
        
        constraints_with_unknown = self.constraints + [unknown_constraint]
        
        with self.assertRaises(ValueError):
            ParameterConstraints(constraints_with_unknown, self.config)


class TestConstraintValidator(unittest.TestCase):
    """约束验证器测试类"""
    
    def setUp(self):
        """设置测试数据"""
        self.params = [
            ParameterDefinition("param1", 0.0, 10.0),
            ParameterDefinition("param2", 0.0, 10.0)
        ]
        
        self.config = SamplingConfig(
            parameters=self.params,
            n_samples=100,
            random_seed=42
        )
        
        # 创建合理的约束
        reasonable_constraints = [
            ConstraintDefinition(
                name="reasonable_constraint",
                constraint_type="linear",
                parameters=["param1", "param2"],
                coefficients=[1.0, 1.0],
                bound=15.0,
                operator="le"
            )
        ]
        
        self.reasonable_constraint_manager = ParameterConstraints(
            reasonable_constraints, self.config
        )
        
        # 创建过于严格的约束
        strict_constraints = [
            ConstraintDefinition(
                name="strict_constraint1",
                constraint_type="linear",
                parameters=["param1", "param2"],
                coefficients=[1.0, 1.0],
                bound=0.1,  # 非常小的边界
                operator="le"
            ),
            ConstraintDefinition(
                name="strict_constraint2",
                constraint_type="linear",
                parameters=["param1", "param2"],
                coefficients=[1.0, -1.0],
                bound=0.05,  # 更严格的约束
                operator="le"
            ),
            ConstraintDefinition(
                name="strict_constraint3",
                constraint_type="linear",
                parameters=["param1", "param2"],
                coefficients=[-1.0, 1.0],
                bound=0.05,  # 冲突的约束
                operator="le"
            )
        ]
        
        self.strict_constraint_manager = ParameterConstraints(
            strict_constraints, self.config
        )
    
    def test_validator_initialization(self):
        """测试验证器初始化"""
        validator = ConstraintValidator(self.reasonable_constraint_manager)
        self.assertIs(validator.constraints, self.reasonable_constraint_manager)
    
    def test_validate_reasonable_constraints(self):
        """测试验证合理约束"""
        validator = ConstraintValidator(self.reasonable_constraint_manager)
        result = validator.validate_constraint_consistency()
        
        self.assertIn('is_consistent', result)
        self.assertIn('conflicts', result)
        self.assertIn('warnings', result)
        
        # 合理的约束应该是一致的
        self.assertTrue(result['is_consistent'])
        self.assertEqual(len(result['conflicts']), 0)
    
    def test_validate_strict_constraints(self):
        """测试验证严格约束"""
        validator = ConstraintValidator(self.strict_constraint_manager)
        result = validator.validate_constraint_consistency()
        
        # 过于严格的约束应该被检测出来
        self.assertFalse(result['is_consistent'])
        self.assertGreater(len(result['conflicts']), 0)
    
    def test_generate_test_samples(self):
        """测试生成测试样本"""
        validator = ConstraintValidator(self.reasonable_constraint_manager)
        test_samples = validator._generate_test_samples(100)
        
        self.assertEqual(test_samples.shape, (100, 2))
        
        # 检查样本范围
        self.assertTrue(np.all(test_samples[:, 0] >= 0.0))
        self.assertTrue(np.all(test_samples[:, 0] <= 10.0))
        self.assertTrue(np.all(test_samples[:, 1] >= 0.0))
        self.assertTrue(np.all(test_samples[:, 1] <= 10.0))


class TestEdgeCases(unittest.TestCase):
    """边界情况测试类"""
    
    def test_empty_constraints(self):
        """测试空约束列表"""
        params = [ParameterDefinition("param1", 0.0, 1.0)]
        config = SamplingConfig(params, 10, 42)
        
        constraint_manager = ParameterConstraints([], config)
        
        self.assertEqual(len(constraint_manager.constraints), 0)
        self.assertEqual(len(constraint_manager.constraint_functions), 0)
        
        # 空约束应该允许所有样本
        test_samples = np.array([[0.5], [1.5], [-0.5]])  # 包含超出边界的样本
        valid_mask = constraint_manager.check_constraints(test_samples)
        
        # 所有样本都应该被认为是有效的（因为没有约束）
        np.testing.assert_array_equal(valid_mask, np.array([True, True, True]))
    
    def test_single_parameter_constraint(self):
        """测试单参数约束"""
        params = [ParameterDefinition("param1", 0.0, 10.0)]
        config = SamplingConfig(params, 10, 42)
        
        constraints = [
            ConstraintDefinition(
                name="single_param_constraint",
                constraint_type="linear",
                parameters=["param1"],
                coefficients=[1.0],
                bound=5.0,
                operator="le"
            )
        ]
        
        constraint_manager = ParameterConstraints(constraints, config)
        
        test_samples = np.array([[3.0], [7.0]])
        valid_mask = constraint_manager.check_constraints(test_samples)
        
        expected_mask = np.array([True, False])  # 3<=5 (True), 7<=5 (False)
        np.testing.assert_array_equal(valid_mask, expected_mask)
    
    def test_constraint_with_nonexistent_parameter(self):
        """测试引用不存在参数的约束"""
        params = [ParameterDefinition("param1", 0.0, 10.0)]
        config = SamplingConfig(params, 10, 42)
        
        constraints = [
            ConstraintDefinition(
                name="invalid_param_constraint",
                constraint_type="linear",
                parameters=["param1", "nonexistent_param"],  # 不存在的参数
                coefficients=[1.0, 1.0],
                bound=5.0,
                operator="le"
            )
        ]
        
        # 应该抛出KeyError
        with self.assertRaises(KeyError):
            ParameterConstraints(constraints, config)
    
    def test_ratio_constraint_wrong_parameter_count(self):
        """测试比例约束参数数量错误"""
        params = [
            ParameterDefinition("param1", 0.0, 10.0),
            ParameterDefinition("param2", 0.0, 10.0),
            ParameterDefinition("param3", 0.0, 10.0)
        ]
        config = SamplingConfig(params, 10, 42)
        
        # 比例约束需要恰好两个参数
        wrong_ratio_constraint = ConstraintDefinition(
            name="wrong_ratio_constraint",
            constraint_type="ratio",
            parameters=["param1", "param2", "param3"],  # 3个参数，应该是2个
            bound=2.0,
            operator="le"
        )
        
        with self.assertRaises(ValueError):
            constraint_manager = ParameterConstraints([wrong_ratio_constraint], config)


if __name__ == '__main__':
    unittest.main()