"""
大规模抽样模块
实现10,000+参数组合的高效生成
"""

import gc
import psutil
import time
import threading
from typing import Dict, List, Optional, Any, Callable
import numpy as np
from dataclasses import dataclass
from pathlib import Path
import pickle
import gzip
import json

from .parameter_sampler import (
    ParameterDefinition, SamplingConfig, SamplingResult, 
    LatinHypercubeSampler
)


@dataclass
class MemoryUsage:
    """内存使用情况数据类"""
    total_mb: float
    available_mb: float
    used_mb: float
    percent: float
    process_mb: float


@dataclass
class ProgressInfo:
    """进度信息数据类"""
    current_samples: int
    total_samples: int
    current_batch: int
    total_batches: int
    elapsed_time: float
    estimated_remaining_time: float
    samples_per_second: float
    memory_usage: MemoryUsage


class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, warning_threshold: float = 80.0, critical_threshold: float = 90.0):
        """
        初始化内存监控器
        
        Args:
            warning_threshold: 内存使用警告阈值（百分比）
            critical_threshold: 内存使用临界阈值（百分比）
        """
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.process = psutil.Process()
    
    def get_memory_usage(self) -> MemoryUsage:
        """
        获取当前内存使用情况
        
        Returns:
            MemoryUsage: 内存使用信息
        """
        # 系统内存信息
        system_memory = psutil.virtual_memory()
        
        # 进程内存信息
        process_memory = self.process.memory_info()
        
        return MemoryUsage(
            total_mb=system_memory.total / (1024 * 1024),
            available_mb=system_memory.available / (1024 * 1024),
            used_mb=system_memory.used / (1024 * 1024),
            percent=system_memory.percent,
            process_mb=process_memory.rss / (1024 * 1024)
        )
    
    def check_memory_status(self) -> str:
        """
        检查内存状态
        
        Returns:
            str: 内存状态（'normal', 'warning', 'critical'）
        """
        usage = self.get_memory_usage()
        
        if usage.percent >= self.critical_threshold:
            return 'critical'
        elif usage.percent >= self.warning_threshold:
            return 'warning'
        else:
            return 'normal'
    
    def force_garbage_collection(self):
        """强制垃圾回收"""
        gc.collect()


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self):
        """初始化进度跟踪器"""
        self.start_time = None
        self.last_update_time = None
        self.samples_history = []
        self.time_history = []
    
    def start(self):
        """开始跟踪"""
        self.start_time = time.time()
        self.last_update_time = self.start_time
        self.samples_history = [0]
        self.time_history = [0]
    
    def update(self, current_samples: int, total_samples: int, 
               current_batch: int, total_batches: int) -> ProgressInfo:
        """
        更新进度信息
        
        Args:
            current_samples: 当前样本数
            total_samples: 总样本数
            current_batch: 当前批次
            total_batches: 总批次数
            
        Returns:
            ProgressInfo: 进度信息
        """
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        
        # 更新历史记录
        self.samples_history.append(current_samples)
        self.time_history.append(elapsed_time)
        
        # 计算速度（使用最近几个数据点）
        if len(self.samples_history) >= 2:
            recent_samples = self.samples_history[-5:]  # 最近5个数据点
            recent_times = self.time_history[-5:]
            
            if len(recent_samples) >= 2:
                time_diff = recent_times[-1] - recent_times[0]
                sample_diff = recent_samples[-1] - recent_samples[0]
                samples_per_second = sample_diff / time_diff if time_diff > 0 else 0
            else:
                samples_per_second = current_samples / elapsed_time if elapsed_time > 0 else 0
        else:
            samples_per_second = 0
        
        # 估算剩余时间
        remaining_samples = total_samples - current_samples
        estimated_remaining_time = (remaining_samples / samples_per_second 
                                  if samples_per_second > 0 else 0)
        
        # 获取内存使用情况
        memory_monitor = MemoryMonitor()
        memory_usage = memory_monitor.get_memory_usage()
        
        self.last_update_time = current_time
        
        return ProgressInfo(
            current_samples=current_samples,
            total_samples=total_samples,
            current_batch=current_batch,
            total_batches=total_batches,
            elapsed_time=elapsed_time,
            estimated_remaining_time=estimated_remaining_time,
            samples_per_second=samples_per_second,
            memory_usage=memory_usage
        )


class SampleStorage:
    """样本存储管理器"""
    
    def __init__(self, storage_path: str, compression: bool = True):
        """
        初始化存储管理器
        
        Args:
            storage_path: 存储路径
            compression: 是否使用压缩
        """
        self.storage_path = Path(storage_path)
        self.compression = compression
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 创建索引文件
        self.index_file = self.storage_path / "sample_index.json"
        self.index = self._load_index()
        # 确保索引文件存在
        if not self.index_file.exists():
            self._save_index()
    
    def _load_index(self) -> Dict[str, Any]:
        """加载索引文件"""
        if self.index_file.exists():
            with open(self.index_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {"samples": [], "total_count": 0}
    
    def _save_index(self):
        """保存索引文件"""
        with open(self.index_file, 'w', encoding='utf-8') as f:
            json.dump(self.index, f, indent=2, ensure_ascii=False)
    
    def store_batch(self, batch_samples: np.ndarray, batch_id: str, 
                   metadata: Optional[Dict] = None) -> str:
        """
        存储批次样本
        
        Args:
            batch_samples: 批次样本数据
            batch_id: 批次ID
            metadata: 元数据
            
        Returns:
            str: 存储文件路径
        """
        # 生成文件名
        if self.compression:
            filename = f"batch_{batch_id}.pkl.gz"
            filepath = self.storage_path / filename
            
            # 压缩存储
            with gzip.open(filepath, 'wb') as f:
                pickle.dump({
                    'samples': batch_samples,
                    'metadata': metadata or {}
                }, f)
        else:
            filename = f"batch_{batch_id}.pkl"
            filepath = self.storage_path / filename
            
            # 普通存储
            with open(filepath, 'wb') as f:
                pickle.dump({
                    'samples': batch_samples,
                    'metadata': metadata or {}
                }, f)
        
        # 更新索引
        self.index["samples"].append({
            "batch_id": batch_id,
            "filename": filename,
            "sample_count": len(batch_samples),
            "shape": batch_samples.shape,
            "metadata": metadata or {}
        })
        self.index["total_count"] += len(batch_samples)
        self._save_index()
        
        return str(filepath)
    
    def load_batch(self, batch_id: str) -> Optional[np.ndarray]:
        """
        加载批次样本
        
        Args:
            batch_id: 批次ID
            
        Returns:
            Optional[np.ndarray]: 样本数据
        """
        # 查找批次信息
        batch_info = None
        for sample_info in self.index["samples"]:
            if sample_info["batch_id"] == batch_id:
                batch_info = sample_info
                break
        
        if not batch_info:
            return None
        
        filepath = self.storage_path / batch_info["filename"]
        
        try:
            if batch_info["filename"].endswith('.gz'):
                with gzip.open(filepath, 'rb') as f:
                    data = pickle.load(f)
            else:
                with open(filepath, 'rb') as f:
                    data = pickle.load(f)
            
            return data['samples']
        except Exception as e:
            print(f"加载批次 {batch_id} 失败: {e}")
            return None
    
    def load_all_samples(self) -> Optional[np.ndarray]:
        """
        加载所有样本
        
        Returns:
            Optional[np.ndarray]: 所有样本数据
        """
        if not self.index["samples"]:
            return None
        
        all_samples = []
        
        for sample_info in self.index["samples"]:
            batch_samples = self.load_batch(sample_info["batch_id"])
            if batch_samples is not None:
                all_samples.append(batch_samples)
        
        if all_samples:
            return np.vstack(all_samples)
        return None
    
    def get_storage_info(self) -> Dict[str, Any]:
        """
        获取存储信息
        
        Returns:
            Dict[str, Any]: 存储信息
        """
        total_size = 0
        for sample_info in self.index["samples"]:
            filepath = self.storage_path / sample_info["filename"]
            if filepath.exists():
                total_size += filepath.stat().st_size
        
        return {
            "total_samples": self.index["total_count"],
            "batch_count": len(self.index["samples"]),
            "total_size_mb": total_size / (1024 * 1024),
            "storage_path": str(self.storage_path),
            "compression_enabled": self.compression
        }
    
    def cleanup(self):
        """清理存储文件"""
        for sample_info in self.index["samples"]:
            filepath = self.storage_path / sample_info["filename"]
            if filepath.exists():
                filepath.unlink()
        
        if self.index_file.exists():
            self.index_file.unlink()
        
        self.index = {"samples": [], "total_count": 0}


class LargeScaleSampler:
    """大规模抽样器"""
    
    def __init__(self, base_sampler: LatinHypercubeSampler, 
                 batch_size: int = 1000,
                 storage_path: Optional[str] = None,
                 enable_compression: bool = True,
                 memory_limit_mb: float = 4000):
        """
        初始化大规模抽样器
        
        Args:
            base_sampler: 基础抽样器
            batch_size: 批次大小
            storage_path: 存储路径
            enable_compression: 启用压缩
            memory_limit_mb: 内存限制（MB）
        """
        self.base_sampler = base_sampler
        self.batch_size = batch_size
        self.memory_limit_mb = memory_limit_mb
        
        # 初始化组件
        self.memory_monitor = MemoryMonitor()
        self.progress_tracker = ProgressTracker()
        
        # 初始化存储
        if storage_path:
            self.storage = SampleStorage(storage_path, enable_compression)
        else:
            self.storage = None
        
        # 性能统计
        self.performance_stats = {
            'total_generation_time': 0,
            'total_samples_generated': 0,
            'average_batch_time': 0,
            'memory_peak_mb': 0,
            'gc_count': 0
        }
    
    def generate_large_samples(self, total_samples: int,
                             progress_callback: Optional[Callable] = None,
                             memory_callback: Optional[Callable] = None,
                             save_intermediate: bool = False) -> SamplingResult:
        """
        生成大规模抽样
        
        Args:
            total_samples: 总样本数
            progress_callback: 进度回调函数
            memory_callback: 内存回调函数
            save_intermediate: 是否保存中间结果
            
        Returns:
            SamplingResult: 抽样结果
        """
        start_time = time.time()
        self.progress_tracker.start()
        
        # 计算批次数
        n_batches = (total_samples + self.batch_size - 1) // self.batch_size
        
        all_samples = []
        batch_times = []
        
        try:
            for batch_idx in range(n_batches):
                batch_start_time = time.time()
                
                # 检查内存状态
                memory_status = self.memory_monitor.check_memory_status()
                if memory_status == 'critical':
                    if memory_callback:
                        memory_callback('critical', self.memory_monitor.get_memory_usage())
                    
                    # 强制垃圾回收
                    self.memory_monitor.force_garbage_collection()
                    self.performance_stats['gc_count'] += 1
                    
                    # 如果仍然内存不足，考虑减小批次大小
                    if self.memory_monitor.get_memory_usage().percent > 85:
                        self.batch_size = max(100, self.batch_size // 2)
                        print(f"内存不足，减小批次大小到 {self.batch_size}")
                
                # 计算当前批次的样本数
                current_batch_size = min(
                    self.batch_size,
                    total_samples - batch_idx * self.batch_size
                )
                
                # 生成批次样本
                batch_samples = self._generate_batch(
                    current_batch_size, 
                    batch_idx
                )
                
                # 存储中间结果
                if save_intermediate and self.storage:
                    self.storage.store_batch(
                        batch_samples, 
                        f"batch_{batch_idx:04d}",
                        {
                            'batch_index': batch_idx,
                            'batch_size': current_batch_size,
                            'timestamp': time.time()
                        }
                    )
                
                all_samples.append(batch_samples)
                
                # 记录批次时间
                batch_time = time.time() - batch_start_time
                batch_times.append(batch_time)
                
                # 更新性能统计
                current_samples = (batch_idx + 1) * self.batch_size
                current_samples = min(current_samples, total_samples)
                
                # 更新进度
                progress_info = self.progress_tracker.update(
                    current_samples, total_samples,
                    batch_idx + 1, n_batches
                )
                
                # 更新内存峰值
                current_memory = self.memory_monitor.get_memory_usage().process_mb
                self.performance_stats['memory_peak_mb'] = max(
                    self.performance_stats['memory_peak_mb'],
                    current_memory
                )
                
                # 调用进度回调
                if progress_callback:
                    progress_callback(progress_info)
                
                # 调用内存回调
                if memory_callback and memory_status != 'normal':
                    memory_callback(memory_status, progress_info.memory_usage)
        
        except Exception as e:
            print(f"大规模抽样过程中发生错误: {e}")
            raise
        
        # 合并所有批次的样本
        try:
            combined_samples = np.vstack(all_samples)
        except MemoryError:
            print("内存不足，无法合并所有样本。尝试使用存储方式...")
            if self.storage:
                combined_samples = self.storage.load_all_samples()
                if combined_samples is None:
                    raise MemoryError("无法加载合并的样本数据")
            else:
                raise MemoryError("内存不足且未启用存储功能")
        
        # 计算最终质量指标
        quality_metrics = self.base_sampler._calculate_quality_metrics(combined_samples)
        
        # 更新性能统计
        total_time = time.time() - start_time
        self.performance_stats.update({
            'total_generation_time': total_time,
            'total_samples_generated': total_samples,
            'average_batch_time': np.mean(batch_times) if batch_times else 0
        })
        
        # 生成哈希签名
        hash_signature = self.base_sampler._generate_hash_signature(combined_samples)
        
        # 创建最终配置
        final_config = SamplingConfig(
            parameters=self.base_sampler.config.parameters,
            n_samples=total_samples,
            random_seed=self.base_sampler.config.random_seed,
            sampling_method=self.base_sampler.config.sampling_method,
            optimization_criterion=self.base_sampler.config.optimization_criterion
        )
        
        return SamplingResult(
            samples=combined_samples,
            parameter_names=[p.name for p in self.base_sampler.config.parameters],
            config=final_config,
            quality_metrics=quality_metrics,
            generation_time=total_time,
            hash_signature=hash_signature
        )
    
    def _generate_batch(self, batch_size: int, batch_index: int) -> np.ndarray:
        """
        生成单个批次的样本
        
        Args:
            batch_size: 批次大小
            batch_index: 批次索引
            
        Returns:
            np.ndarray: 批次样本
        """
        # 为每个批次使用不同的随机种子
        batch_seed = self.base_sampler.config.random_seed + batch_index * 1000
        
        # 创建批次配置
        batch_config = SamplingConfig(
            parameters=self.base_sampler.config.parameters,
            n_samples=batch_size,
            random_seed=batch_seed,
            sampling_method=self.base_sampler.config.sampling_method,
            optimization_criterion=self.base_sampler.config.optimization_criterion
        )
        
        # 创建批次抽样器
        batch_sampler = LatinHypercubeSampler(batch_config)
        batch_result = batch_sampler.generate_samples()
        
        return batch_result.samples
    
    def get_performance_report(self) -> Dict[str, Any]:
        """
        获取性能报告
        
        Returns:
            Dict[str, Any]: 性能报告
        """
        stats = self.performance_stats.copy()
        
        # 计算派生指标
        if stats['total_generation_time'] > 0:
            stats['samples_per_second'] = (
                stats['total_samples_generated'] / stats['total_generation_time']
            )
        else:
            stats['samples_per_second'] = 0
        
        # 添加存储信息
        if self.storage:
            stats['storage_info'] = self.storage.get_storage_info()
        
        # 添加内存信息
        stats['current_memory_usage'] = self.memory_monitor.get_memory_usage()
        
        return stats
    
    def cleanup(self):
        """清理资源"""
        if self.storage:
            self.storage.cleanup()
        
        # 强制垃圾回收
        self.memory_monitor.force_garbage_collection()


def create_large_scale_sampler(parameters: List[ParameterDefinition],
                             n_samples: int,
                             random_seed: int = 42,
                             batch_size: int = 1000,
                             storage_path: Optional[str] = None) -> LargeScaleSampler:
    """
    创建大规模抽样器的便捷函数
    
    Args:
        parameters: 参数定义列表
        n_samples: 样本数量
        random_seed: 随机种子
        batch_size: 批次大小
        storage_path: 存储路径
        
    Returns:
        LargeScaleSampler: 大规模抽样器实例
    """
    # 创建基础配置
    config = SamplingConfig(
        parameters=parameters,
        n_samples=batch_size,  # 基础抽样器使用批次大小
        random_seed=random_seed,
        sampling_method="lhs",
        optimization_criterion="maximin"
    )
    
    # 创建基础抽样器
    base_sampler = LatinHypercubeSampler(config)
    
    # 创建大规模抽样器
    large_sampler = LargeScaleSampler(
        base_sampler=base_sampler,
        batch_size=batch_size,
        storage_path=storage_path,
        enable_compression=True,
        memory_limit_mb=4000
    )
    
    return large_sampler