"""
Unit tests for utility values module.
"""
import pytest
import os
import tempfile
from src.modules.economics.utility_values import UtilityValueManager, UtilityScale
from src.core.individual import Individual


class TestUtilityValueManager:
    """Test suite for UtilityValueManager class."""
    
    def test_init(self):
        """Test UtilityValueManager initialization."""
        manager = UtilityValueManager()
        
        assert isinstance(manager.utility_values, dict)
        assert isinstance(manager.age_adjustments, dict)
        assert isinstance(manager.treatment_disutilities, dict)
        assert manager.scale == UtilityScale.EQ5D
    
    def test_get_utility_value(self):
        """Test getting utility values."""
        manager = UtilityValueManager()
        
        # Test known values
        assert manager.get_utility_value('normal') == 1.0
        assert manager.get_utility_value('clinical_cancer_stage_i') == 0.85
        assert manager.get_utility_value('clinical_cancer_stage_iv') == 0.55
        
        # Test unknown value (should default to 1.0)
        assert manager.get_utility_value('unknown_state') == 1.0
    
    def test_get_age_adjusted_utility(self):
        """Test age-adjusted utility calculation."""
        manager = UtilityValueManager()
        
        base_utility = 0.8
        
        # Test different age groups
        adjusted_utility_25 = manager.get_age_adjusted_utility(base_utility, 25)
        adjusted_utility_55 = manager.get_age_adjusted_utility(base_utility, 55)
        adjusted_utility_85 = manager.get_age_adjusted_utility(base_utility, 85)
        
        assert adjusted_utility_25 > adjusted_utility_55 > adjusted_utility_85
        assert adjusted_utility_25 <= base_utility  # All adjustments should be <= 1.0
    
    def test_get_disease_state_utility(self):
        """Test getting disease state utilities."""
        manager = UtilityValueManager()
        
        # Test without age adjustment
        utility = manager.get_disease_state_utility('clinical_cancer_stage_ii')
        assert utility == 0.78
        
        # Test with age adjustment
        adjusted_utility = manager.get_disease_state_utility('clinical_cancer_stage_ii', 65)
        assert adjusted_utility <= 0.78  # Should be adjusted downward due to age
        assert adjusted_utility > 0  # Should still be positive
    
    def test_get_treatment_disutility(self):
        """Test getting treatment disutilities."""
        manager = UtilityValueManager()
        
        # Test known disutilities
        assert manager.get_treatment_disutility('colonoscopy') == -0.01
        assert manager.get_treatment_disutility('chemotherapy') == -0.20
        
        # Test unknown disutility (should default to 0.0)
        assert manager.get_treatment_disutility('unknown_treatment') == 0.0
    
    def test_apply_treatment_disutility(self):
        """Test applying treatment disutilities."""
        manager = UtilityValueManager()
        
        base_utility = 0.9
        adjusted_utility = manager.apply_treatment_disutility(base_utility, 'colonoscopy')
        
        assert adjusted_utility == base_utility + (-0.01)  # 0.9 - 0.01 = 0.89
        assert adjusted_utility < base_utility  # Disutility should reduce utility
    
    def test_interpolate_utility(self):
        """Test utility interpolation."""
        manager = UtilityValueManager()
        
        # Create test utility values
        test_utilities = {
            'age_18_29': 0.95,
            'age_30_39': 0.90,
            'age_40_49': 0.85,
            'age_50_59': 0.80
        }
        
        # Test exact age match
        utility_exact = manager.interpolate_utility(25, test_utilities)
        assert utility_exact == 0.95
        
        # Test interpolation
        utility_interp = manager.interpolate_utility(35, test_utilities)
        assert 0.90 <= utility_interp <= 0.95  # Should be between 0.90 and 0.95
        
        # Test extrapolation (below range)
        utility_extrap_low = manager.interpolate_utility(15, test_utilities)
        assert utility_extrap_low == 0.95  # Should use the lowest value
        
        # Test extrapolation (above range)
        utility_extrap_high = manager.interpolate_utility(65, test_utilities)
        assert utility_extrap_high == 0.80  # Should use the highest value
    
    def test_get_utility_for_individual(self):
        """Test getting utility for an individual."""
        manager = UtilityValueManager()
        
        # Create mock individual
        from src.core.enums import Gender
        individual = Individual(birth_year=1980, gender=Gender.FEMALE)
        individual.age = 55
        individual.disease_state = 'clinical_cancer_stage_ii'
        
        utility = manager.get_utility_for_individual(individual)
        
        # Should be adjusted for both disease state and age
        assert utility <= 0.78  # Base utility for stage II cancer
        assert utility > 0  # Should still be positive
    
    def test_load_utilities_from_yaml(self):
        """Test loading utilities from YAML file."""
        manager = UtilityValueManager()
        
        # Create temporary YAML file
        yaml_content = """
utility_weights:
  scale: "EQ5D"
  disease_state_utilities:
    test_state: 0.75
  treatment_related_utilities:
    screening_disutility:
      test_screening: -0.005
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(yaml_content)
            temp_file = f.name
        
        try:
            manager.load_utilities_from_yaml(temp_file)
            
            # Check that values were loaded
            assert manager.get_utility_value('test_state') == 0.75
            assert manager.get_treatment_disutility('test_screening') == -0.005
        finally:
            os.unlink(temp_file)
    
    def test_load_utilities_from_json(self):
        """Test loading utilities from JSON file."""
        manager = UtilityValueManager()
        
        # Create temporary JSON file
        json_content = """
{
    "utility_weights": {
        "scale": "SF6D",
        "disease_state_utilities": {
            "test_state_json": 0.80
        },
        "treatment_related_utilities": {
            "screening_disutility": {
                "test_screening_json": -0.003
            }
        }
    }
}
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write(json_content)
            temp_file = f.name
        
        try:
            manager.load_utilities_from_json(temp_file)
            
            # Check that values were loaded
            assert manager.get_utility_value('test_state_json') == 0.80
            assert manager.get_treatment_disutility('test_screening_json') == -0.003
            assert manager.scale == UtilityScale.SF6D
        finally:
            os.unlink(temp_file)


if __name__ == "__main__":
    pytest.main([__file__])