"""
Uncertainty Analysis for health outcomes metrics including confidence intervals.
"""
from typing import Dict, List, Tuple, Callable, Optional, Any
import numpy as np
from scipy import stats
from dataclasses import dataclass
import logging
from src.modules.economics.qaly_calculator import QALYResult

logger = logging.getLogger(__name__)


@dataclass
class UncertaintyResult:
    """Result of uncertainty analysis."""
    mean_outcome: float
    std_outcome: float
    confidence_interval: Tuple[float, float]
    percentile_2_5: float
    percentile_97_5: float
    sample_size: int


class UncertaintyAnalyzer:
    """Analyzer for uncertainty in health outcomes metrics."""
    
    def __init__(self, n_simulations: int = 1000):
        """
        Initialize uncertainty analyzer.
        
        Args:
            n_simulations: Number of simulations for Monte Carlo analysis (default: 1000)
        """
        self.n_simulations = n_simulations
    
    def bootstrap_qaly_ci(
        self, 
        qaly_results: List[QALYResult], 
        confidence_level: float = 0.95
    ) -> <PERSON><PERSON>[float, float]:
        """增强的Bootstrap置信区间计算"""
        
        if not qaly_results:
            raise ValueError("QALY结果列表不能为空")
        
        if not 0 < confidence_level < 1:
            raise ValueError(f"置信水平必须在(0,1)范围内: {confidence_level}")
        
        qaly_values = [result.total_qalys for result in qaly_results]
        n_samples = len(qaly_values)
        
        if n_samples == 0:
            return (0.0, 0.0)
        
        bootstrap_means = []
        
        # Perform bootstrap sampling
        for _ in range(self.n_simulations):
            # Sample with replacement
            bootstrap_sample = np.random.choice(qaly_values, size=n_samples, replace=True)
            bootstrap_means.append(np.mean(bootstrap_sample))
        
        # Calculate confidence interval
        alpha = 1 - confidence_level
        lower_percentile = (alpha / 2) * 100
        upper_percentile = (1 - alpha / 2) * 100
        
        ci_lower = np.percentile(bootstrap_means, lower_percentile)
        ci_upper = np.percentile(bootstrap_means, upper_percentile)
        
        # 添加收敛性检查
        if len(bootstrap_means) < 100:
            logger.warning("Bootstrap样本数量可能不足，建议增加到1000+")
        
        return (ci_lower, ci_upper)
    
    def monte_carlo_sensitivity_analysis(
        self, 
        base_parameters: Dict[str, Any], 
        parameter_distributions: Dict[str, stats._distn_infrastructure.rv_frozen],
        outcome_calculator: Callable
    ) -> Dict:
        """
        Perform Monte Carlo sensitivity analysis.
        
        Args:
            base_parameters: Base parameter values
            parameter_distributions: Dictionary mapping parameter names to scipy distribution objects
            outcome_calculator: Function that calculates outcome given parameters
            
        Returns:
            Dictionary with sensitivity analysis results
        """
        results = []
        parameter_samples = []
        
        # Run Monte Carlo simulations
        for _ in range(self.n_simulations):
            # Sample parameters from their distributions
            sampled_params = {}
            for param_name, distribution in parameter_distributions.items():
                sampled_params[param_name] = distribution.rvs()
            
            parameter_samples.append(sampled_params)
            
            # Calculate outcome with sampled parameters
            try:
                outcome = outcome_calculator({**base_parameters, **sampled_params})
                results.append(outcome)
            except Exception:
                # Skip invalid parameter combinations
                continue
        
        if not results:
            return {
                'mean_outcome': 0.0,
                'std_outcome': 0.0,
                'percentile_2_5': 0.0,
                'percentile_97_5': 0.0,
                'parameter_samples': parameter_samples,
                'outcome_samples': []
            }
        
        # Analyze results
        results_array = np.array(results)
        
        return {
            'mean_outcome': np.mean(results_array),
            'std_outcome': np.std(results_array),
            'percentile_2_5': np.percentile(results_array, 2.5),
            'percentile_97_5': np.percentile(results_array, 97.5),
            'parameter_samples': parameter_samples,
            'outcome_samples': results
        }
    
    def probabilistic_sensitivity_analysis(
        self,
        base_parameters: Dict[str, Any],
        parameter_distributions: Dict[str, stats._distn_infrastructure.rv_frozen],
        outcome_function: Callable,
        percentiles: Optional[List[float]] = None
    ) -> UncertaintyResult:
        """
        Perform probabilistic sensitivity analysis.
        
        Args:
            base_parameters: Base parameter values
            parameter_distributions: Dictionary mapping parameter names to scipy distribution objects
            outcome_function: Function that calculates outcome given parameters
            percentiles: Percentiles to calculate (default: [2.5, 50, 97.5])
            
        Returns:
            UncertaintyResult with analysis results
        """
        if percentiles is None:
            percentiles = [2.5, 50, 97.5]
        
        outcomes = []
        
        # Run simulations
        for _ in range(self.n_simulations):
            # Sample parameters
            sampled_params = {}
            for param_name, distribution in parameter_distributions.items():
                sampled_params[param_name] = distribution.rvs()
            
            # Combine with base parameters
            combined_params = {**base_parameters, **sampled_params}
            
            # Calculate outcome
            try:
                outcome = outcome_function(combined_params)
                outcomes.append(outcome)
            except Exception:
                # Skip invalid combinations
                continue
        
        if not outcomes:
            return UncertaintyResult(
                mean_outcome=0.0,
                std_outcome=0.0,
                confidence_interval=(0.0, 0.0),
                percentile_2_5=0.0,
                percentile_97_5=0.0,
                sample_size=0
            )
        
        outcomes_array = np.array(outcomes)
        
        # Calculate statistics
        mean_outcome = np.mean(outcomes_array)
        std_outcome = np.std(outcomes_array)
        
        # Calculate percentiles
        percentile_values = np.percentile(outcomes_array, percentiles)
        percentile_dict = dict(zip(percentiles, percentile_values))
        
        return UncertaintyResult(
            mean_outcome=mean_outcome,
            std_outcome=std_outcome,
            confidence_interval=(percentile_dict.get(2.5, 0.0), percentile_dict.get(97.5, 0.0)),
            percentile_2_5=percentile_dict.get(2.5, 0.0),
            percentile_97_5=percentile_dict.get(97.5, 0.0),
            sample_size=len(outcomes)
        )
    
    def calculate_confidence_interval(
        self,
        data: List[float],
        confidence_level: float = 0.95
    ) -> Tuple[float, float]:
        """
        Calculate confidence interval for a set of data points.
        
        Args:
            data: List of data points
            confidence_level: Confidence level (default: 0.95 for 95% CI)
            
        Returns:
            Tuple of (lower_bound, upper_bound) for confidence interval
        """
        if not data:
            return (0.0, 0.0)
        
        data_array = np.array(data)
        n = len(data_array)
        
        if n == 1:
            return (data_array[0], data_array[0])
        
        # Calculate mean and standard error
        mean = np.mean(data_array)
        std_err = np.std(data_array, ddof=1) / np.sqrt(n)
        
        # Calculate confidence interval using t-distribution
        alpha = 1 - confidence_level
        t_value = stats.t.ppf(1 - alpha/2, n-1)
        
        margin_error = t_value * std_err
        ci_lower = mean - margin_error
        ci_upper = mean + margin_error
        
        return (ci_lower, ci_upper)
    
    def analyze_threshold_parameters(
        self,
        base_parameters: Dict[str, Any],
        parameter_bounds: Dict[str, Tuple[float, float]],
        outcome_function: Callable,
        cost_effectiveness_threshold: Optional[float] = None
    ) -> Dict:
        """
        Analyze which parameters have the most impact on results (threshold analysis).
        
        Args:
            base_parameters: Base parameter values
            parameter_bounds: Dictionary mapping parameter names to (min, max) bounds
            outcome_function: Function that calculates outcome given parameters
            cost_effectiveness_threshold: Cost-effectiveness threshold for analysis
            
        Returns:
            Dictionary with threshold analysis results
        """
        sensitivity_results = {}
        base_outcome = outcome_function(base_parameters)
        
        # Test each parameter at its bounds
        for param_name, (min_val, max_val) in parameter_bounds.items():
            # Test at minimum value
            min_params = base_parameters.copy()
            min_params[param_name] = min_val
            min_outcome = outcome_function(min_params)
            
            # Test at maximum value
            max_params = base_parameters.copy()
            max_params[param_name] = max_val
            max_outcome = outcome_function(max_params)
            
            # Calculate sensitivity (difference from base)
            min_sensitivity = abs(min_outcome - base_outcome)
            max_sensitivity = abs(max_outcome - base_outcome)
            
            sensitivity_results[param_name] = {
                'min_value': min_val,
                'max_value': max_val,
                'min_outcome': min_outcome,
                'max_outcome': max_outcome,
                'min_sensitivity': min_sensitivity,
                'max_sensitivity': max_sensitivity,
                'max_absolute_sensitivity': max(min_sensitivity, max_sensitivity)
            }
        
        # Sort by sensitivity
        sorted_results = dict(sorted(
            sensitivity_results.items(),
            key=lambda x: x[1]['max_absolute_sensitivity'],
            reverse=True
        ))
        
        return {
            'base_outcome': base_outcome,
            'sensitivity_by_parameter': sorted_results,
            'most_sensitive_parameter': next(iter(sorted_results)) if sorted_results else None
        }
