"""
向量化抽样模块
实现抽样算法的向量化计算优化
"""

import numpy as np
import time
import logging
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
from scipy.stats import qmc
import numba
from numba import jit, prange, vectorize
from concurrent.futures import ThreadPoolExecutor
import multiprocessing as mp

from .parameter_sampler import (
    ParameterDefinition, SamplingConfig, SamplingResult, 
    LatinHypercubeSampler
)

logger = logging.getLogger(__name__)

@dataclass
class VectorizationConfig:
    """向量化配置"""
    enable_numba: bool = True
    enable_parallel: bool = True
    chunk_size: int = 10000
    n_threads: int = None  # None表示自动检测
    use_gpu: bool = False  # 预留GPU支持

# Numba优化的核心函数
@jit(nopython=True, parallel=True)
def _vectorized_lhs_core(n_samples: int, n_dimensions: int, seed: int) -> np.ndarray:
    """使用Numba优化的核心LHS算法"""
    np.random.seed(seed)
    
    # 创建基础网格
    samples = np.zeros((n_samples, n_dimensions), dtype=np.float64)
    
    for dim in prange(n_dimensions):
        # 为每个维度创建等间隔的区间
        intervals = np.arange(n_samples, dtype=np.float64)
        
        # 在每个区间内随机抽样
        random_offsets = np.random.random(n_samples)
        samples[:, dim] = (intervals + random_offsets) / n_samples
        
        # 随机打乱顺序
        np.random.shuffle(samples[:, dim])
    
    return samples

@jit(nopython=True, parallel=True)
def _vectorized_scale_uniform(samples: np.ndarray, min_vals: np.ndarray, max_vals: np.ndarray) -> np.ndarray:
    """向量化的均匀分布缩放"""
    n_samples, n_dims = samples.shape
    scaled = np.zeros_like(samples)
    
    for i in prange(n_dims):
        scaled[:, i] = min_vals[i] + samples[:, i] * (max_vals[i] - min_vals[i])
    
    return scaled

@jit(nopython=True, parallel=True)
def _vectorized_quality_metrics(samples: np.ndarray) -> Tuple[float, float, float]:
    """向量化的质量指标计算"""
    n_samples, n_dims = samples.shape
    
    # 计算最小距离
    min_distance = np.inf
    total_distance = 0.0
    pair_count = 0
    
    for i in prange(n_samples):
        for j in range(i + 1, n_samples):
            distance = 0.0
            for k in range(n_dims):
                diff = samples[i, k] - samples[j, k]
                distance += diff * diff
            distance = np.sqrt(distance)
            
            if distance < min_distance:
                min_distance = distance
            total_distance += distance
            pair_count += 1
    
    mean_distance = total_distance / pair_count if pair_count > 0 else 0.0
    
    # 计算相关性
    max_correlation = 0.0
    for i in prange(n_dims):
        for j in range(i + 1, n_dims):
            # 简化的相关系数计算
            x = samples[:, i]
            y = samples[:, j]
            
            x_mean = np.mean(x)
            y_mean = np.mean(y)
            
            numerator = 0.0
            x_var = 0.0
            y_var = 0.0
            
            for k in range(n_samples):
                x_diff = x[k] - x_mean
                y_diff = y[k] - y_mean
                numerator += x_diff * y_diff
                x_var += x_diff * x_diff
                y_var += y_diff * y_diff
            
            if x_var > 0 and y_var > 0:
                correlation = abs(numerator / np.sqrt(x_var * y_var))
                if correlation > max_correlation:
                    max_correlation = correlation
    
    return min_distance, mean_distance, max_correlation

@vectorize(['float64(float64, float64, float64)'], nopython=True)
def _normal_ppf_approx(p, loc, scale):
    """正态分布逆累积分布函数的近似实现"""
    # 使用Beasley-Springer-Moro算法的简化版本
    if p <= 0.0:
        return -np.inf
    if p >= 1.0:
        return np.inf
    
    # 转换到标准正态分布
    if p < 0.5:
        sign = -1.0
        p = 1.0 - p
    else:
        sign = 1.0
    
    # 近似计算
    t = np.sqrt(-2.0 * np.log(p))
    
    # 多项式近似
    c0 = 2.515517
    c1 = 0.802853
    c2 = 0.010328
    d1 = 1.432788
    d2 = 0.189269
    d3 = 0.001308
    
    numerator = c0 + c1 * t + c2 * t * t
    denominator = 1.0 + d1 * t + d2 * t * t + d3 * t * t * t
    
    z = sign * (t - numerator / denominator)
    
    return loc + scale * z

class VectorizedSampler:
    """向量化抽样器"""
    
    def __init__(self, config: VectorizationConfig = None):
        """
        初始化向量化抽样器
        
        Args:
            config: 向量化配置
        """
        self.config = config or VectorizationConfig()
        self.n_threads = self.config.n_threads or mp.cpu_count()
        
        # 性能统计
        self.performance_stats = {
            'vectorized_time': 0.0,
            'standard_time': 0.0,
            'speedup_ratio': 1.0,
            'samples_processed': 0
        }
    
    def generate_vectorized_lhs(self, parameters: List[ParameterDefinition], 
                              n_samples: int, 
                              random_seed: int = 42) -> np.ndarray:
        """
        生成向量化的拉丁超立方抽样
        
        Args:
            parameters: 参数定义列表
            n_samples: 样本数量
            random_seed: 随机种子
            
        Returns:
            np.ndarray: 抽样样本
        """
        start_time = time.time()
        
        n_dimensions = len(parameters)
        
        if self.config.enable_numba:
            # 使用Numba优化的核心算法
            unit_samples = _vectorized_lhs_core(n_samples, n_dimensions, random_seed)
        else:
            # 标准实现
            sampler = qmc.LatinHypercube(d=n_dimensions, seed=random_seed)
            unit_samples = sampler.random(n=n_samples)
        
        # 向量化的参数缩放
        scaled_samples = self._vectorized_scale_parameters(unit_samples, parameters)
        
        # 更新性能统计
        self.performance_stats['vectorized_time'] += time.time() - start_time
        self.performance_stats['samples_processed'] += n_samples
        
        return scaled_samples
    
    def _vectorized_scale_parameters(self, unit_samples: np.ndarray, 
                                   parameters: List[ParameterDefinition]) -> np.ndarray:
        """向量化的参数缩放"""
        n_samples, n_dims = unit_samples.shape
        scaled_samples = np.zeros_like(unit_samples)
        
        # 分离不同分布类型的参数
        uniform_indices = []
        normal_indices = []
        lognormal_indices = []
        
        min_vals = np.zeros(n_dims)
        max_vals = np.zeros(n_dims)
        
        for i, param in enumerate(parameters):
            min_vals[i] = param.min_value
            max_vals[i] = param.max_value
            
            if param.distribution == "uniform":
                uniform_indices.append(i)
            elif param.distribution == "normal":
                normal_indices.append(i)
            elif param.distribution == "lognormal":
                lognormal_indices.append(i)
        
        # 向量化处理均匀分布
        if uniform_indices:
            uniform_mask = np.array(uniform_indices)
            if self.config.enable_numba:
                scaled_samples[:, uniform_mask] = _vectorized_scale_uniform(
                    unit_samples[:, uniform_mask],
                    min_vals[uniform_mask],
                    max_vals[uniform_mask]
                )
            else:
                for i in uniform_indices:
                    scaled_samples[:, i] = (min_vals[i] + 
                                          unit_samples[:, i] * (max_vals[i] - min_vals[i]))
        
        # 向量化处理正态分布
        if normal_indices:
            for i in normal_indices:
                loc = (min_vals[i] + max_vals[i]) / 2
                scale = (max_vals[i] - min_vals[i]) / 6  # 3-sigma规则
                
                if self.config.enable_numba:
                    scaled_samples[:, i] = _normal_ppf_approx(unit_samples[:, i], loc, scale)
                else:
                    from scipy.stats import norm
                    scaled_samples[:, i] = norm.ppf(unit_samples[:, i], loc=loc, scale=scale)
        
        # 处理对数正态分布
        if lognormal_indices:
            for i in lognormal_indices:
                from scipy.stats import lognorm
                scale_param = np.exp((np.log(min_vals[i]) + np.log(max_vals[i])) / 2)
                scaled_samples[:, i] = lognorm.ppf(unit_samples[:, i], s=0.5, scale=scale_param)
        
        return scaled_samples
    
    def calculate_vectorized_quality_metrics(self, samples: np.ndarray) -> Dict[str, float]:
        """向量化的质量指标计算"""
        start_time = time.time()
        
        if self.config.enable_numba and samples.shape[0] <= 5000:  # 对于大样本，Numba可能不如scipy快
            min_dist, mean_dist, max_corr = _vectorized_quality_metrics(samples)
            
            # 计算覆盖度（使用标准方法）
            coverage_scores = []
            for i in range(samples.shape[1]):
                from scipy.stats import kstest
                ks_stat, _ = kstest(samples[:, i], 'uniform')
                coverage_scores.append(1 - ks_stat)
            
            metrics = {
                'min_distance': float(min_dist),
                'mean_distance': float(mean_dist),
                'max_correlation': float(max_corr),
                'mean_correlation': float(max_corr),  # 简化
                'mean_coverage': float(np.mean(coverage_scores)),
                'min_coverage': float(np.min(coverage_scores))
            }
        else:
            # 使用标准方法
            from scipy.spatial.distance import pdist
            from scipy.stats import kstest
            
            distances = pdist(samples)
            metrics = {
                'min_distance': float(np.min(distances)),
                'mean_distance': float(np.mean(distances))
            }
            
            # 计算相关性
            correlation_matrix = np.corrcoef(samples.T)
            off_diagonal = correlation_matrix[np.triu_indices_from(correlation_matrix, k=1)]
            metrics['max_correlation'] = float(np.max(np.abs(off_diagonal)))
            metrics['mean_correlation'] = float(np.mean(np.abs(off_diagonal)))
            
            # 计算覆盖度
            coverage_scores = []
            for i in range(samples.shape[1]):
                ks_stat, _ = kstest(samples[:, i], 'uniform')
                coverage_scores.append(1 - ks_stat)
            
            metrics['mean_coverage'] = float(np.mean(coverage_scores))
            metrics['min_coverage'] = float(np.min(coverage_scores))
        
        calculation_time = time.time() - start_time
        logger.debug(f"质量指标计算耗时: {calculation_time:.3f}秒")
        
        return metrics
    
    def batch_vectorized_sampling(self, parameters: List[ParameterDefinition],
                                n_samples: int,
                                random_seed: int = 42,
                                progress_callback: Optional[callable] = None) -> np.ndarray:
        """批量向量化抽样"""
        if n_samples <= self.config.chunk_size:
            return self.generate_vectorized_lhs(parameters, n_samples, random_seed)
        
        # 分批处理
        all_samples = []
        n_batches = (n_samples + self.config.chunk_size - 1) // self.config.chunk_size
        
        for batch_idx in range(n_batches):
            batch_start = batch_idx * self.config.chunk_size
            batch_end = min(batch_start + self.config.chunk_size, n_samples)
            batch_size = batch_end - batch_start
            
            # 为每个批次使用不同的种子
            batch_seed = random_seed + batch_idx * 1000
            
            batch_samples = self.generate_vectorized_lhs(parameters, batch_size, batch_seed)
            all_samples.append(batch_samples)
            
            if progress_callback:
                progress = (batch_idx + 1) / n_batches
                progress_callback(progress, batch_idx + 1, n_batches)
        
        # 合并所有批次
        combined_samples = np.vstack(all_samples)
        
        # 重新打乱以保持LHS特性
        for dim in range(len(parameters)):
            np.random.shuffle(combined_samples[:, dim])
        
        return combined_samples
    
    def parallel_vectorized_sampling(self, parameters: List[ParameterDefinition],
                                   n_samples: int,
                                   random_seed: int = 42) -> np.ndarray:
        """并行向量化抽样"""
        if not self.config.enable_parallel or n_samples <= self.config.chunk_size:
            return self.generate_vectorized_lhs(parameters, n_samples, random_seed)
        
        # 计算每个线程的工作量
        samples_per_thread = n_samples // self.n_threads
        remaining_samples = n_samples % self.n_threads
        
        # 创建任务列表
        tasks = []
        current_seed = random_seed
        
        for thread_id in range(self.n_threads):
            thread_samples = samples_per_thread + (1 if thread_id < remaining_samples else 0)
            if thread_samples > 0:
                tasks.append((parameters, thread_samples, current_seed + thread_id * 1000))
        
        # 并行执行
        with ThreadPoolExecutor(max_workers=self.n_threads) as executor:
            futures = [
                executor.submit(self._worker_vectorized_sampling, *task)
                for task in tasks
            ]
            
            results = [future.result() for future in futures]
        
        # 合并结果
        if results:
            combined_samples = np.vstack(results)
            
            # 重新打乱以保持LHS特性
            for dim in range(len(parameters)):
                np.random.shuffle(combined_samples[:, dim])
            
            return combined_samples
        else:
            return np.array([])
    
    def _worker_vectorized_sampling(self, parameters: List[ParameterDefinition],
                                  n_samples: int, 
                                  random_seed: int) -> np.ndarray:
        """工作线程的向量化抽样"""
        return self.generate_vectorized_lhs(parameters, n_samples, random_seed)
    
    def benchmark_performance(self, parameters: List[ParameterDefinition],
                            n_samples: int,
                            n_runs: int = 3) -> Dict[str, Any]:
        """性能基准测试"""
        logger.info(f"开始性能基准测试: {n_samples}样本, {n_runs}次运行")
        
        # 测试向量化版本
        vectorized_times = []
        for run in range(n_runs):
            start_time = time.time()
            samples_vec = self.generate_vectorized_lhs(parameters, n_samples, 42 + run)
            vectorized_times.append(time.time() - start_time)
        
        # 测试标准版本
        standard_times = []
        for run in range(n_runs):
            start_time = time.time()
            
            # 创建标准抽样器
            config = SamplingConfig(
                parameters=parameters,
                n_samples=n_samples,
                random_seed=42 + run,
                sampling_method="lhs",
                optimization_criterion="maximin"
            )
            sampler = LatinHypercubeSampler(config)
            result = sampler.generate_samples()
            
            standard_times.append(time.time() - start_time)
        
        # 计算统计信息
        avg_vectorized_time = np.mean(vectorized_times)
        avg_standard_time = np.mean(standard_times)
        speedup_ratio = avg_standard_time / avg_vectorized_time if avg_vectorized_time > 0 else 1.0
        
        # 更新性能统计
        self.performance_stats.update({
            'vectorized_time': avg_vectorized_time,
            'standard_time': avg_standard_time,
            'speedup_ratio': speedup_ratio,
            'samples_processed': n_samples * n_runs
        })
        
        benchmark_results = {
            'n_samples': n_samples,
            'n_runs': n_runs,
            'vectorized_times': vectorized_times,
            'standard_times': standard_times,
            'avg_vectorized_time': avg_vectorized_time,
            'avg_standard_time': avg_standard_time,
            'speedup_ratio': speedup_ratio,
            'vectorized_std': np.std(vectorized_times),
            'standard_std': np.std(standard_times),
            'samples_per_second_vectorized': n_samples / avg_vectorized_time,
            'samples_per_second_standard': n_samples / avg_standard_time
        }
        
        logger.info(f"基准测试完成: 向量化版本 {avg_vectorized_time:.3f}s, "
                   f"标准版本 {avg_standard_time:.3f}s, 加速比 {speedup_ratio:.2f}x")
        
        return benchmark_results
    
    def get_optimization_recommendations(self) -> List[str]:
        """获取优化建议"""
        recommendations = []
        
        if self.performance_stats['speedup_ratio'] < 1.2:
            recommendations.append("向量化加速效果不明显，考虑调整配置或使用标准方法")
        elif self.performance_stats['speedup_ratio'] > 3.0:
            recommendations.append("向量化效果显著，建议继续使用")
        
        if not self.config.enable_numba:
            recommendations.append("考虑启用Numba以获得更好的性能")
        
        if not self.config.enable_parallel and self.n_threads > 1:
            recommendations.append("考虑启用并行处理以提高大规模抽样性能")
        
        if self.config.chunk_size > 50000:
            recommendations.append("块大小较大，可能导致内存压力，考虑减小chunk_size")
        elif self.config.chunk_size < 1000:
            recommendations.append("块大小较小，可能影响向量化效率，考虑增大chunk_size")
        
        return recommendations if recommendations else ["当前配置已优化"]
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        return {
            'config': {
                'enable_numba': self.config.enable_numba,
                'enable_parallel': self.config.enable_parallel,
                'chunk_size': self.config.chunk_size,
                'n_threads': self.n_threads
            },
            'performance_stats': self.performance_stats.copy(),
            'recommendations': self.get_optimization_recommendations()
        }

def create_optimized_sampler(parameters: List[ParameterDefinition],
                           enable_vectorization: bool = True,
                           enable_parallel: bool = True) -> Union[VectorizedSampler, LatinHypercubeSampler]:
    """
    创建优化的抽样器
    
    Args:
        parameters: 参数定义列表
        enable_vectorization: 启用向量化
        enable_parallel: 启用并行处理
        
    Returns:
        优化的抽样器实例
    """
    if enable_vectorization:
        config = VectorizationConfig(
            enable_numba=True,
            enable_parallel=enable_parallel,
            chunk_size=10000,
            n_threads=None
        )
        return VectorizedSampler(config)
    else:
        # 返回标准抽样器
        sampling_config = SamplingConfig(
            parameters=parameters,
            n_samples=1000,  # 默认值，实际使用时会被覆盖
            random_seed=42,
            sampling_method="lhs",
            optimization_criterion="maximin"
        )
        return LatinHypercubeSampler(sampling_config)