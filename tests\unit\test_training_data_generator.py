"""
训练数据生成器模块单元测试
"""

import pytest
import numpy as np
import tempfile
import os
import shutil
from unittest.mock import Mock, patch, MagicMock
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler

from src.calibration.training_data_generator import (
    TrainingDataGenerator, DataGenerationConfig, TrainingDataset
)


class TestDataGenerationConfig:
    """DataGenerationConfig测试类"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = DataGenerationConfig()
        
        assert config.n_samples == 10000
        assert config.batch_size == 100
        assert config.test_size == 0.2
        assert config.validation_size == 0.2
        assert config.random_seed == 42
        assert config.use_cache is True
        assert config.cache_dir == "data/cache"
        assert config.n_workers == 4
        assert config.preprocessing_method == "robust"
        assert config.add_noise is False
        assert config.noise_level == 0.01
        assert config.augmentation_factor == 1.0
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = DataGenerationConfig(
            n_samples=5000,
            batch_size=50,
            test_size=0.15,
            validation_size=0.15,
            random_seed=123,
            use_cache=False,
            preprocessing_method="standard",
            add_noise=True,
            noise_level=0.05,
            augmentation_factor=1.5
        )
        
        assert config.n_samples == 5000
        assert config.batch_size == 50
        assert config.test_size == 0.15
        assert config.validation_size == 0.15
        assert config.random_seed == 123
        assert config.use_cache is False
        assert config.preprocessing_method == "standard"
        assert config.add_noise is True
        assert config.noise_level == 0.05
        assert config.augmentation_factor == 1.5


class TestTrainingDataset:
    """TrainingDataset测试类"""
    
    def test_training_dataset_creation(self):
        """测试训练数据集创建"""
        X_train = np.random.randn(100, 10)
        X_val = np.random.randn(20, 10)
        X_test = np.random.randn(20, 10)
        y_train = np.random.randn(100, 5)
        y_val = np.random.randn(20, 5)
        y_test = np.random.randn(20, 5)
        
        feature_names = [f"feature_{i}" for i in range(10)]
        target_names = [f"target_{i}" for i in range(5)]
        input_scaler = StandardScaler()
        output_scaler = StandardScaler()
        metadata = {"test": "data"}
        
        dataset = TrainingDataset(
            X_train=X_train,
            X_val=X_val,
            X_test=X_test,
            y_train=y_train,
            y_val=y_val,
            y_test=y_test,
            feature_names=feature_names,
            target_names=target_names,
            input_scaler=input_scaler,
            output_scaler=output_scaler,
            metadata=metadata
        )
        
        assert np.array_equal(dataset.X_train, X_train)
        assert np.array_equal(dataset.X_val, X_val)
        assert np.array_equal(dataset.X_test, X_test)
        assert np.array_equal(dataset.y_train, y_train)
        assert np.array_equal(dataset.y_val, y_val)
        assert np.array_equal(dataset.y_test, y_test)
        assert dataset.feature_names == feature_names
        assert dataset.target_names == target_names
        assert dataset.input_scaler == input_scaler
        assert dataset.output_scaler == output_scaler
        assert dataset.metadata == metadata


class TestTrainingDataGenerator:
    """TrainingDataGenerator测试类"""
    
    def setup_method(self):
        """测试前设置"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建配置
        self.config = DataGenerationConfig(
            n_samples=1000,  # 减少样本数以加快测试
            batch_size=50,
            cache_dir=os.path.join(self.temp_dir, "cache"),
            use_cache=False  # 测试时不使用缓存
        )
        
        # 创建模拟的模拟引擎
        self.mock_simulation_engine = Mock()
        
        # 创建数据生成器
        self.generator = TrainingDataGenerator(
            config=self.config,
            simulation_engine=self.mock_simulation_engine
        )
    
    def teardown_method(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_initialization(self):
        """测试初始化"""
        assert self.generator.config == self.config
        assert self.generator.simulation_engine == self.mock_simulation_engine
        assert self.generator.logger is not None
    
    @patch('src.calibration.parameter_sampler.LatinHypercubeSampler')
    def test_generate_parameter_samples(self, mock_sampler_class):
        """测试参数样本生成"""
        # 设置模拟采样器
        mock_sampler = Mock()
        mock_sampler_class.return_value = mock_sampler
        
        # 模拟采样结果
        mock_samples = np.random.randn(1000, 20)
        mock_result = Mock()
        mock_result.samples = mock_samples
        mock_sampler.generate_samples.return_value = mock_result
        
        # 生成参数样本
        samples = self.generator._generate_parameter_samples()
        
        assert samples.shape == (1000, 20)
        mock_sampler.generate_samples.assert_called_once()
    
    def test_run_simulation_batch(self):
        """测试批量模拟运行"""
        # 设置模拟引擎返回值
        parameter_batch = np.random.randn(10, 20)
        mock_results = np.random.randn(10, 24)  # 24个输出指标
        
        self.mock_simulation_engine.run_batch.return_value = mock_results
        
        # 运行批量模拟
        results = self.generator._run_simulation_batch(parameter_batch)
        
        assert results.shape == (10, 24)
        self.mock_simulation_engine.run_batch.assert_called_once_with(
            parameter_batch
        )
    
    def test_preprocess_data_standard(self):
        """测试标准预处理"""
        # 创建测试数据
        X_raw = np.random.randn(100, 10)
        y_raw = np.random.randn(100, 5)
        
        # 设置标准预处理
        self.generator.config.preprocessing_method = "standard"
        
        X_processed, y_processed = self.generator._preprocess_data(X_raw, y_raw)
        
        # 检查数据形状
        assert X_processed.shape == X_raw.shape
        assert y_processed.shape == y_raw.shape
        
        # 检查标准化效果（均值接近0，标准差接近1）
        assert abs(np.mean(X_processed)) < 0.1
        assert abs(np.std(X_processed) - 1.0) < 0.1
    
    def test_preprocess_data_robust(self):
        """测试鲁棒预处理"""
        # 创建测试数据
        X_raw = np.random.randn(100, 10)
        y_raw = np.random.randn(100, 5)
        
        # 设置鲁棒预处理
        self.generator.config.preprocessing_method = "robust"
        
        X_processed, y_processed = self.generator._preprocess_data(X_raw, y_raw)
        
        # 检查数据形状
        assert X_processed.shape == X_raw.shape
        assert y_processed.shape == y_raw.shape
    
    def test_preprocess_data_minmax(self):
        """测试最小-最大预处理"""
        # 创建测试数据
        X_raw = np.random.randn(100, 10)
        y_raw = np.random.randn(100, 5)
        
        # 设置最小-最大预处理
        self.generator.config.preprocessing_method = "minmax"
        
        X_processed, y_processed = self.generator._preprocess_data(X_raw, y_raw)
        
        # 检查数据形状
        assert X_processed.shape == X_raw.shape
        assert y_processed.shape == y_raw.shape
        
        # 检查缩放效果（应该在[0,1]范围内）
        assert np.min(X_processed) >= -0.1  # 允许小的数值误差
        assert np.max(X_processed) <= 1.1
    
    def test_preprocess_data_invalid_method(self):
        """测试无效预处理方法"""
        X_raw = np.random.randn(100, 10)
        y_raw = np.random.randn(100, 5)
        
        self.generator.config.preprocessing_method = "invalid"
        
        with pytest.raises(ValueError, match="不支持的预处理方法"):
            self.generator._preprocess_data(X_raw, y_raw)
    
    def test_split_data(self):
        """测试数据分割"""
        # 创建测试数据
        X = np.random.randn(1000, 10)
        y = np.random.randn(1000, 5)
        
        # 分割数据
        splits = self.generator._split_data(X, y)
        
        X_train, X_val, X_test, y_train, y_val, y_test = splits
        
        # 检查分割比例
        total_samples = len(X)
        expected_test = int(total_samples * self.config.test_size)
        expected_val = int((total_samples - expected_test) * self.config.validation_size)
        expected_train = total_samples - expected_test - expected_val
        
        assert len(X_train) == expected_train
        assert len(X_val) == expected_val
        assert len(X_test) == expected_test
        assert len(y_train) == expected_train
        assert len(y_val) == expected_val
        assert len(y_test) == expected_test
    
    def test_add_noise(self):
        """测试添加噪声"""
        # 创建测试数据
        X_clean = np.ones((100, 10))
        y_clean = np.ones((100, 5))
        
        # 设置噪声参数
        self.generator.config.add_noise = True
        self.generator.config.noise_level = 0.1
        
        X_noisy, y_noisy = self.generator._add_noise(X_clean, y_clean)
        
        # 检查噪声效果
        assert not np.array_equal(X_clean, X_noisy)
        assert not np.array_equal(y_clean, y_noisy)
        
        # 检查噪声水平
        X_noise = X_noisy - X_clean
        assert np.std(X_noise) > 0
        assert np.std(X_noise) < 0.2  # 应该在合理范围内
    
    def test_augment_data(self):
        """测试数据增强"""
        # 创建测试数据
        X_original = np.random.randn(100, 10)
        y_original = np.random.randn(100, 5)
        
        # 设置增强因子
        self.generator.config.augmentation_factor = 2.0
        
        X_augmented, y_augmented = self.generator._augment_data(
            X_original, y_original
        )
        
        # 检查增强后的数据量
        expected_size = int(len(X_original) * 2.0)
        assert len(X_augmented) == expected_size
        assert len(y_augmented) == expected_size
        
        # 检查原始数据是否包含在增强数据中
        assert np.array_equal(X_augmented[:len(X_original)], X_original)
        assert np.array_equal(y_augmented[:len(y_original)], y_original)
    
    def test_generate_cache_key(self):
        """测试缓存键生成"""
        cache_key = self.generator._generate_cache_key()
        
        assert isinstance(cache_key, str)
        assert len(cache_key) > 0
        
        # 相同配置应该生成相同的键
        cache_key2 = self.generator._generate_cache_key()
        assert cache_key == cache_key2
        
        # 不同配置应该生成不同的键
        self.generator.config.n_samples = 2000
        cache_key3 = self.generator._generate_cache_key()
        assert cache_key != cache_key3
    
    def test_save_to_cache(self):
        """测试保存到缓存"""
        # 创建测试数据集
        dataset = TrainingDataset(
            X_train=np.random.randn(100, 10),
            X_val=np.random.randn(20, 10),
            X_test=np.random.randn(20, 10),
            y_train=np.random.randn(100, 5),
            y_val=np.random.randn(20, 5),
            y_test=np.random.randn(20, 5),
            feature_names=[f"feature_{i}" for i in range(10)],
            target_names=[f"target_{i}" for i in range(5)],
            input_scaler=StandardScaler(),
            output_scaler=StandardScaler(),
            metadata={"test": "data"}
        )
        
        cache_key = "test_cache_key"
        
        # 保存到缓存
        self.generator._save_to_cache(dataset, cache_key)
        
        # 检查缓存文件是否存在
        cache_path = os.path.join(
            self.generator.config.cache_dir,
            f"{cache_key}.pkl"
        )
        assert os.path.exists(cache_path)
    
    def test_load_from_cache(self):
        """测试从缓存加载"""
        # 先保存数据到缓存
        original_dataset = TrainingDataset(
            X_train=np.random.randn(100, 10),
            X_val=np.random.randn(20, 10),
            X_test=np.random.randn(20, 10),
            y_train=np.random.randn(100, 5),
            y_val=np.random.randn(20, 5),
            y_test=np.random.randn(20, 5),
            feature_names=[f"feature_{i}" for i in range(10)],
            target_names=[f"target_{i}" for i in range(5)],
            input_scaler=StandardScaler(),
            output_scaler=StandardScaler(),
            metadata={"test": "data"}
        )
        
        cache_key = "test_load_cache_key"
        self.generator._save_to_cache(original_dataset, cache_key)
        
        # 从缓存加载
        loaded_dataset = self.generator._load_from_cache(cache_key)
        
        assert loaded_dataset is not None
        assert np.array_equal(loaded_dataset.X_train, original_dataset.X_train)
        assert np.array_equal(loaded_dataset.y_train, original_dataset.y_train)
        assert loaded_dataset.feature_names == original_dataset.feature_names
        assert loaded_dataset.metadata == original_dataset.metadata
    
    def test_check_cache(self):
        """测试缓存检查"""
        cache_key = "nonexistent_key"
        
        # 不存在的缓存应该返回False
        assert self.generator._check_cache(cache_key) is False
        
        # 创建缓存文件
        cache_dir = self.generator.config.cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        cache_path = os.path.join(cache_dir, f"{cache_key}.pkl")
        
        with open(cache_path, 'w') as f:
            f.write("dummy")
        
        # 存在的缓存应该返回True
        assert self.generator._check_cache(cache_key) is True
    
    @patch('src.calibration.parameter_sampler.LatinHypercubeSampler')
    def test_generate_raw_data(self, mock_sampler_class):
        """测试原始数据生成"""
        # 设置模拟采样器
        mock_sampler = Mock()
        mock_sampler_class.return_value = mock_sampler
        mock_samples = np.random.randn(1000, 20)
        mock_result = Mock()
        mock_result.samples = mock_samples
        mock_sampler.generate_samples.return_value = mock_result
        
        # 设置模拟引擎
        mock_simulation_results = np.random.randn(1000, 24)
        self.mock_simulation_engine.run_batch.return_value = mock_simulation_results
        
        # 生成原始数据
        X_raw, y_raw = self.generator._generate_raw_data()
        
        assert X_raw.shape == (1000, 20)
        assert y_raw.shape == (1000, 24)
    
    def test_create_synthetic_simulation_engine(self):
        """测试创建合成模拟引擎"""
        engine = TrainingDataGenerator.create_synthetic_simulation_engine()
        
        assert engine is not None
        
        # 测试合成引擎
        test_params = np.random.randn(10, 20)
        results = engine.run_batch(test_params)
        
        assert results.shape == (10, 24)
        assert not np.isnan(results).any()


if __name__ == "__main__":
    pytest.main([__file__])
