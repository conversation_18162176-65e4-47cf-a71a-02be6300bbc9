"""
多策略比较分析模块测试

测试策略比较器的各项功能，包括：
- 策略比较分析
- 成本效益前沿分析
- 策略排序和筛选
- 决策支持生成

作者：<PERSON> (Dev Agent)
创建日期：2025-01-07
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch

from src.modules.economics.strategy_comparison import (
    StrategyComparator, StrategyData, ComparisonResult, FrontierAnalysis,
    StrategyRanking, create_example_strategies
)
from src.modules.economics.icer_calculator import ICERInterpretation


class TestStrategyData:
    """测试策略数据结构"""
    
    def test_strategy_data_creation(self):
        """测试策略数据创建"""
        strategy = StrategyData(
            name="test_strategy",
            cost=1000.0,
            qalys=15.5,
            description="测试策略"
        )
        
        assert strategy.name == "test_strategy"
        assert strategy.cost == 1000.0
        assert strategy.qalys == 15.5
        assert strategy.description == "测试策略"
        assert strategy.cost_samples is None
        assert strategy.qaly_samples is None


class TestStrategyComparator:
    """测试策略比较器"""
    
    @pytest.fixture
    def comparator(self):
        """创建策略比较器实例"""
        return StrategyComparator(wtp_threshold=150000, reference_strategy="no_screening")
    
    @pytest.fixture
    def sample_strategies(self):
        """创建示例策略数据"""
        strategies = [
            StrategyData(name="no_screening", cost=0, qalys=15.0),
            StrategyData(name="annual_fobt", cost=2000, qalys=15.5),
            StrategyData(name="colonoscopy", cost=8000, qalys=16.0),
            StrategyData(name="expensive_strategy", cost=20000, qalys=16.1)
        ]
        
        # 添加不确定性样本
        np.random.seed(42)
        for strategy in strategies:
            strategy.cost_samples = np.random.normal(strategy.cost, strategy.cost * 0.1, 100)
            strategy.qaly_samples = np.random.normal(strategy.qalys, strategy.qalys * 0.05, 100)
            strategy.cost_samples = np.maximum(strategy.cost_samples, 0)
            strategy.qaly_samples = np.maximum(strategy.qaly_samples, 0)
        
        return strategies
    
    def test_comparator_initialization(self, comparator):
        """测试比较器初始化"""
        assert comparator.wtp_threshold == 150000
        assert comparator.reference_strategy == "no_screening"
        assert comparator.icer_calculator is not None
        assert comparator.nhb_calculator is not None
        assert comparator.ceac_generator is not None
    
    def test_compare_strategies_basic(self, comparator, sample_strategies):
        """测试基础策略比较功能"""
        results = comparator.compare_strategies(sample_strategies, include_uncertainty=False)
        
        assert len(results) == 4
        assert all(isinstance(result, ComparisonResult) for result in results)
        
        # 检查参考策略
        no_screening_result = next(r for r in results if r.strategy_name == "no_screening")
        assert no_screening_result.incremental_cost == 0
        assert no_screening_result.incremental_qalys == 0
        assert no_screening_result.icer is None
    
    def test_compare_strategies_with_uncertainty(self, comparator, sample_strategies):
        """测试包含不确定性的策略比较"""
        results = comparator.compare_strategies(sample_strategies, include_uncertainty=True)
        
        assert len(results) == 4
        # 验证排名已设置
        assert all(result.rank_by_icer > 0 for result in results)
        assert all(result.rank_by_nhb > 0 for result in results)
    
    def test_analyze_efficiency_frontier(self, comparator, sample_strategies):
        """测试成本效益前沿分析"""
        frontier_analysis = comparator.analyze_efficiency_frontier(sample_strategies)
        
        assert isinstance(frontier_analysis, FrontierAnalysis)
        assert len(frontier_analysis.frontier_strategies) > 0
        assert len(frontier_analysis.frontier_points) == len(frontier_analysis.frontier_strategies)
        assert len(frontier_analysis.efficiency_frontier) == len(frontier_analysis.frontier_strategies)
        
        # 验证前沿策略按成本排序
        frontier_costs = [point[0] for point in frontier_analysis.frontier_points]
        assert frontier_costs == sorted(frontier_costs)
    
    def test_frontier_analysis_dominance(self, comparator):
        """测试前沿分析中的占优关系"""
        # 创建包含占优关系的策略
        strategies = [
            StrategyData(name="dominated", cost=5000, qalys=15.0),  # 被占优
            StrategyData(name="dominant", cost=3000, qalys=15.5),   # 占优
            StrategyData(name="reference", cost=0, qalys=14.0)      # 参考
        ]
        
        frontier_analysis = comparator.analyze_efficiency_frontier(strategies)
        
        # 被占优策略不应在前沿上
        assert "dominated" in frontier_analysis.dominated_strategies
        assert "dominated" not in frontier_analysis.frontier_strategies
        
        # 占优策略应在前沿上
        assert "dominant" in frontier_analysis.frontier_strategies
    
    def test_rank_strategies_by_nhb(self, comparator, sample_strategies):
        """测试按NHB排序策略"""
        ranking = comparator.rank_strategies(sample_strategies, StrategyRanking.BY_NHB)
        
        assert len(ranking) == 4
        assert all('rank' in item for item in ranking)
        assert all('nhb' in item for item in ranking)
        
        # 验证排序正确性（NHB降序）
        nhb_values = [item['nhb'] for item in ranking]
        assert nhb_values == sorted(nhb_values, reverse=True)
    
    def test_rank_strategies_by_cost(self, comparator, sample_strategies):
        """测试按成本排序策略"""
        ranking = comparator.rank_strategies(sample_strategies, StrategyRanking.BY_COST)
        
        assert len(ranking) == 4
        
        # 验证排序正确性（成本升序）
        costs = [item['cost'] for item in ranking]
        assert costs == sorted(costs)
    
    def test_rank_strategies_by_effectiveness(self, comparator, sample_strategies):
        """测试按效果排序策略"""
        ranking = comparator.rank_strategies(sample_strategies, StrategyRanking.BY_EFFECTIVENESS)
        
        assert len(ranking) == 4
        
        # 验证排序正确性（QALY降序）
        qalys = [item['qalys'] for item in ranking]
        assert qalys == sorted(qalys, reverse=True)
    
    def test_rank_strategies_by_icer(self, comparator, sample_strategies):
        """测试按ICER排序策略"""
        ranking = comparator.rank_strategies(sample_strategies, StrategyRanking.BY_ICER)
        
        assert len(ranking) == 4
        assert all('icer' in item for item in ranking)
        assert all('rank' in item for item in ranking)
    
    def test_filter_strategies_by_cost(self, comparator, sample_strategies):
        """测试按成本筛选策略"""
        criteria = {'max_cost': 5000}
        filtered = comparator.filter_strategies(sample_strategies, criteria)
        
        assert all(strategy.cost <= 5000 for strategy in filtered)
        assert len(filtered) < len(sample_strategies)
    
    def test_filter_strategies_by_effectiveness(self, comparator, sample_strategies):
        """测试按效果筛选策略"""
        criteria = {'min_qalys': 15.5}
        filtered = comparator.filter_strategies(sample_strategies, criteria)
        
        assert all(strategy.qalys >= 15.5 for strategy in filtered)
    
    def test_filter_strategies_cost_effective_only(self, comparator, sample_strategies):
        """测试仅筛选成本效益可接受的策略"""
        criteria = {'only_cost_effective': True}
        filtered = comparator.filter_strategies(sample_strategies, criteria)
        
        # 验证筛选结果
        assert len(filtered) <= len(sample_strategies)
    
    def test_filter_strategies_frontier_only(self, comparator, sample_strategies):
        """测试仅筛选前沿策略"""
        criteria = {'only_frontier': True}
        filtered = comparator.filter_strategies(sample_strategies, criteria)
        
        # 验证所有筛选出的策略都在前沿上
        frontier_analysis = comparator.analyze_efficiency_frontier(sample_strategies)
        filtered_names = [s.name for s in filtered]
        
        for name in filtered_names:
            assert name in frontier_analysis.frontier_strategies
    
    def test_prepare_visualization_data(self, comparator, sample_strategies):
        """测试准备可视化数据"""
        viz_data = comparator.prepare_visualization_data(sample_strategies, include_uncertainty=True)
        
        # 验证数据结构
        assert 'scatter_plot' in viz_data
        assert 'frontier_analysis' in viz_data
        assert 'rankings' in viz_data
        assert 'comparison_table' in viz_data
        
        # 验证散点图数据
        scatter = viz_data['scatter_plot']
        assert len(scatter['strategies']) == 4
        assert len(scatter['costs']) == 4
        assert len(scatter['qalys']) == 4
        assert len(scatter['is_frontier']) == 4
        
        # 验证排名数据
        rankings = viz_data['rankings']
        assert 'by_nhb' in rankings
        assert 'by_cost' in rankings
        assert 'by_effectiveness' in rankings
        
        # 验证比较表数据
        table = viz_data['comparison_table']
        assert len(table) == 4
        assert all('strategy' in row for row in table)
        assert all('cost' in row for row in table)
        assert all('qalys' in row for row in table)
    
    def test_prepare_visualization_data_with_ceac(self, comparator, sample_strategies):
        """测试包含CEAC的可视化数据准备"""
        viz_data = comparator.prepare_visualization_data(sample_strategies, include_uncertainty=True)
        
        # 应该包含CEAC数据（因为策略有不确定性样本）
        assert 'ceac' in viz_data
        ceac = viz_data['ceac']
        assert 'wtp_thresholds' in ceac
        assert 'probabilities' in ceac
        assert 'strategies' in ceac
    
    def test_generate_decision_support_basic(self, comparator, sample_strategies):
        """测试基础决策支持生成"""
        decision_support = comparator.generate_decision_support(sample_strategies)
        
        # 验证数据结构
        assert 'recommendations' in decision_support
        assert 'frontier_analysis' in decision_support
        assert 'risk_analysis' in decision_support
        assert 'sensitivity_summary' in decision_support
        assert 'decision_matrix' in decision_support
        
        # 验证推荐列表
        recommendations = decision_support['recommendations']
        assert len(recommendations) > 0
        assert all('type' in rec for rec in recommendations)
        assert all('strategy' in rec for rec in recommendations)
        assert all('reason' in rec for rec in recommendations)
        assert all('priority' in rec for rec in recommendations)
        
        # 验证决策矩阵
        matrix = decision_support['decision_matrix']
        assert len(matrix) == 4
        assert all('strategy' in row for row in matrix)
        assert all('cost_effectiveness' in row for row in matrix)
    
    def test_generate_decision_support_with_budget(self, comparator, sample_strategies):
        """测试带预算约束的决策支持"""
        criteria = {'budget_constraint': 5000}
        decision_support = comparator.generate_decision_support(sample_strategies, criteria)
        
        # 应该包含预算约束推荐
        recommendations = decision_support['recommendations']
        budget_recs = [r for r in recommendations if r['type'] == 'budget_constrained']
        
        if budget_recs:  # 如果有符合预算的策略
            assert len(budget_recs) > 0
    
    def test_dominance_relationships(self, comparator):
        """测试占优关系分析"""
        # 创建明确的占优关系
        strategies = [
            StrategyData(name="reference", cost=0, qalys=14.0),
            StrategyData(name="dominated", cost=5000, qalys=14.5),    # 被占优
            StrategyData(name="dominant", cost=3000, qalys=15.0),     # 占优
        ]
        
        results = comparator.compare_strategies(strategies, include_uncertainty=False)
        
        # 找到占优和被占优策略的结果
        dominant_result = next(r for r in results if r.strategy_name == "dominant")
        dominated_result = next(r for r in results if r.strategy_name == "dominated")
        
        # 验证占优关系
        assert len(dominant_result.dominates) > 0
        assert dominated_result.dominated_by is not None


class TestCreateExampleStrategies:
    """测试示例策略创建函数"""
    
    def test_create_example_strategies(self):
        """测试创建示例策略"""
        strategies = create_example_strategies()
        
        assert len(strategies) == 5
        assert all(isinstance(s, StrategyData) for s in strategies)
        
        # 验证策略名称
        strategy_names = [s.name for s in strategies]
        expected_names = [
            "no_screening", "annual_fobt", "biennial_fobt", 
            "colonoscopy_10y", "flexible_sigmoidoscopy_5y"
        ]
        assert all(name in strategy_names for name in expected_names)
        
        # 验证不确定性样本
        assert all(s.cost_samples is not None for s in strategies)
        assert all(s.qaly_samples is not None for s in strategies)
        assert all(len(s.cost_samples) == 1000 for s in strategies)
        assert all(len(s.qaly_samples) == 1000 for s in strategies)
        
        # 验证样本非负
        for strategy in strategies:
            assert all(cost >= 0 for cost in strategy.cost_samples)
            assert all(qaly >= 0 for qaly in strategy.qaly_samples)


class TestIntegration:
    """集成测试"""
    
    def test_full_workflow(self):
        """测试完整工作流程"""
        # 创建策略数据
        strategies = create_example_strategies()
        
        # 初始化比较器
        comparator = StrategyComparator(wtp_threshold=150000)
        
        # 执行完整分析流程
        
        # 1. 策略比较
        comparison_results = comparator.compare_strategies(strategies)
        assert len(comparison_results) == 5
        
        # 2. 前沿分析
        frontier_analysis = comparator.analyze_efficiency_frontier(strategies)
        assert len(frontier_analysis.frontier_strategies) > 0
        
        # 3. 策略排序
        nhb_ranking = comparator.rank_strategies(strategies, StrategyRanking.BY_NHB)
        assert len(nhb_ranking) == 5
        
        # 4. 策略筛选
        filtered_strategies = comparator.filter_strategies(
            strategies, 
            {'only_cost_effective': True}
        )
        assert len(filtered_strategies) <= 5
        
        # 5. 可视化数据准备
        viz_data = comparator.prepare_visualization_data(strategies)
        assert 'scatter_plot' in viz_data
        assert 'ceac' in viz_data
        
        # 6. 决策支持
        decision_support = comparator.generate_decision_support(strategies)
        assert len(decision_support['recommendations']) > 0
    
    def test_edge_cases(self):
        """测试边界情况"""
        comparator = StrategyComparator()
        
        # 空策略列表
        with pytest.raises(IndexError):
            comparator.compare_strategies([])
        
        # 单个策略
        single_strategy = [StrategyData(name="single", cost=1000, qalys=15.0)]
        results = comparator.compare_strategies(single_strategy)
        assert len(results) == 1
        
        # 相同成本和效果的策略
        identical_strategies = [
            StrategyData(name="strategy1", cost=1000, qalys=15.0),
            StrategyData(name="strategy2", cost=1000, qalys=15.0)
        ]
        results = comparator.compare_strategies(identical_strategies)
        assert len(results) == 2
    
    def test_performance_with_large_dataset(self):
        """测试大数据集性能"""
        # 创建较大的策略集
        strategies = []
        np.random.seed(42)
        
        for i in range(20):
            cost = np.random.uniform(0, 10000)
            qalys = np.random.uniform(14, 17)
            
            strategy = StrategyData(
                name=f"strategy_{i}",
                cost=cost,
                qalys=qalys
            )
            
            # 添加不确定性样本
            strategy.cost_samples = np.random.normal(cost, cost * 0.1, 500)
            strategy.qaly_samples = np.random.normal(qalys, qalys * 0.05, 500)
            strategy.cost_samples = np.maximum(strategy.cost_samples, 0)
            strategy.qaly_samples = np.maximum(strategy.qaly_samples, 0)
            
            strategies.append(strategy)
        
        comparator = StrategyComparator()
        
        # 执行分析（应该在合理时间内完成）
        import time
        start_time = time.time()
        
        results = comparator.compare_strategies(strategies)
        viz_data = comparator.prepare_visualization_data(strategies)
        decision_support = comparator.generate_decision_support(strategies)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 验证结果
        assert len(results) == 20
        assert 'ceac' in viz_data
        assert len(decision_support['recommendations']) > 0
        
        # 性能要求：20个策略的完整分析应在10秒内完成
        assert execution_time < 10, f"分析耗时{execution_time:.2f}秒，超过10秒限制"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])