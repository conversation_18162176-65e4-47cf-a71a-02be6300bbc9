"""
模型评估器模块
提供全面的模型性能评估和验证功能
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    mean_squared_error, mean_absolute_error, r2_score,
    mean_absolute_percentage_error
)
from sklearn.model_selection import KFold, cross_val_score
from scipy import stats
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
import warnings
import json
import os
from datetime import datetime
import time

# 导入TensorFlow相关
import tensorflow as tf
from tensorflow import keras

# 设置中文字体
plt.rcParams['font.sans - seri'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class ModelEvaluator:
    """模型评估器类，提供全面的模型性能评估功能"""

    def __init__(
        self,
        model: keras.Model,
        input_scaler: Optional[Any] = None,
        output_scaler: Optional[Any] = None,
        target_names: Optional[List[str]] = None
    ):
        """
        初始化模型评估器

        Args:
            model: 要评估的Keras模型
            input_scaler: 输入数据标准化器
            output_scaler: 输出数据标准化器
            target_names: 目标变量名称列表
        """
        self.model = model
        self.input_scaler = input_scaler
        self.output_scaler = output_scaler
        self.target_names = target_names

        # 设置日志
        self.logger = logging.getLogger(__name__)

        # 评估结果存储
        self.evaluation_results = {}
        self.prediction_cache = {}

        self.logger.info("模型评估器初始化完成")

    def evaluate_comprehensive(
        self,
        X_test: np.ndarray,
        y_test: np.ndarray,
        X_train: Optional[np.ndarray] = None,
        y_train: Optional[np.ndarray] = None,
        X_val: Optional[np.ndarray] = None,
        y_val: Optional[np.ndarray] = None,
        detailed: bool = True
    ) -> Dict[str, Any]:
        """
        全面评估模型性能

        Args:
            X_test: 测试集输入
            y_test: 测试集目标
            X_train: 训练集输入（可选）
            y_train: 训练集目标（可选）
            X_val: 验证集输入（可选）
            y_val: 验证集目标（可选）
            detailed: 是否进行详细评估

        Returns:
            Dict[str, Any]: 全面的评估结果
        """
        self.logger.info("开始全面模型评估...")

        results = {}

        # 基础性能评估
        test_metrics = self.evaluate_basic_metrics(X_test, y_test, 'test')
        results.update(test_metrics)

        if X_train is not None and y_train is not None:
            train_metrics = self.evaluate_basic_metrics(X_train, y_train, 'train')
            results.update(train_metrics)

        if X_val is not None and y_val is not None:
            val_metrics = self.evaluate_basic_metrics(X_val, y_val, 'val')
            results.update(val_metrics)

        if detailed:
            # 详细评估
            detailed_results = self.evaluate_detailed_metrics(X_test, y_test)
            results.update(detailed_results)

            # 残差分析
            residual_analysis = self.analyze_residuals(X_test, y_test)
            results['residual_analysis'] = residual_analysis

            # 预测区间分析
            prediction_intervals = self.calculate_prediction_intervals(X_test, y_test)
            results['prediction_intervals'] = prediction_intervals

            # 特征重要性分析（如果可能）
            if hasattr(self.model, 'layers') and len(self.model.layers) > 0:
                feature_importance = self.analyze_feature_importance(X_test)
                results['feature_importance'] = feature_importance

        self.evaluation_results = results
        self.logger.info("全面模型评估完成")

        return results

    def evaluate_basic_metrics(
        self,
        X: np.ndarray,
        y_true: np.ndarray,
        dataset_name: str = 'test'
    ) -> Dict[str, float]:
        """
        评估基础性能指标

        Args:
            X: 输入数据
            y_true: 真实目标值
            dataset_name: 数据集名称

        Returns:
            Dict[str, float]: 基础性能指标
        """
        # 预测
        y_pred = self.model.predict(X, verbose=0)

        # 反标准化（如果需要）
        if self.output_scaler is not None:
            if len(y_true.shape) == 1:
                y_true_orig = self.output_scaler.inverse_transform(y_true.reshape(-1, 1)).flatten()
                y_pred_orig = self.output_scaler.inverse_transform(y_pred.reshape(-1, 1)).flatten()
            else:
                y_true_orig = self.output_scaler.inverse_transform(y_true)
                y_pred_orig = self.output_scaler.inverse_transform(y_pred)
        else:
            y_true_orig = y_true
            y_pred_orig = y_pred.flatten() if len(y_pred.shape) > 1 else y_pred

        # 缓存预测结果
        self.prediction_cache[dataset_name] = {
            'y_true': y_true_orig,
            'y_pred': y_pred_orig
        }

        # 计算基础指标
        metrics = {}

        if len(y_true_orig.shape) == 1:
            # 单输出情况
            metrics.update(self._calculate_single_output_metrics(
                y_true_orig, y_pred_orig, dataset_name
            ))
        else:
            # 多输出情况
            metrics.update(self._calculate_multi_output_metrics(
                y_true_orig, y_pred_orig, dataset_name
            ))

        return metrics

    def _calculate_single_output_metrics(
        self,
        y_true: np.ndarray,
        y_pred: np.ndarray,
        prefix: str
    ) -> Dict[str, float]:
        """
        计算单输出指标

        Args:
            y_true: 真实值
            y_pred: 预测值
            prefix: 指标前缀

        Returns:
            Dict[str, float]: 单输出指标
        """
        metrics = {}

        # 基础回归指标
        metrics[f'{prefix}_mse'] = float(mean_squared_error(y_true, y_pred))
        metrics[f'{prefix}_rmse'] = float(np.sqrt(mean_squared_error(y_true, y_pred)))
        metrics[f'{prefix}_mae'] = float(mean_absolute_error(y_true, y_pred))
        metrics[f'{prefix}_r2'] = float(r2_score(y_true, y_pred))

        # MAPE（避免除零错误）
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            mape = np.mean(np.abs((y_true - y_pred) / (y_true + 1e - 8))) * 100
            metrics[f'{prefix}_mape'] = float(mape)

        # 调整R²
        n = len(y_true)
        p = 1  # 假设单特征，实际应该传入特征数
        adj_r2 = 1 - (1 - metrics[f'{prefix}_r2']) * (n - 1) / (n - p - 1)
        metrics[f'{prefix}_adj_r2'] = float(adj_r2)

        # 最大误差
        metrics[f'{prefix}_max_error'] = float(np.max(np.abs(y_true - y_pred)))

        # 中位数绝对误差
        metrics[f'{prefix}_median_ae'] = float(np.median(np.abs(y_true - y_pred)))

        return metrics

    def _calculate_multi_output_metrics(
        self,
        y_true: np.ndarray,
        y_pred: np.ndarray,
        prefix: str
    ) -> Dict[str, float]:
        """
        计算多输出指标

        Args:
            y_true: 真实值
            y_pred: 预测值
            prefix: 指标前缀

        Returns:
            Dict[str, float]: 多输出指标
        """
        metrics = {}

        # 整体指标
        metrics[f'{prefix}_mse'] = float(mean_squared_error(y_true, y_pred))
        metrics[f'{prefix}_rmse'] = float(np.sqrt(mean_squared_error(y_true, y_pred)))
        metrics[f'{prefix}_mae'] = float(mean_absolute_error(y_true, y_pred))
        metrics[f'{prefix}_r2'] = float(r2_score(y_true, y_pred))

        # 各维度指标
        n_outputs = y_true.shape[1]
        for i in range(n_outputs):
            target_name = self.target_names[i] if self.target_names else f'target_{i}'

            y_true_i = y_true[:, i]
            y_pred_i = y_pred[:, i]

            metrics[f'{prefix}_{target_name}_mse'] = float(mean_squared_error(y_true_i, y_pred_i))
            metrics[f'{prefix}_{target_name}_rmse'] = float(np.sqrt(mean_squared_error(y_true_i, y_pred_i)))
            metrics[f'{prefix}_{target_name}_mae'] = float(mean_absolute_error(y_true_i, y_pred_i))
            metrics[f'{prefix}_{target_name}_r2'] = float(r2_score(y_true_i, y_pred_i))

            # MAPE
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                mape = np.mean(np.abs((y_true_i - y_pred_i) / (y_true_i + 1e - 8))) * 100
                metrics[f'{prefix}_{target_name}_mape'] = float(mape)

        return metrics

    def evaluate_detailed_metrics(
        self,
        X_test: np.ndarray,
        y_test: np.ndarray
    ) -> Dict[str, Any]:
        """
        评估详细性能指标

        Args:
            X_test: 测试输入
            y_test: 测试目标

        Returns:
            Dict[str, Any]: 详细性能指标
        """
        results = {}

        # 获取预测结果
        if 'test' in self.prediction_cache:
            y_true = self.prediction_cache['test']['y_true']
            y_pred = self.prediction_cache['test']['y_pred']
        else:
            y_pred = self.model.predict(X_test, verbose=0)
            if self.output_scaler is not None:
                y_true = self.output_scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
                y_pred = self.output_scaler.inverse_transform(y_pred.reshape(-1, 1)).flatten()
            else:
                y_true = y_test
                y_pred = y_pred.flatten()

        # 统计分析
        results['statistical_analysis'] = self._statistical_analysis(y_true, y_pred)

        # 分布分析
        results['distribution_analysis'] = self._distribution_analysis(y_true, y_pred)

        # 误差分析
        results['error_analysis'] = self._error_analysis(y_true, y_pred)

        return results

    def _statistical_analysis(
        self,
        y_true: np.ndarray,
        y_pred: np.ndarray
    ) -> Dict[str, float]:
        """
        统计分析

        Args:
            y_true: 真实值
            y_pred: 预测值

        Returns:
            Dict[str, float]: 统计分析结果
        """
        residuals = y_true - y_pred

        analysis = {}

        # 残差统计
        analysis['residual_mean'] = float(np.mean(residuals))
        analysis['residual_std'] = float(np.std(residuals))
        analysis['residual_skewness'] = float(stats.skew(residuals))
        analysis['residual_kurtosis'] = float(stats.kurtosis(residuals))

        # 正态性检验
        _, p_value = stats.shapiro(residuals[:min(5000, len(residuals))])  # 限制样本数量
        analysis['residual_normality_p'] = float(p_value)
        analysis['residual_is_normal'] = p_value > 0.05

        # 相关性分析
        correlation, p_corr = stats.pearsonr(y_true, y_pred)
        analysis['correlation'] = float(correlation)
        analysis['correlation_p'] = float(p_corr)

        # Durbin - Watson检验（自相关）
        dw_stat = self._durbin_watson(residuals)
        analysis['durbin_watson'] = float(dw_stat)

        return analysis

    def _distribution_analysis(
        self,
        y_true: np.ndarray,
        y_pred: np.ndarray
    ) -> Dict[str, Any]:
        """
        分布分析

        Args:
            y_true: 真实值
            y_pred: 预测值

        Returns:
            Dict[str, Any]: 分布分析结果
        """
        analysis = {}

        # 真实值分布
        analysis['y_true_stats'] = {
            'mean': float(np.mean(y_true)),
            'std': float(np.std(y_true)),
            'min': float(np.min(y_true)),
            'max': float(np.max(y_true)),
            'q25': float(np.percentile(y_true, 25)),
            'q50': float(np.percentile(y_true, 50)),
            'q75': float(np.percentile(y_true, 75))
        }

        # 预测值分布
        analysis['y_pred_stats'] = {
            'mean': float(np.mean(y_pred)),
            'std': float(np.std(y_pred)),
            'min': float(np.min(y_pred)),
            'max': float(np.max(y_pred)),
            'q25': float(np.percentile(y_pred, 25)),
            'q50': float(np.percentile(y_pred, 50)),
            'q75': float(np.percentile(y_pred, 75))
        }

        # KS检验（分布一致性）
        ks_stat, ks_p = stats.ks_2samp(y_true, y_pred)
        analysis['ks_test'] = {
            'statistic': float(ks_stat),
            'p_value': float(ks_p),
            'distributions_similar': ks_p > 0.05
        }

        return analysis

    def _error_analysis(
        self,
        y_true: np.ndarray,
        y_pred: np.ndarray
    ) -> Dict[str, Any]:
        """
        误差分析

        Args:
            y_true: 真实值
            y_pred: 预测值

        Returns:
            Dict[str, Any]: 误差分析结果
        """
        residuals = y_true - y_pred
        abs_residuals = np.abs(residuals)

        analysis = {}

        # 误差分位数
        analysis['error_percentiles'] = {
            'p10': float(np.percentile(abs_residuals, 10)),
            'p25': float(np.percentile(abs_residuals, 25)),
            'p50': float(np.percentile(abs_residuals, 50)),
            'p75': float(np.percentile(abs_residuals, 75)),
            'p90': float(np.percentile(abs_residuals, 90)),
            'p95': float(np.percentile(abs_residuals, 95)),
            'p99': float(np.percentile(abs_residuals, 99))
        }

        # 大误差样本分析
        large_error_threshold = np.percentile(abs_residuals, 95)
        large_error_mask = abs_residuals > large_error_threshold
        analysis['large_error_analysis'] = {
            'threshold': float(large_error_threshold),
            'count': int(np.sum(large_error_mask)),
            'percentage': float(np.mean(large_error_mask) * 100),
            'mean_error': float(np.mean(abs_residuals[large_error_mask])),
            'max_error': float(np.max(abs_residuals))
        }

        return analysis

    def analyze_residuals(
        self,
        X_test: np.ndarray,
        y_test: np.ndarray
    ) -> Dict[str, Any]:
        """
        残差分析

        Args:
            X_test: 测试输入
            y_test: 测试目标

        Returns:
            Dict[str, Any]: 残差分析结果
        """
        # 获取预测结果
        if 'test' in self.prediction_cache:
            y_true = self.prediction_cache['test']['y_true']
            y_pred = self.prediction_cache['test']['y_pred']
        else:
            y_pred = self.model.predict(X_test, verbose=0)
            if self.output_scaler is not None:
                y_true = self.output_scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
                y_pred = self.output_scaler.inverse_transform(y_pred.reshape(-1, 1)).flatten()
            else:
                y_true = y_test
                y_pred = y_pred.flatten()

        residuals = y_true - y_pred

        analysis = {}

        # 残差基本统计
        analysis['basic_stats'] = {
            'mean': float(np.mean(residuals)),
            'std': float(np.std(residuals)),
            'min': float(np.min(residuals)),
            'max': float(np.max(residuals)),
            'range': float(np.max(residuals) - np.min(residuals))
        }

        # 残差与预测值的关系
        corr_pred, p_pred = stats.pearsonr(y_pred, residuals)
        analysis['residual_pred_correlation'] = {
            'correlation': float(corr_pred),
            'p_value': float(p_pred),
            'is_homoscedastic': abs(corr_pred) < 0.1 and p_pred > 0.05
        }

        # 残差自相关分析
        if len(residuals) > 10:
            autocorr_lag1 = np.corrcoef(residuals[:-1], residuals[1:])[0, 1]
            analysis['autocorrelation_lag1'] = float(autocorr_lag1)

        return analysis

    def calculate_prediction_intervals(
        self,
        X_test: np.ndarray,
        y_test: np.ndarray,
        confidence_level: float = 0.95
    ) -> Dict[str, Any]:
        """
        计算预测区间

        Args:
            X_test: 测试输入
            y_test: 测试目标
            confidence_level: 置信水平

        Returns:
            Dict[str, Any]: 预测区间分析
        """
        # 获取预测结果
        if 'test' in self.prediction_cache:
            y_true = self.prediction_cache['test']['y_true']
            y_pred = self.prediction_cache['test']['y_pred']
        else:
            y_pred = self.model.predict(X_test, verbose=0)
            if self.output_scaler is not None:
                y_true = self.output_scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
                y_pred = self.output_scaler.inverse_transform(y_pred.reshape(-1, 1)).flatten()
            else:
                y_true = y_test
                y_pred = y_pred.flatten()

        residuals = y_true - y_pred
        residual_std = np.std(residuals)

        # 计算置信区间
        alpha = 1 - confidence_level
        z_score = stats.norm.ppf(1 - alpha / 2)

        lower_bound = y_pred - z_score * residual_std
        upper_bound = y_pred + z_score * residual_std

        # 计算覆盖率
        coverage = np.mean((y_true >= lower_bound) & (y_true <= upper_bound))

        analysis = {
            'confidence_level': confidence_level,
            'residual_std': float(residual_std),
            'z_score': float(z_score),
            'coverage_rate': float(coverage),
            'expected_coverage': confidence_level,
            'coverage_difference': float(coverage - confidence_level),
            'interval_width_mean': float(np.mean(upper_bound - lower_bound)),
            'interval_width_std': float(np.std(upper_bound - lower_bound))
        }

        return analysis

    def analyze_feature_importance(
        self,
        X_test: np.ndarray,
        n_permutations: int = 10
    ) -> Dict[str, Any]:
        """
        分析特征重要性（基于排列重要性）

        Args:
            X_test: 测试输入
            n_permutations: 排列次数

        Returns:
            Dict[str, Any]: 特征重要性分析
        """
        self.logger.info("开始特征重要性分析...")

        # 基准性能
        baseline_pred = self.model.predict(X_test, verbose=0)
        if len(baseline_pred.shape) > 1:
            baseline_pred = baseline_pred.flatten()

        n_features = X_test.shape[1]
        importance_scores = np.zeros(n_features)

        for feature_idx in range(n_features):
            feature_scores = []

            for _ in range(n_permutations):
                # 复制数据并排列特定特征
                X_permuted = X_test.copy()
                X_permuted[:, feature_idx] = np.random.permutation(X_permuted[:, feature_idx])

                # 预测
                permuted_pred = self.model.predict(X_permuted, verbose=0)
                if len(permuted_pred.shape) > 1:
                    permuted_pred = permuted_pred.flatten()

                # 计算性能下降
                baseline_mse = np.mean((baseline_pred - baseline_pred) ** 2)  # 应该是0
                permuted_mse = np.mean((baseline_pred - permuted_pred) ** 2)

                feature_scores.append(permuted_mse)

            importance_scores[feature_idx] = np.mean(feature_scores)

        # 标准化重要性分数
        if np.sum(importance_scores) > 0:
            importance_scores = importance_scores / np.sum(importance_scores)

        analysis = {
            'feature_importance': importance_scores.tolist(),
            'top_features': np.argsort(importance_scores)[::-1][:10].tolist(),
            'importance_sum': float(np.sum(importance_scores))
        }

        self.logger.info("特征重要性分析完成")
        return analysis

    def cross_validate(
        self,
        X: np.ndarray,
        y: np.ndarray,
        cv_folds: int = 5,
        scoring: str = 'r2'
    ) -> Dict[str, Any]:
        """
        交叉验证评估

        Args:
            X: 输入数据
            y: 目标数据
            cv_folds: 交叉验证折数
            scoring: 评分指标

        Returns:
            Dict[str, Any]: 交叉验证结果
        """
        self.logger.info(f"开始 {cv_folds} 折交叉验证...")

        kfold = KFold(n_splits=cv_folds, shuffle=True, random_state=42)
        cv_results = {
            'fold_scores': [],
            'fold_metrics': []
        }

        for fold, (train_idx, val_idx) in enumerate(kfold.split(X)):
            self.logger.info(f"交叉验证 Fold {fold + 1}/{cv_folds}")

            X_train_fold, X_val_fold = X[train_idx], X[val_idx]
            y_train_fold, y_val_fold = y[train_idx], y[val_idx]

            # 克隆并训练模型
            fold_model = keras.models.clone_model(self.model)
            fold_model.compile(
                optimizer=self.model.optimizer,
                loss=self.model.loss,
                metrics=self.model.metrics
            )

            # 训练
            fold_model.fit(
                X_train_fold, y_train_fold,
                validation_data=(X_val_fold, y_val_fold),
                epochs=50,
                batch_size=32,
                verbose=0
            )

            # 评估
            y_pred_fold = fold_model.predict(X_val_fold, verbose=0)
            if len(y_pred_fold.shape) > 1:
                y_pred_fold = y_pred_fold.flatten()

            # 计算指标
            fold_r2 = r2_score(y_val_fold, y_pred_fold)
            fold_mse = mean_squared_error(y_val_fold, y_pred_fold)
            fold_mae = mean_absolute_error(y_val_fold, y_pred_fold)

            cv_results['fold_scores'].append(fold_r2)
            cv_results['fold_metrics'].append({
                'r2': float(fold_r2),
                'mse': float(fold_mse),
                'mae': float(fold_mae)
            })

        # 汇总结果
        scores = np.array(cv_results['fold_scores'])
        cv_results['mean_score'] = float(np.mean(scores))
        cv_results['std_score'] = float(np.std(scores))
        cv_results['min_score'] = float(np.min(scores))
        cv_results['max_score'] = float(np.max(scores))

        self.logger.info(f"
            "交叉验证完成，平均 {scoring}: {cv_results['mean_score']:.4f} ± {cv_results['std_score']:.4f}")

        return cv_results

    def plot_evaluation_results(
        self,
        save_dir: Optional[str] = None,
        figsize: Tuple[int, int] = (20, 15)
    ):
        """
        绘制评估结果图表

        Args:
            save_dir: 保存目录
            figsize: 图形大小
        """
        if not self.prediction_cache:
            self.logger.warning("没有可用的预测结果进行绘图")
            return

        # 创建子图
        fig, axes = plt.subplots(3, 3, figsize=figsize)
        fig.suptitle('模型评估结果', fontsize=16)

        # 获取测试集数据
        if 'test' in self.prediction_cache:
            y_true = self.prediction_cache['test']['y_true']
            y_pred = self.prediction_cache['test']['y_pred']
            residuals = y_true - y_pred

            # 1. 预测vs真实值散点图
            axes[0, 0].scatter(y_true, y_pred, alpha=0.6)
            min_val = min(y_true.min(), y_pred.min())
            max_val = max(y_true.max(), y_pred.max())
            axes[0, 0].plot([min_val, max_val], [min_val, max_val], 'r--', lw=2)
            axes[0, 0].set_xlabel('真实值')
            axes[0, 0].set_ylabel('预测值')
            axes[0, 0].set_title('预测 vs 真实值')
            axes[0, 0].grid(True)

            # 添加R²信息
            r2 = r2_score(y_true, y_pred)
            axes[0, 0].text(0.05, 0.95, f'R² = {r2:.4f}', transform=axes[0, 0].transAxes,
                           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

            # 2. 残差图
            axes[0, 1].scatter(y_pred, residuals, alpha=0.6)
            axes[0, 1].axhline(y=0, color='r', linestyle='--')
            axes[0, 1].set_xlabel('预测值')
            axes[0, 1].set_ylabel('残差')
            axes[0, 1].set_title('残差图')
            axes[0, 1].grid(True)

            # 3. 残差直方图
            axes[0, 2].hist(residuals, bins=30, alpha=0.7, density=True)
            axes[0, 2].set_xlabel('残差')
            axes[0, 2].set_ylabel('密度')
            axes[0, 2].set_title('残差分布')
            axes[0, 2].grid(True)

            # 添加正态分布曲线
            mu, sigma = np.mean(residuals), np.std(residuals)
            x = np.linspace(residuals.min(), residuals.max(), 100)
            axes[0, 2].plot(x, stats.norm.pdf(x, mu, sigma), 'r-', lw=2, label='正态分布')
            axes[0, 2].legend()

            # 4. Q - Q图
            stats.probplot(residuals, dist="norm", plot=axes[1, 0])
            axes[1, 0].set_title('Q - Q图 (正态性检验)')
            axes[1, 0].grid(True)

            # 5. 误差分布箱线图
            axes[1, 1].boxplot([np.abs(residuals)], labels=['绝对误差'])
            axes[1, 1].set_ylabel('绝对误差')
            axes[1, 1].set_title('误差分布')
            axes[1, 1].grid(True)

            # 6. 累积误差分布
            sorted_abs_errors = np.sort(np.abs(residuals))
            cumulative_prob = np.arange(1, len(sorted_abs_errors) + 1) / len(sorted_abs_errors)
            axes[1, 2].plot(sorted_abs_errors, cumulative_prob)
            axes[1, 2].set_xlabel('绝对误差')
            axes[1, 2].set_ylabel('累积概率')
            axes[1, 2].set_title('累积误差分布')
            axes[1, 2].grid(True)

            # 7. 预测误差随时间变化（如果有索引）
            axes[2, 0].plot(np.abs(residuals))
            axes[2, 0].set_xlabel('样本索引')
            axes[2, 0].set_ylabel('绝对误差')
            axes[2, 0].set_title('误差随样本变化')
            axes[2, 0].grid(True)

            # 8. 性能指标雷达图（如果有多个数据集）
            if len(self.prediction_cache) > 1:
                datasets = list(self.prediction_cache.keys())
                metrics = ['R²', 'RMSE', 'MAE']

                # 计算各数据集的标准化指标
                metric_values = []
                for dataset in datasets:
                    y_t = self.prediction_cache[dataset]['y_true']
                    y_p = self.prediction_cache[dataset]['y_pred']

                    r2 = r2_score(y_t, y_p)
                    rmse = np.sqrt(mean_squared_error(y_t, y_p))
                    mae = mean_absolute_error(y_t, y_p)

                    # 标准化指标（R²保持原值，RMSE和MAE取倒数并标准化）
                    metric_values.append([r2, 1 / (1 + rmse), 1 / (1 + mae)])

                # 绘制雷达图
                angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
                angles += angles[:1]  # 闭合

                axes[2, 1].set_theta_offset(np.pi / 2)
                axes[2, 1].set_theta_direction(-1)
                axes[2, 1].set_thetagrids(np.degrees(angles[:-1]), metrics)

                for i, (dataset, values) in enumerate(zip(datasets, metric_values)):
                    values += values[:1]  # 闭合
                    axes[2, 1].plot(angles, values, 'o-', linewidth=2, label=dataset)
                    axes[2, 1].fill(angles, values, alpha=0.25)

                axes[2, 1].set_ylim(0, 1)
                axes[2, 1].set_title('多数据集性能对比')
                axes[2, 1].legend()
            else:
                axes[2, 1].text(0.5, 0.5, '需要多个数据集\n才能显示对比',
                               ha='center', va='center', transform=axes[2, 1].transAxes)
                axes[2, 1].set_title('多数据集性能对比')

            # 9. 特征重要性（如果可用）
            if 'feature_importance' in self.evaluation_results:
                importance = self.evaluation_results['feature_importance']['feature_importance']
                feature_names = [f'特征_{i}' for i in range(len(importance))]

                # 只显示前10个最重要的特征
                top_indices = np.argsort(importance)[-10:]
                top_importance = [importance[i] for i in top_indices]
                top_names = [feature_names[i] for i in top_indices]

                axes[2, 2].barh(range(len(top_importance)), top_importance)
                axes[2, 2].set_yticks(range(len(top_importance)))
                axes[2, 2].set_yticklabels(top_names)
                axes[2, 2].set_xlabel('重要性分数')
                axes[2, 2].set_title('特征重要性 (Top 10)')
                axes[2, 2].grid(True)
            else:
                axes[2, 2].text(0.5, 0.5, '特征重要性\n分析未完成',
                               ha='center', va='center', transform=axes[2, 2].transAxes)
                axes[2, 2].set_title('特征重要性')

        plt.tight_layout()

        if save_dir:
            os.makedirs(save_dir, exist_ok=True)
            save_path = os.path.join(save_dir, 'evaluation_results.png')
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"评估结果图已保存到: {save_path}")

        plt.show()

    def generate_evaluation_report(self) -> str:
        """
        生成评估报告

        Returns:
            str: 详细的评估报告
        """
        if not self.evaluation_results:
            return "评估结果不可用，请先运行评估"

        report = []
        report.append("=" * 80)
        report.append("模型性能评估报告")
        report.append("=" * 80)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # 基础性能指标
        report.append("基础性能指标:")
        report.append("-" * 40)

        for dataset in ['train', 'val', 'test']:
            if f'{dataset}_r2' in self.evaluation_results:
                report.append(f"{dataset.upper()}集:")
                report.append(f"  R²: {self.evaluation_results.get(f'{dataset}_r2', 0):.6f}")
                report.append(f"  RMSE: {self.evaluation_results.get(f'{dataset}_rmse', 0):.6f}")
                report.append(f"  MAE: {self.evaluation_results.get(f'{dataset}_mae', 0):.6f}")
                report.append(f"  MAPE: {self.evaluation_results.get(f'{dataset}_mape', 0):.2f}%")
                report.append(f"  最大误差: {self.evaluation_results.get(f'{dataset}_max_error', 0):.6f}")
                report.append("")

        # 统计分析
        if 'statistical_analysis' in self.evaluation_results:
            stats_analysis = self.evaluation_results['statistical_analysis']
            report.append("统计分析:")
            report.append("-" * 40)
            report.append(f"残差均值: {stats_analysis.get('residual_mean', 0):.6f}")
            report.append(f"残差标准差: {stats_analysis.get('residual_std', 0):.6f}")
            report.append(f"残差偏度: {stats_analysis.get('residual_skewness', 0):.6f}")
            report.append(f"残差峰度: {stats_analysis.get('residual_kurtosis', 0):.6f}")
            report.append(f"残差正态性 (p值): {stats_analysis.get('residual_normality_p', 0):.6f}")
            report.append(f"残差是否正态分布: {'是' if stats_analysis.get('residual_is_normal', False) else '否'}")
            report.append(f"预测 - 真实值相关性: {stats_analysis.get('correlation', 0):.6f}")
            report.append("")

        # 残差分析
        if 'residual_analysis' in self.evaluation_results:
            residual_analysis = self.evaluation_results['residual_analysis']
            report.append("残差分析:")
            report.append("-" * 40)

            basic_stats = residual_analysis.get('basic_stats', {})
            report.append(f"残差范围: [{basic_stats.get('min', 0):.6f}, {basic_stats.get('max', 0):.6f}]")
            report.append(f"残差标准差: {basic_stats.get('std', 0):.6f}")

            pred_corr = residual_analysis.get('residual_pred_correlation', {})
            report.append(f"残差 - 预测值相关性: {pred_corr.get('correlation', 0):.6f}")
            report.append(f"同方差性: {'是' if pred_corr.get('is_homoscedastic', False) else '否'}")
            report.append("")

        # 预测区间分析
        if 'prediction_intervals' in self.evaluation_results:
            interval_analysis = self.evaluation_results['prediction_intervals']
            report.append("预测区间分析:")
            report.append("-" * 40)
            confidence = interval_analysis.get('confidence_level', 0.95)
            report.append(f"置信水平: {confidence * 100:.1f}%")
            report.append(f"实际覆盖率: {interval_analysis.get('coverage_rate', 0) * 100:.2f}%")
            report.append(f"期望覆盖率: {interval_analysis.get('expected_coverage', 0) * 100:.2f}%")
            report.append(f"覆盖率差异: {interval_analysis.get('coverage_difference', 0) * 100:.2f}%")
            report.append(f"平均区间宽度: {interval_analysis.get('interval_width_mean', 0):.6f}")
            report.append("")

        # 误差分析
        if 'error_analysis' in self.evaluation_results:
            error_analysis = self.evaluation_results['error_analysis']
            report.append("误差分析:")
            report.append("-" * 40)

            percentiles = error_analysis.get('error_percentiles', {})
            report.append("误差分位数:")
            for p in ['p10', 'p25', 'p50', 'p75', 'p90', 'p95', 'p99']:
                if p in percentiles:
                    report.append(f"  {p}: {percentiles[p]:.6f}")

            large_error = error_analysis.get('large_error_analysis', {})
            if large_error:
                report.append(f"大误差样本分析 (>{large_error.get('threshold', 0):.6f}):")
                report.append(f"  数量: {large_error.get('count', 0)}")
                report.append(f"  比例: {large_error.get('percentage', 0):.2f}%")
                report.append(f"  平均误差: {large_error.get('mean_error', 0):.6f}")
                report.append(f"  最大误差: {large_error.get('max_error', 0):.6f}")
            report.append("")

        # 特征重要性
        if 'feature_importance' in self.evaluation_results:
            importance_analysis = self.evaluation_results['feature_importance']
            report.append("特征重要性分析:")
            report.append("-" * 40)

            importance_scores = importance_analysis.get('feature_importance', [])
            top_features = importance_analysis.get('top_features', [])

            report.append("最重要的10个特征:")
            for i, feature_idx in enumerate(top_features[:10]):
                if feature_idx < len(importance_scores):
                    score = importance_scores[feature_idx]
                    report.append(f"  特征_{feature_idx}: {score:.6f}")
            report.append("")

        # 模型诊断建议
        report.append("模型诊断建议:")
        report.append("-" * 40)

        # 基于评估结果给出建议
        suggestions = self._generate_suggestions()
        for suggestion in suggestions:
            report.append(f"• {suggestion}")

        report.append("")
        report.append("=" * 80)

        return "\n".join(report)

    def _generate_suggestions(self) -> List[str]:
        """
        基于评估结果生成改进建议

        Returns:
            List[str]: 建议列表
        """
        suggestions = []

        # 检查R²分数
        test_r2 = self.evaluation_results.get('test_r2', 0)
        if test_r2 < 0.7:
            suggestions.append("模型R²较低，考虑增加模型复杂度或改进特征工程")
        elif test_r2 > 0.95:
            suggestions.append("模型R²很高，检查是否存在过拟合")

        # 检查训练集和测试集性能差异
        train_r2 = self.evaluation_results.get('train_r2', 0)
        if train_r2 - test_r2 > 0.1:
            suggestions.append("训练集和测试集性能差异较大，可能存在过拟合，考虑增加正则化")

        # 检查残差正态性
        if 'statistical_analysis' in self.evaluation_results:
            stats_analysis = self.evaluation_results['statistical_analysis']
            if not stats_analysis.get('residual_is_normal', True):
                suggestions.append("残差不符合正态分布，考虑数据变换或使用非线性模型")

        # 检查同方差性
        if 'residual_analysis' in self.evaluation_results:
            residual_analysis = self.evaluation_results['residual_analysis']
            pred_corr = residual_analysis.get('residual_pred_correlation', {})
            if not pred_corr.get('is_homoscedastic', True):
                suggestions.append("残差存在异方差性，考虑使用加权回归或变换目标变量")

        # 检查预测区间覆盖率
        if 'prediction_intervals' in self.evaluation_results:
            interval_analysis = self.evaluation_results['prediction_intervals']
            coverage_diff = abs(interval_analysis.get('coverage_difference', 0))
            if coverage_diff > 0.05:
                suggestions.append("预测区间覆盖率偏差较大，模型不确定性估计可能不准确")

        if not suggestions:
            suggestions.append("模型性能良好，各项指标均在合理范围内")

        return suggestions

    def save_evaluation_results(self, save_path: str):
        """
        保存评估结果

        Args:
            save_path: 保存路径
        """
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # 保存评估结果
        with open(save_path, 'w', encoding='utf - 8') as f:
            json.dump(self.evaluation_results, f, indent=2, ensure_ascii=False)

        # 保存评估报告
        report_path = save_path.replace('.json', '_report.txt')
        report = self.generate_evaluation_report()
        with open(report_path, 'w', encoding='utf - 8') as f:
            f.write(report)

        self.logger.info(f"评估结果已保存到: {save_path}")
        self.logger.info(f"评估报告已保存到: {report_path}")

    @staticmethod
    def _durbin_watson(residuals: np.ndarray) -> float:
        """
        计算Durbin - Watson统计量

        Args:
            residuals: 残差数组

        Returns:
            float: DW统计量
        """
        diff = np.diff(residuals)
        return np.sum(diff**2) / np.sum(residuals**2)


def create_model_evaluator(
    model: keras.Model,
    input_scaler: Optional[Any] = None,
    output_scaler: Optional[Any] = None,
    target_names: Optional[List[str]] = None
) -> ModelEvaluator:
    """
    创建模型评估器的便捷函数

    Args:
        model: Keras模型
        input_scaler: 输入标准化器
        output_scaler: 输出标准化器
        target_names: 目标变量名称

    Returns:
        ModelEvaluator: 模型评估器实例
    """
    return ModelEvaluator(
        model=model,
        input_scaler=input_scaler,
        output_scaler=output_scaler,
        target_names=target_names
    )


if __name__ == "__main__":
    # 示例用法
    from sklearn.datasets import make_regression
    from sklearn.model_selection import train_test_split
    from sklearn.preprocessing import StandardScaler

    # 生成示例数据
    X, y = make_regression(n_samples=1000, n_features=10, noise=0.1, random_state=42)
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    # 数据标准化
    scaler_X = StandardScaler()
    scaler_y = StandardScaler()

    X_train_scaled = scaler_X.fit_transform(X_train)
    X_test_scaled = scaler_X.transform(X_test)
    y_train_scaled = scaler_y.fit_transform(y_train.reshape(-1, 1)).flatten()
    y_test_scaled = scaler_y.transform(y_test.reshape(-1, 1)).flatten()

    # 创建简单模型
    model = keras.Sequential([
        keras.layers.Dense(64, activation='relu', input_shape=(10,)),
        keras.layers.Dense(32, activation='relu'),
        keras.layers.Dense(1)
    ])

    model.compile(optimizer='adam', loss='mse', metrics=['mae'])

    # 训练模型
    model.fit(X_train_scaled, y_train_scaled, epochs=50, verbose=0)

    # 创建评估器
    evaluator = create_model_evaluator(
        model=model,
        input_scaler=scaler_X,
        output_scaler=scaler_y
    )

    # 全面评估
    results = evaluator.evaluate_comprehensive(
        X_test_scaled, y_test_scaled,
        X_train_scaled, y_train_scaled
    )

    # 生成报告
    report = evaluator.generate_evaluation_report()
    print(report)

    # 绘制评估结果
    evaluator.plot_evaluation_results()
