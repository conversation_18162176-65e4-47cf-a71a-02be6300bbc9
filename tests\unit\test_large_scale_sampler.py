"""
大规模抽样器单元测试
"""

import unittest
import numpy as np
import tempfile
import shutil
import time
from unittest.mock import patch, MagicMock
from pathlib import Path

from src.calibration.parameter_sampler import ParameterDefinition, SamplingConfig, LatinHypercubeSampler
from src.calibration.large_scale_sampler import (
    MemoryUsage, ProgressInfo, MemoryMonitor, ProgressTracker,
    SampleStorage, LargeScaleSampler, create_large_scale_sampler
)


class TestMemoryUsage(unittest.TestCase):
    """内存使用情况测试类"""
    
    def test_memory_usage_creation(self):
        """测试内存使用情况创建"""
        usage = MemoryUsage(
            total_mb=8192.0,
            available_mb=4096.0,
            used_mb=4096.0,
            percent=50.0,
            process_mb=512.0
        )
        
        self.assertEqual(usage.total_mb, 8192.0)
        self.assertEqual(usage.available_mb, 4096.0)
        self.assertEqual(usage.used_mb, 4096.0)
        self.assertEqual(usage.percent, 50.0)
        self.assertEqual(usage.process_mb, 512.0)


class TestProgressInfo(unittest.TestCase):
    """进度信息测试类"""
    
    def test_progress_info_creation(self):
        """测试进度信息创建"""
        memory_usage = MemoryUsage(8192.0, 4096.0, 4096.0, 50.0, 512.0)
        
        progress = ProgressInfo(
            current_samples=500,
            total_samples=1000,
            current_batch=5,
            total_batches=10,
            elapsed_time=30.0,
            estimated_remaining_time=30.0,
            samples_per_second=16.67,
            memory_usage=memory_usage
        )
        
        self.assertEqual(progress.current_samples, 500)
        self.assertEqual(progress.total_samples, 1000)
        self.assertEqual(progress.current_batch, 5)
        self.assertEqual(progress.total_batches, 10)
        self.assertEqual(progress.elapsed_time, 30.0)
        self.assertEqual(progress.estimated_remaining_time, 30.0)
        self.assertAlmostEqual(progress.samples_per_second, 16.67, places=2)


class TestMemoryMonitor(unittest.TestCase):
    """内存监控器测试类"""
    
    def setUp(self):
        """设置测试数据"""
        self.monitor = MemoryMonitor(warning_threshold=80.0, critical_threshold=90.0)
    
    def test_memory_monitor_initialization(self):
        """测试内存监控器初始化"""
        self.assertEqual(self.monitor.warning_threshold, 80.0)
        self.assertEqual(self.monitor.critical_threshold, 90.0)
        self.assertIsNotNone(self.monitor.process)
    
    @patch('psutil.virtual_memory')
    @patch('psutil.Process')
    def test_get_memory_usage(self, mock_process_class, mock_virtual_memory):
        """测试获取内存使用情况"""
        # 模拟系统内存
        mock_memory = MagicMock()
        mock_memory.total = 8 * 1024 * 1024 * 1024  # 8GB
        mock_memory.available = 4 * 1024 * 1024 * 1024  # 4GB
        mock_memory.used = 4 * 1024 * 1024 * 1024  # 4GB
        mock_memory.percent = 50.0
        mock_virtual_memory.return_value = mock_memory
        
        # 模拟进程内存
        mock_process = MagicMock()
        mock_process_info = MagicMock()
        mock_process_info.rss = 512 * 1024 * 1024  # 512MB
        mock_process.memory_info.return_value = mock_process_info
        mock_process_class.return_value = mock_process
        
        # 重新创建监控器以使用模拟的进程
        monitor = MemoryMonitor()
        usage = monitor.get_memory_usage()
        
        self.assertAlmostEqual(usage.total_mb, 8192.0, places=1)
        self.assertAlmostEqual(usage.available_mb, 4096.0, places=1)
        self.assertAlmostEqual(usage.used_mb, 4096.0, places=1)
        self.assertEqual(usage.percent, 50.0)
        self.assertAlmostEqual(usage.process_mb, 512.0, places=1)
    
    def test_check_memory_status_normal(self):
        """测试正常内存状态检查"""
        with patch.object(self.monitor, 'get_memory_usage') as mock_get_usage:
            mock_usage = MemoryUsage(8192.0, 6000.0, 2192.0, 25.0, 512.0)
            mock_get_usage.return_value = mock_usage
            
            status = self.monitor.check_memory_status()
            self.assertEqual(status, 'normal')
    
    def test_check_memory_status_warning(self):
        """测试警告内存状态检查"""
        with patch.object(self.monitor, 'get_memory_usage') as mock_get_usage:
            mock_usage = MemoryUsage(8192.0, 1500.0, 6692.0, 85.0, 512.0)
            mock_get_usage.return_value = mock_usage
            
            status = self.monitor.check_memory_status()
            self.assertEqual(status, 'warning')
    
    def test_check_memory_status_critical(self):
        """测试临界内存状态检查"""
        with patch.object(self.monitor, 'get_memory_usage') as mock_get_usage:
            mock_usage = MemoryUsage(8192.0, 500.0, 7692.0, 95.0, 512.0)
            mock_get_usage.return_value = mock_usage
            
            status = self.monitor.check_memory_status()
            self.assertEqual(status, 'critical')


class TestProgressTracker(unittest.TestCase):
    """进度跟踪器测试类"""
    
    def setUp(self):
        """设置测试数据"""
        self.tracker = ProgressTracker()
    
    def test_progress_tracker_initialization(self):
        """测试进度跟踪器初始化"""
        self.assertIsNone(self.tracker.start_time)
        self.assertIsNone(self.tracker.last_update_time)
        self.assertEqual(len(self.tracker.samples_history), 0)
        self.assertEqual(len(self.tracker.time_history), 0)
    
    def test_start_tracking(self):
        """测试开始跟踪"""
        self.tracker.start()
        
        self.assertIsNotNone(self.tracker.start_time)
        self.assertIsNotNone(self.tracker.last_update_time)
        self.assertEqual(len(self.tracker.samples_history), 1)
        self.assertEqual(len(self.tracker.time_history), 1)
        self.assertEqual(self.tracker.samples_history[0], 0)
        self.assertEqual(self.tracker.time_history[0], 0)
    
    @patch('src.calibration.large_scale_sampler.MemoryMonitor')
    def test_update_progress(self, mock_memory_monitor_class):
        """测试更新进度"""
        # 模拟内存监控器
        mock_monitor = MagicMock()
        mock_usage = MemoryUsage(8192.0, 4096.0, 4096.0, 50.0, 512.0)
        mock_monitor.get_memory_usage.return_value = mock_usage
        mock_memory_monitor_class.return_value = mock_monitor
        
        self.tracker.start()
        time.sleep(0.1)  # 等待一小段时间
        
        progress_info = self.tracker.update(
            current_samples=500,
            total_samples=1000,
            current_batch=5,
            total_batches=10
        )
        
        self.assertEqual(progress_info.current_samples, 500)
        self.assertEqual(progress_info.total_samples, 1000)
        self.assertEqual(progress_info.current_batch, 5)
        self.assertEqual(progress_info.total_batches, 10)
        self.assertGreater(progress_info.elapsed_time, 0)
        self.assertGreaterEqual(progress_info.samples_per_second, 0)


class TestSampleStorage(unittest.TestCase):
    """样本存储测试类"""
    
    def setUp(self):
        """设置测试数据"""
        self.temp_dir = tempfile.mkdtemp()
        self.storage = SampleStorage(self.temp_dir, compression=True)
    
    def tearDown(self):
        """清理测试数据"""
        shutil.rmtree(self.temp_dir)
    
    def test_storage_initialization(self):
        """测试存储初始化"""
        self.assertEqual(str(self.storage.storage_path), self.temp_dir)
        self.assertTrue(self.storage.compression)
        self.assertTrue(self.storage.index_file.exists())
    
    def test_store_and_load_batch(self):
        """测试存储和加载批次"""
        # 创建测试样本
        test_samples = np.random.random((100, 3))
        metadata = {"batch_index": 0, "timestamp": time.time()}
        
        # 存储批次
        filepath = self.storage.store_batch(test_samples, "test_batch", metadata)
        
        self.assertTrue(Path(filepath).exists())
        
        # 加载批次
        loaded_samples = self.storage.load_batch("test_batch")
        
        self.assertIsNotNone(loaded_samples)
        np.testing.assert_array_equal(loaded_samples, test_samples)
    
    def test_store_multiple_batches(self):
        """测试存储多个批次"""
        batches = []
        for i in range(3):
            samples = np.random.random((50, 2))
            batches.append(samples)
            self.storage.store_batch(samples, f"batch_{i}")
        
        # 检查索引
        self.assertEqual(len(self.storage.index["samples"]), 3)
        self.assertEqual(self.storage.index["total_count"], 150)
        
        # 加载所有样本
        all_samples = self.storage.load_all_samples()
        self.assertIsNotNone(all_samples)
        self.assertEqual(all_samples.shape, (150, 2))
    
    def test_storage_without_compression(self):
        """测试无压缩存储"""
        storage_no_comp = SampleStorage(self.temp_dir + "_no_comp", compression=False)
        
        test_samples = np.random.random((50, 2))
        filepath = storage_no_comp.store_batch(test_samples, "no_comp_batch")
        
        self.assertTrue(Path(filepath).exists())
        self.assertTrue(filepath.endswith('.pkl'))
        self.assertFalse(filepath.endswith('.gz'))
        
        # 清理
        storage_no_comp.cleanup()
    
    def test_get_storage_info(self):
        """测试获取存储信息"""
        # 存储一些样本
        for i in range(2):
            samples = np.random.random((100, 3))
            self.storage.store_batch(samples, f"info_batch_{i}")
        
        info = self.storage.get_storage_info()
        
        self.assertEqual(info["total_samples"], 200)
        self.assertEqual(info["batch_count"], 2)
        self.assertGreater(info["total_size_mb"], 0)
        self.assertTrue(info["compression_enabled"])
    
    def test_cleanup(self):
        """测试清理存储"""
        # 存储一些样本
        samples = np.random.random((50, 2))
        self.storage.store_batch(samples, "cleanup_test")
        
        # 确认文件存在
        self.assertGreater(len(list(Path(self.temp_dir).glob("*.pkl.gz"))), 0)
        
        # 清理
        self.storage.cleanup()
        
        # 确认文件被删除
        self.assertEqual(len(list(Path(self.temp_dir).glob("*.pkl.gz"))), 0)
        self.assertEqual(self.storage.index["total_count"], 0)


class TestLargeScaleSampler(unittest.TestCase):
    """大规模抽样器测试类"""
    
    def setUp(self):
        """设置测试数据"""
        self.params = [
            ParameterDefinition("param1", 0.0, 1.0),
            ParameterDefinition("param2", -1.0, 1.0),
            ParameterDefinition("param3", 0.1, 10.0)
        ]
        
        self.config = SamplingConfig(
            parameters=self.params,
            n_samples=100,
            random_seed=42
        )
        
        self.base_sampler = LatinHypercubeSampler(self.config)
        self.temp_dir = tempfile.mkdtemp()
        
        self.large_sampler = LargeScaleSampler(
            base_sampler=self.base_sampler,
            batch_size=50,
            storage_path=self.temp_dir,
            memory_limit_mb=1000
        )
    
    def tearDown(self):
        """清理测试数据"""
        self.large_sampler.cleanup()
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_large_sampler_initialization(self):
        """测试大规模抽样器初始化"""
        self.assertEqual(self.large_sampler.batch_size, 50)
        self.assertEqual(self.large_sampler.memory_limit_mb, 1000)
        self.assertIsNotNone(self.large_sampler.memory_monitor)
        self.assertIsNotNone(self.large_sampler.progress_tracker)
        self.assertIsNotNone(self.large_sampler.storage)
    
    def test_generate_large_samples_basic(self):
        """测试基本大规模抽样"""
        result = self.large_sampler.generate_large_samples(200)
        
        self.assertEqual(result.samples.shape, (200, 3))
        self.assertEqual(result.config.n_samples, 200)
        self.assertEqual(len(result.parameter_names), 3)
        self.assertGreater(result.generation_time, 0)
        self.assertIsInstance(result.hash_signature, str)
    
    def test_generate_large_samples_with_callbacks(self):
        """测试带回调的大规模抽样"""
        progress_calls = []
        memory_calls = []
        
        def progress_callback(progress_info):
            progress_calls.append(progress_info)
        
        def memory_callback(status, memory_usage):
            memory_calls.append((status, memory_usage))
        
        result = self.large_sampler.generate_large_samples(
            150,
            progress_callback=progress_callback,
            memory_callback=memory_callback
        )
        
        self.assertEqual(result.samples.shape, (150, 3))
        self.assertGreater(len(progress_calls), 0)
        
        # 检查进度回调参数
        final_progress = progress_calls[-1]
        self.assertEqual(final_progress.total_samples, 150)
        self.assertGreaterEqual(final_progress.current_samples, 150)
    
    def test_generate_large_samples_with_intermediate_save(self):
        """测试保存中间结果的大规模抽样"""
        result = self.large_sampler.generate_large_samples(
            100,
            save_intermediate=True
        )
        
        self.assertEqual(result.samples.shape, (100, 3))
        
        # 检查存储信息
        storage_info = self.large_sampler.storage.get_storage_info()
        self.assertGreater(storage_info["batch_count"], 0)
        self.assertEqual(storage_info["total_samples"], 100)
    
    def test_generate_batch(self):
        """测试生成单个批次"""
        batch_samples = self.large_sampler._generate_batch(30, 0)
        
        self.assertEqual(batch_samples.shape, (30, 3))
        
        # 检查样本范围
        self.assertTrue(np.all(batch_samples[:, 0] >= 0.0))
        self.assertTrue(np.all(batch_samples[:, 0] <= 1.0))
        self.assertTrue(np.all(batch_samples[:, 1] >= -1.0))
        self.assertTrue(np.all(batch_samples[:, 1] <= 1.0))
    
    def test_different_batch_seeds(self):
        """测试不同批次使用不同种子"""
        batch1 = self.large_sampler._generate_batch(50, 0)
        batch2 = self.large_sampler._generate_batch(50, 1)
        
        # 不同批次应产生不同样本
        self.assertFalse(np.array_equal(batch1, batch2))
    
    def test_get_performance_report(self):
        """测试获取性能报告"""
        # 先生成一些样本
        self.large_sampler.generate_large_samples(100)
        
        report = self.large_sampler.get_performance_report()
        
        # 检查报告内容
        self.assertIn('total_generation_time', report)
        self.assertIn('total_samples_generated', report)
        self.assertIn('samples_per_second', report)
        self.assertIn('memory_peak_mb', report)
        self.assertIn('storage_info', report)
        self.assertIn('current_memory_usage', report)
        
        self.assertEqual(report['total_samples_generated'], 100)
        self.assertGreater(report['total_generation_time'], 0)
    
    @patch('src.calibration.large_scale_sampler.MemoryMonitor')
    def test_memory_management(self, mock_memory_monitor_class):
        """测试内存管理"""
        # 模拟内存监控器
        mock_monitor = MagicMock()
        
        # 模拟内存状态变化
        memory_states = ['normal', 'warning', 'critical', 'normal']
        mock_monitor.check_memory_status.side_effect = memory_states
        
        mock_usage = MemoryUsage(8192.0, 1000.0, 7192.0, 85.0, 512.0)
        mock_monitor.get_memory_usage.return_value = mock_usage
        
        mock_memory_monitor_class.return_value = mock_monitor
        
        # 创建新的抽样器使用模拟的监控器
        sampler = LargeScaleSampler(
            base_sampler=self.base_sampler,
            batch_size=25,
            memory_limit_mb=1000
        )
        
        memory_callbacks = []
        
        def memory_callback(status, memory_usage):
            memory_callbacks.append(status)
        
        result = sampler.generate_large_samples(
            100,
            memory_callback=memory_callback
        )
        
        self.assertEqual(result.samples.shape, (100, 3))
        # 应该有内存回调被调用
        self.assertGreater(len([cb for cb in memory_callbacks if cb != 'normal']), 0)


class TestCreateLargeScaleSampler(unittest.TestCase):
    """创建大规模抽样器函数测试类"""
    
    def test_create_large_scale_sampler(self):
        """测试创建大规模抽样器便捷函数"""
        params = [
            ParameterDefinition("param1", 0.0, 1.0),
            ParameterDefinition("param2", -1.0, 1.0)
        ]
        
        temp_dir = tempfile.mkdtemp()
        
        try:
            sampler = create_large_scale_sampler(
                parameters=params,
                n_samples=1000,
                random_seed=123,
                batch_size=100,
                storage_path=temp_dir
            )
            
            self.assertIsInstance(sampler, LargeScaleSampler)
            self.assertEqual(sampler.batch_size, 100)
            self.assertEqual(sampler.base_sampler.config.random_seed, 123)
            self.assertIsNotNone(sampler.storage)
            
            # 测试生成样本
            result = sampler.generate_large_samples(200)
            self.assertEqual(result.samples.shape, (200, 2))
            
        finally:
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    def test_create_large_scale_sampler_without_storage(self):
        """测试创建无存储的大规模抽样器"""
        params = [ParameterDefinition("param1", 0.0, 1.0)]
        
        sampler = create_large_scale_sampler(
            parameters=params,
            n_samples=500,
            batch_size=50
        )
        
        self.assertIsInstance(sampler, LargeScaleSampler)
        self.assertIsNone(sampler.storage)
        
        # 测试生成样本
        result = sampler.generate_large_samples(100)
        self.assertEqual(result.samples.shape, (100, 1))


class TestEdgeCases(unittest.TestCase):
    """边界情况测试类"""
    
    def setUp(self):
        """设置测试数据"""
        self.params = [ParameterDefinition("param1", 0.0, 1.0)]
        self.config = SamplingConfig(self.params, 50, 42)
        self.base_sampler = LatinHypercubeSampler(self.config)
    
    def test_very_large_sample_size(self):
        """测试非常大的样本量"""
        sampler = LargeScaleSampler(
            base_sampler=self.base_sampler,
            batch_size=100
        )
        
        # 测试10,000个样本
        result = sampler.generate_large_samples(10000)
        
        self.assertEqual(result.samples.shape, (10000, 1))
        self.assertGreater(result.generation_time, 0)
    
    def test_small_batch_size(self):
        """测试小批次大小"""
        sampler = LargeScaleSampler(
            base_sampler=self.base_sampler,
            batch_size=10  # 很小的批次
        )
        
        result = sampler.generate_large_samples(50)
        
        self.assertEqual(result.samples.shape, (50, 1))
    
    def test_samples_not_divisible_by_batch_size(self):
        """测试样本数不能被批次大小整除"""
        sampler = LargeScaleSampler(
            base_sampler=self.base_sampler,
            batch_size=30
        )
        
        result = sampler.generate_large_samples(100)  # 100 = 3*30 + 10
        
        self.assertEqual(result.samples.shape, (100, 1))


if __name__ == '__main__':
    unittest.main()