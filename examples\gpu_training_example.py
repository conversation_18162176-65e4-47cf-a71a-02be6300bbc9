"""
GPU加速深度神经网络训练示例
演示如何使用GPU加速进行模型校准
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.datasets import make_regression
from sklearn.preprocessing import StandardScaler
import tensorflow as tf

# 导入自定义模块
from src.calibration.training_manager import TrainingManager, create_training_pipeline
from src.calibration.neural_network_gpu import GPUNetworkArchitectureFactory, NetworkConfig


def generate_calibration_data(n_samples: int = 2000, n_features: int = 15, noise: float = 0.1):
    """
    生成校准数据
    
    Args:
        n_samples: 样本数量
        n_features: 特征数量
        noise: 噪声水平
        
    Returns:
        tuple: (X, y) 特征和目标
    """
    print(f"生成校准数据: {n_samples} 样本, {n_features} 特征")
    
    # 生成基础回归数据
    X, y = make_regression(
        n_samples=n_samples,
        n_features=n_features,
        n_informative=int(n_features * 0.8),
        noise=noise * 100,
        random_state=42
    )
    
    # 添加非线性关系
    X_nonlinear = np.column_stack([
        X,
        X[:, 0] * X[:, 1],  # 交互项
        np.sin(X[:, 2]),    # 非线性变换
        np.exp(X[:, 3] / 10),  # 指数变换
        X[:, 4] ** 2        # 平方项
    ])
    
    # 重新计算目标变量
    y = (
        np.sum(X_nonlinear[:, :n_features], axis=1) * 0.5 +
        X_nonlinear[:, n_features] * 0.3 +
        X_nonlinear[:, n_features + 1] * 0.2 +
        X_nonlinear[:, n_features + 2] * 0.1 +
        X_nonlinear[:, n_features + 3] * 0.1 +
        noise * np.random.randn(n_samples)
    )
    
    return X_nonlinear, y


def compare_architectures():
    """比较不同网络架构的性能"""
    print("=" * 60)
    print("比较不同网络架构的性能")
    print("=" * 60)
    
    # 生成数据
    X, y = generate_calibration_data(n_samples=3000, n_features=20)
    
    architectures = ['feedforward', 'residual', 'ensemble']
    results = {}
    
    for arch in architectures:
        print(f"\n训练 {arch} 架构...")
        
        # 创建配置
        config = GPUNetworkArchitectureFactory.create_gpu_config(
            input_dim=X.shape[1],
            output_dim=1,
            complexity='medium',
            enable_gpu=True
        )
        config.architecture = arch
        
        # 创建训练管理器
        trainer = TrainingManager(config)
        
        # 准备数据
        trainer.prepare_data(X, y, test_size=0.2, val_size=0.2)
        
        # 创建和训练模型
        trainer.create_model()
        history = trainer.train_model(epochs=50, verbose=0)
        
        # 评估模型
        eval_results = trainer.evaluate_model()
        
        results[arch] = {
            'test_r2': eval_results['test_r2'],
            'test_rmse': eval_results['test_rmse'],
            'test_mae': eval_results['test_mae'],
            'training_time': len(history.history['loss'])
        }
        
        print(f"{arch} 架构结果:")
        print(f"  测试集 R²: {eval_results['test_r2']:.4f}")
        print(f"  测试集 RMSE: {eval_results['test_rmse']:.4f}")
        print(f"  测试集 MAE: {eval_results['test_mae']:.4f}")
    
    # 比较结果
    print("\n" + "=" * 60)
    print("架构比较结果:")
    print("=" * 60)
    
    best_arch = max(results.keys(), key=lambda k: results[k]['test_r2'])
    
    for arch, metrics in results.items():
        status = " (最佳)" if arch == best_arch else ""
        print(f"{arch}{status}:")
        print(f"  R²: {metrics['test_r2']:.4f}")
        print(f"  RMSE: {metrics['test_rmse']:.4f}")
        print(f"  MAE: {metrics['test_mae']:.4f}")
        print()
    
    return results


def gpu_performance_test():
    """GPU性能测试"""
    print("=" * 60)
    print("GPU性能测试")
    print("=" * 60)
    
    # 检查GPU可用性
    print("GPU信息:")
    print(f"  TensorFlow版本: {tf.__version__}")
    print(f"  可用GPU数量: {len(tf.config.list_physical_devices('GPU'))}")
    
    if tf.config.list_physical_devices('GPU'):
        for i, gpu in enumerate(tf.config.list_physical_devices('GPU')):
            print(f"  GPU {i}: {gpu}")
    else:
        print("  未检测到GPU，将使用CPU")
    
    # 生成大规模数据
    X, y = generate_calibration_data(n_samples=5000, n_features=50)
    
    # GPU训练
    print("\n使用GPU训练...")
    gpu_config = GPUNetworkArchitectureFactory.create_gpu_config(
        input_dim=X.shape[1],
        output_dim=1,
        complexity='complex',
        enable_gpu=True
    )
    
    gpu_trainer = TrainingManager(gpu_config)
    gpu_trainer.prepare_data(X, y)
    gpu_trainer.create_model()
    
    import time
    start_time = time.time()
    gpu_trainer.train_model(epochs=30, verbose=0)
    gpu_time = time.time() - start_time
    
    gpu_results = gpu_trainer.evaluate_model()
    
    print(f"GPU训练完成:")
    print(f"  训练时间: {gpu_time:.2f} 秒")
    print(f"  测试集 R²: {gpu_results['test_r2']:.4f}")
    
    # CPU训练（对比）
    print("\n使用CPU训练...")
    cpu_config = GPUNetworkArchitectureFactory.create_gpu_config(
        input_dim=X.shape[1],
        output_dim=1,
        complexity='complex',
        enable_gpu=False
    )
    
    cpu_trainer = TrainingManager(cpu_config)
    cpu_trainer.prepare_data(X, y)
    cpu_trainer.create_model()
    
    start_time = time.time()
    cpu_trainer.train_model(epochs=30, verbose=0)
    cpu_time = time.time() - start_time
    
    cpu_results = cpu_trainer.evaluate_model()
    
    print(f"CPU训练完成:")
    print(f"  训练时间: {cpu_time:.2f} 秒")
    print(f"  测试集 R²: {cpu_results['test_r2']:.4f}")
    
    # 性能比较
    print("\n" + "=" * 60)
    print("性能比较:")
    print("=" * 60)
    
    if gpu_time < cpu_time:
        speedup = cpu_time / gpu_time
        print(f"GPU加速比: {speedup:.2f}x")
        print(f"GPU训练比CPU快 {(1 - gpu_time/cpu_time)*100:.1f}%")
    else:
        print("在此配置下，CPU训练更快（可能由于数据规模较小）")
    
    return {
        'gpu_time': gpu_time,
        'cpu_time': cpu_time,
        'gpu_r2': gpu_results['test_r2'],
        'cpu_r2': cpu_results['test_r2']
    }


def hyperparameter_optimization():
    """超参数优化示例"""
    print("=" * 60)
    print("超参数优化示例")
    print("=" * 60)
    
    # 生成数据
    X, y = generate_calibration_data(n_samples=2000, n_features=25)
    
    # 定义超参数搜索空间
    param_grid = {
        'complexity': ['simple', 'medium', 'complex'],
        'learning_rate': [0.001, 0.01, 0.1],
        'dropout_rate': [0.1, 0.2, 0.3],
        'architecture': ['feedforward', 'residual']
    }
    
    best_score = -np.inf
    best_params = None
    results = []
    
    print("开始超参数搜索...")
    
    # 简化的网格搜索
    for complexity in param_grid['complexity']:
        for lr in param_grid['learning_rate']:
            for dropout in param_grid['dropout_rate']:
                for arch in param_grid['architecture']:
                    
                    print(f"测试参数: {complexity}, {lr}, {dropout}, {arch}")
                    
                    try:
                        # 创建配置
                        config = GPUNetworkArchitectureFactory.create_gpu_config(
                            input_dim=X.shape[1],
                            output_dim=1,
                            complexity=complexity,
                            enable_gpu=True
                        )
                        config.architecture = arch
                        config.learning_rate = lr
                        config.dropout_rate = dropout
                        
                        # 训练模型
                        trainer = TrainingManager(config)
                        trainer.prepare_data(X, y)
                        trainer.create_model()
                        trainer.train_model(epochs=20, verbose=0)
                        
                        # 评估
                        eval_results = trainer.evaluate_model(detailed=False)
                        score = eval_results['val_r2']
                        
                        results.append({
                            'complexity': complexity,
                            'learning_rate': lr,
                            'dropout_rate': dropout,
                            'architecture': arch,
                            'val_r2': score,
                            'test_r2': eval_results['test_r2']
                        })
                        
                        if score > best_score:
                            best_score = score
                            best_params = {
                                'complexity': complexity,
                                'learning_rate': lr,
                                'dropout_rate': dropout,
                                'architecture': arch
                            }
                        
                        print(f"  验证集 R²: {score:.4f}")
                        
                    except Exception as e:
                        print(f"  训练失败: {e}")
                        continue
    
    print("\n" + "=" * 60)
    print("超参数优化结果:")
    print("=" * 60)
    
    print("最佳参数:")
    for key, value in best_params.items():
        print(f"  {key}: {value}")
    print(f"  最佳验证分数: {best_score:.4f}")
    
    # 显示前5个结果
    results_df = pd.DataFrame(results)
    top_results = results_df.nlargest(5, 'val_r2')
    
    print("\n前5个最佳配置:")
    print(top_results.to_string(index=False))
    
    return best_params, results


def complete_training_pipeline():
    """完整训练流水线示例"""
    print("=" * 60)
    print("完整训练流水线示例")
    print("=" * 60)
    
    # 生成数据
    X, y = generate_calibration_data(n_samples=3000, n_features=30)
    
    print("使用完整训练流水线...")
    
    # 使用便捷函数创建训练流水线
    trainer = create_training_pipeline(
        X=X,
        y=y,
        architecture='residual',
        complexity='medium',
        enable_gpu=True,
        epochs=100,
        test_size=0.2,
        val_size=0.2,
        save_results=True,
        results_dir="gpu_training_results"
    )
    
    # 生成报告
    report = trainer.generate_report()
    print("\n" + report)
    
    return trainer


def main():
    """主函数"""
    print("GPU加速深度神经网络训练示例")
    print("=" * 60)
    
    # 设置随机种子
    np.random.seed(42)
    tf.random.set_seed(42)
    
    try:
        # 1. 架构比较
        print("\n1. 架构比较测试")
        arch_results = compare_architectures()
        
        # 2. GPU性能测试
        print("\n2. GPU性能测试")
        perf_results = gpu_performance_test()
        
        # 3. 超参数优化
        print("\n3. 超参数优化")
        best_params, opt_results = hyperparameter_optimization()
        
        # 4. 完整训练流水线
        print("\n4. 完整训练流水线")
        pipeline_trainer = complete_training_pipeline()
        
        print("\n" + "=" * 60)
        print("所有测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()