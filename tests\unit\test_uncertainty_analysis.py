"""
Unit tests for uncertainty analysis module.
"""
import pytest
import numpy as np
from scipy import stats
from src.modules.economics.uncertainty_analysis import Uncertainty<PERSON><PERSON><PERSON><PERSON>, UncertaintyResult
from src.modules.economics.qaly_calculator import QALYResult


class TestUncertaintyAnalyzer:
    """Test suite for UncertaintyAnalyzer class."""
    
    def test_init(self):
        """Test UncertaintyAnalyzer initialization."""
        analyzer = UncertaintyAnalyzer()
        assert analyzer.n_simulations == 1000
        
        analyzer_custom = UncertaintyAnalyzer(n_simulations=500)
        assert analyzer_custom.n_simulations == 500
    
    def test_bootstrap_qaly_ci(self):
        """Test bootstrap confidence interval calculation for QALYs."""
        analyzer = UncertaintyAnalyzer(n_simulations=100)  # Use fewer simulations for testing
        
        # Create mock QALY results
        qaly_results = [
            QALYResult(
                total_qalys=10.0 + np.random.normal(0, 1),
                undiscounted_qalys=12.0,
                discounted_qalys=10.0,
                qaly_by_age_group={'50_59': 5.0, '60_69': 5.0}
            ) for _ in range(50)
        ]
        
        ci_lower, ci_upper = analyzer.bootstrap_qaly_ci(qaly_results, confidence_level=0.95)
        
        assert isinstance(ci_lower, float)
        assert isinstance(ci_upper, float)
        assert ci_lower <= ci_upper
    
    def test_bootstrap_qaly_ci_empty_input(self):
        """Test bootstrap with empty input should raise ValueError."""
        analyzer = UncertaintyAnalyzer()

        with pytest.raises(ValueError, match="QALY结果列表不能为空"):
            analyzer.bootstrap_qaly_ci([])
    
    def test_monte_carlo_sensitivity_analysis(self):
        """Test Monte Carlo sensitivity analysis."""
        analyzer = UncertaintyAnalyzer(n_simulations=50)  # Use fewer simulations for testing
        
        # Define base parameters
        base_parameters = {
            'utility_cancer': 0.75,
            'duration_cancer': 5.0
        }
        
        # Define parameter distributions
        parameter_distributions = {
            'utility_cancer': stats.norm(0.75, 0.05),  # Normal distribution
            'duration_cancer': stats.uniform(4.0, 2.0)  # Uniform distribution between 4 and 6
        }
        
        # Define outcome function
        def outcome_function(params):
            utility = params['utility_cancer']
            duration = params['duration_cancer']
            return utility * duration  # Simple QALY calculation
        
        results = analyzer.monte_carlo_sensitivity_analysis(
            base_parameters, 
            parameter_distributions, 
            outcome_function
        )
        
        assert isinstance(results, dict)
        assert 'mean_outcome' in results
        assert 'std_outcome' in results
        assert 'percentile_2_5' in results
        assert 'percentile_97_5' in results
        assert 'parameter_samples' in results
        assert 'outcome_samples' in results
        assert len(results['outcome_samples']) <= 50  # May be less due to potential errors
    
    def test_probabilistic_sensitivity_analysis(self):
        """Test probabilistic sensitivity analysis."""
        analyzer = UncertaintyAnalyzer(n_simulations=50)  # Use fewer simulations for testing
        
        # Define base parameters
        base_parameters = {
            'effectiveness': 0.8,
            'cost': 1000.0
        }
        
        # Define parameter distributions
        parameter_distributions = {
            'effectiveness': stats.beta(8, 2),  # Beta distribution
            'cost': stats.norm(1000, 100)       # Normal distribution
        }
        
        # Define outcome function (e.g., cost-effectiveness ratio)
        def outcome_function(params):
            effectiveness = params['effectiveness']
            cost = params['cost']
            return cost / effectiveness if effectiveness > 0 else float('inf')
        
        result = analyzer.probabilistic_sensitivity_analysis(
            base_parameters,
            parameter_distributions,
            outcome_function
        )
        
        assert isinstance(result, UncertaintyResult)
        assert isinstance(result.mean_outcome, float)
        assert isinstance(result.std_outcome, float)
        assert isinstance(result.confidence_interval, tuple)
        assert len(result.confidence_interval) == 2
        assert isinstance(result.percentile_2_5, float)
        assert isinstance(result.percentile_97_5, float)
        assert isinstance(result.sample_size, int)
    
    def test_calculate_confidence_interval(self):
        """Test confidence interval calculation."""
        analyzer = UncertaintyAnalyzer()
        
        # Create sample data
        data = [10.0, 11.0, 9.5, 10.5, 12.0, 9.0, 11.5, 10.2, 9.8, 10.8]
        
        ci_lower, ci_upper = analyzer.calculate_confidence_interval(data, confidence_level=0.95)
        
        assert isinstance(ci_lower, float)
        assert isinstance(ci_upper, float)
        assert ci_lower <= ci_upper
        # Mean should be within the confidence interval
        mean = np.mean(data)
        assert ci_lower <= mean <= ci_upper
    
    def test_calculate_confidence_interval_empty_input(self):
        """Test confidence interval calculation with empty input."""
        analyzer = UncertaintyAnalyzer()
        
        ci_lower, ci_upper = analyzer.calculate_confidence_interval([])
        assert ci_lower == 0.0
        assert ci_upper == 0.0
    
    def test_analyze_threshold_parameters(self):
        """Test threshold parameter analysis."""
        analyzer = UncertaintyAnalyzer()
        
        # Define base parameters
        base_parameters = {
            'screening_effectiveness': 0.7,
            'treatment_effectiveness': 0.8
        }
        
        # Define parameter bounds
        parameter_bounds = {
            'screening_effectiveness': (0.5, 0.9),
            'treatment_effectiveness': (0.6, 0.95)
        }
        
        # Define outcome function
        def outcome_function(params):
            screening = params['screening_effectiveness']
            treatment = params['treatment_effectiveness']
            return screening * treatment  # Simple combined effectiveness
        
        results = analyzer.analyze_threshold_parameters(
            base_parameters,
            parameter_bounds,
            outcome_function
        )
        
        assert isinstance(results, dict)
        assert 'base_outcome' in results
        assert 'sensitivity_by_parameter' in results
        assert isinstance(results['sensitivity_by_parameter'], dict)
        
        if results['sensitivity_by_parameter']:
            assert 'most_sensitive_parameter' in results


if __name__ == "__main__":
    pytest.main([__file__])