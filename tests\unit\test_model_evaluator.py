"""
模型评估器模块单元测试
"""

import pytest
import numpy as np
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
import tensorflow as tf
from tensorflow import keras
from sklearn.preprocessing import StandardScaler

from src.calibration.model_evaluator import ModelEvaluator


class TestModelEvaluator:
    """ModelEvaluator测试类"""
    
    def setup_method(self):
        """测试前设置"""
        # 创建一个简单的测试模型
        self.model = keras.Sequential([
            keras.layers.Dense(10, activation='relu', input_shape=(5,)),
            keras.layers.Dense(3, activation='linear')
        ])
        self.model.compile(optimizer='adam', loss='mse', metrics=['mae'])
        
        # 创建测试数据
        np.random.seed(42)
        self.X_test = np.random.randn(100, 5)
        self.y_test = np.random.randn(100, 3)
        self.X_train = np.random.randn(200, 5)
        self.y_train = np.random.randn(200, 3)
        
        # 创建标准化器
        self.input_scaler = StandardScaler()
        self.output_scaler = StandardScaler()
        self.input_scaler.fit(self.X_train)
        self.output_scaler.fit(self.y_train)
        
        # 创建目标名称
        self.target_names = ['target_1', 'target_2', 'target_3']
        
        # 创建评估器
        self.evaluator = ModelEvaluator(
            model=self.model,
            input_scaler=self.input_scaler,
            output_scaler=self.output_scaler,
            target_names=self.target_names
        )
    
    def test_initialization(self):
        """测试初始化"""
        assert self.evaluator.model == self.model
        assert self.evaluator.input_scaler == self.input_scaler
        assert self.evaluator.output_scaler == self.output_scaler
        assert self.evaluator.target_names == self.target_names
        assert self.evaluator.logger is not None
        assert self.evaluator.evaluation_results == {}
        assert self.evaluator.prediction_cache == {}
    
    def test_initialization_minimal(self):
        """测试最小化初始化"""
        evaluator = ModelEvaluator(self.model)
        
        assert evaluator.model == self.model
        assert evaluator.input_scaler is None
        assert evaluator.output_scaler is None
        assert evaluator.target_names is None
    
    def test_evaluate_basic_metrics(self):
        """测试基础指标评估"""
        metrics = self.evaluator.evaluate_basic_metrics(
            self.X_test, self.y_test, 'test'
        )
        
        assert 'test_mse' in metrics
        assert 'test_mae' in metrics
        assert 'test_r2' in metrics
        assert 'test_rmse' in metrics
        
        # 检查指标值的合理性
        assert metrics['test_mse'] >= 0
        assert metrics['test_mae'] >= 0
        assert metrics['test_rmse'] >= 0
        assert -1 <= metrics['test_r2'] <= 1
    
    def test_evaluate_comprehensive(self):
        """测试全面评估"""
        results = self.evaluator.evaluate_comprehensive(
            X_test=self.X_test,
            y_test=self.y_test,
            X_train=self.X_train,
            y_train=self.y_train,
            detailed=True
        )
        
        # 检查基础指标
        assert 'test_mse' in results
        assert 'test_mae' in results
        assert 'test_r2' in results
        assert 'train_mse' in results
        
        # 检查详细分析
        assert 'residual_analysis' in results
        assert 'prediction_intervals' in results
        assert 'feature_importance' in results
    
    def test_evaluate_comprehensive_minimal(self):
        """测试最小化全面评估"""
        results = self.evaluator.evaluate_comprehensive(
            X_test=self.X_test,
            y_test=self.y_test,
            detailed=False
        )
        
        # 应该只有基础指标
        assert 'test_mse' in results
        assert 'test_mae' in results
        assert 'residual_analysis' not in results
    
    def test_cross_validate(self):
        """测试交叉验证"""
        cv_results = self.evaluator.cross_validate(
            self.X_train, self.y_train, cv_folds=3
        )
        
        assert 'cv_scores' in cv_results
        assert 'cv_mean' in cv_results
        assert 'cv_std' in cv_results
        assert 'cv_details' in cv_results
        
        # 检查交叉验证分数
        assert len(cv_results['cv_scores']) == 3
        assert isinstance(cv_results['cv_mean'], float)
        assert isinstance(cv_results['cv_std'], float)
    
    def test_analyze_residuals(self):
        """测试残差分析"""
        y_pred = self.model.predict(self.X_test, verbose=0)
        
        residual_analysis = self.evaluator.analyze_residuals(
            self.y_test, y_pred
        )
        
        assert 'residual_stats' in residual_analysis
        assert 'normality_test' in residual_analysis
        assert 'heteroscedasticity_test' in residual_analysis
        
        # 检查残差统计
        stats = residual_analysis['residual_stats']
        assert 'mean' in stats
        assert 'std' in stats
        assert 'skewness' in stats
        assert 'kurtosis' in stats
    
    def test_calculate_prediction_intervals(self):
        """测试预测区间计算"""
        y_pred = self.model.predict(self.X_test, verbose=0)
        
        intervals = self.evaluator.calculate_prediction_intervals(
            self.y_test, y_pred, confidence=0.95
        )
        
        assert 'lower_bound' in intervals
        assert 'upper_bound' in intervals
        assert 'coverage' in intervals
        assert 'interval_width' in intervals
        
        # 检查区间形状
        assert intervals['lower_bound'].shape == y_pred.shape
        assert intervals['upper_bound'].shape == y_pred.shape
        
        # 检查覆盖率
        assert 0 <= intervals['coverage'] <= 1
    
    def test_analyze_feature_importance(self):
        """测试特征重要性分析"""
        importance = self.evaluator.analyze_feature_importance(
            self.X_test, self.y_test, method='permutation'
        )
        
        assert 'importance_scores' in importance
        assert 'feature_ranking' in importance
        assert 'method' in importance
        
        # 检查重要性分数形状
        scores = importance['importance_scores']
        assert scores.shape[0] == self.X_test.shape[1]  # 特征数量
    
    def test_analyze_feature_importance_invalid_method(self):
        """测试无效特征重要性方法"""
        with pytest.raises(ValueError, match="不支持的特征重要性方法"):
            self.evaluator.analyze_feature_importance(
                self.X_test, self.y_test, method='invalid'
            )
    
    def test_generate_prediction_report(self):
        """测试生成预测报告"""
        y_pred = self.model.predict(self.X_test, verbose=0)
        
        report = self.evaluator.generate_prediction_report(
            self.y_test, y_pred
        )
        
        assert isinstance(report, str)
        assert "预测性能报告" in report
        assert "均方误差" in report
        assert "平均绝对误差" in report
        assert "决定系数" in report
    
    def test_generate_prediction_report_with_names(self):
        """测试带目标名称的预测报告"""
        y_pred = self.model.predict(self.X_test, verbose=0)
        
        report = self.evaluator.generate_prediction_report(
            self.y_test, y_pred, target_names=self.target_names
        )
        
        assert "target_1" in report
        assert "target_2" in report
        assert "target_3" in report
    
    @patch('matplotlib.pyplot.savefig')
    @patch('matplotlib.pyplot.show')
    def test_plot_predictions(self, mock_show, mock_savefig):
        """测试绘制预测结果"""
        y_pred = self.model.predict(self.X_test, verbose=0)
        
        self.evaluator.plot_predictions(
            self.y_test, y_pred, save_path="test_predictions.png"
        )
        
        mock_savefig.assert_called_once()
    
    @patch('matplotlib.pyplot.savefig')
    @patch('matplotlib.pyplot.show')
    def test_plot_residuals(self, mock_show, mock_savefig):
        """测试绘制残差"""
        y_pred = self.model.predict(self.X_test, verbose=0)
        
        self.evaluator.plot_residuals(
            self.y_test, y_pred, save_path="test_residuals.png"
        )
        
        mock_savefig.assert_called_once()
    
    @patch('matplotlib.pyplot.savefig')
    @patch('matplotlib.pyplot.show')
    def test_plot_feature_importance(self, mock_show, mock_savefig):
        """测试绘制特征重要性"""
        importance_scores = np.random.randn(5)
        
        self.evaluator.plot_feature_importance(
            importance_scores, save_path="test_importance.png"
        )
        
        mock_savefig.assert_called_once()
    
    def test_save_evaluation_results(self):
        """测试保存评估结果"""
        with tempfile.TemporaryDirectory() as temp_dir:
            save_path = os.path.join(temp_dir, "evaluation.json")
            
            # 先进行一些评估
            self.evaluator.evaluate_basic_metrics(
                self.X_test, self.y_test, 'test'
            )
            
            # 保存结果
            self.evaluator.save_evaluation_results(save_path)
            
            # 验证文件存在
            assert os.path.exists(save_path)
    
    def test_load_evaluation_results(self):
        """测试加载评估结果"""
        with tempfile.TemporaryDirectory() as temp_dir:
            save_path = os.path.join(temp_dir, "evaluation.json")
            
            # 创建测试数据并保存
            test_results = {'test_mse': 0.5, 'test_mae': 0.3}
            self.evaluator.evaluation_results = test_results
            self.evaluator.save_evaluation_results(save_path)
            
            # 创建新的评估器并加载
            new_evaluator = ModelEvaluator(self.model)
            loaded_results = new_evaluator.load_evaluation_results(save_path)
            
            assert loaded_results['test_mse'] == 0.5
            assert loaded_results['test_mae'] == 0.3
    
    def test_load_evaluation_results_not_found(self):
        """测试加载不存在的评估结果"""
        result = self.evaluator.load_evaluation_results("nonexistent.json")
        assert result is None
    
    def test_compare_models(self):
        """测试模型比较"""
        # 创建另一个模型
        model2 = keras.Sequential([
            keras.layers.Dense(8, activation='relu', input_shape=(5,)),
            keras.layers.Dense(3, activation='linear')
        ])
        model2.compile(optimizer='adam', loss='mse', metrics=['mae'])
        
        evaluator2 = ModelEvaluator(model2)
        
        comparison = self.evaluator.compare_models(
            evaluator2, self.X_test, self.y_test
        )
        
        assert 'model1_metrics' in comparison
        assert 'model2_metrics' in comparison
        assert 'comparison_summary' in comparison
        
        # 检查指标
        assert 'mse' in comparison['model1_metrics']
        assert 'mse' in comparison['model2_metrics']
    
    def test_benchmark_performance(self):
        """测试性能基准测试"""
        benchmark = self.evaluator.benchmark_performance(
            self.X_test, n_runs=3
        )
        
        assert 'prediction_times' in benchmark
        assert 'mean_time' in benchmark
        assert 'std_time' in benchmark
        assert 'throughput' in benchmark
        
        # 检查时间统计
        assert len(benchmark['prediction_times']) == 3
        assert benchmark['mean_time'] > 0
        assert benchmark['std_time'] >= 0
        assert benchmark['throughput'] > 0
    
    def test_evaluate_uncertainty(self):
        """测试不确定性评估"""
        uncertainty = self.evaluator.evaluate_uncertainty(
            self.X_test, n_samples=10
        )
        
        assert 'mean_predictions' in uncertainty
        assert 'prediction_std' in uncertainty
        assert 'epistemic_uncertainty' in uncertainty
        assert 'aleatoric_uncertainty' in uncertainty
        
        # 检查形状
        assert uncertainty['mean_predictions'].shape == (len(self.X_test), 3)
        assert uncertainty['prediction_std'].shape == (len(self.X_test), 3)


if __name__ == "__main__":
    pytest.main([__file__])
