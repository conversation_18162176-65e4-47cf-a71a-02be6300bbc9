"""
参数约束模块
实现参数约束和边界条件检查
"""

from typing import Dict, List, Optional, Any, Callable
import numpy as np
import json
from dataclasses import dataclass
from .parameter_sampler import SamplingConfig, ParameterDefinition


@dataclass
class ConstraintDefinition:
    """约束定义数据类"""
    name: str
    constraint_type: str  # linear, ratio, custom, boundary
    parameters: List[str]  # 涉及的参数名称
    coefficients: Optional[List[float]] = None  # 线性约束系数
    bound: Optional[float] = None  # 约束边界值
    operator: str = "le"  # le, ge, eq (<=, >=, ==)
    function_code: Optional[str] = None  # 自定义约束函数代码
    description: str = ""


class ParameterConstraints:
    """参数约束管理器"""
    
    def __init__(self, constraint_definitions: List[ConstraintDefinition], 
                 parameter_config: SamplingConfig):
        """
        初始化约束管理器
        
        Args:
            constraint_definitions: 约束定义列表
            parameter_config: 参数配置
        """
        self.constraints = constraint_definitions
        self.parameter_config = parameter_config
        self.parameter_names = [p.name for p in parameter_config.parameters]
        self.parameter_indices = {name: i for i, name in enumerate(self.parameter_names)}
        self.constraint_functions = self._compile_constraints()
    
    def _compile_constraints(self) -> List[Callable]:
        """
        编译约束条件为可执行函数
        
        Returns:
            List[Callable]: 约束函数列表
        """
        functions = []
        
        for constraint in self.constraints:
            if constraint.constraint_type == 'linear':
                func = self._create_linear_constraint(constraint)
            elif constraint.constraint_type == 'ratio':
                func = self._create_ratio_constraint(constraint)
            elif constraint.constraint_type == 'boundary':
                func = self._create_boundary_constraint(constraint)
            elif constraint.constraint_type == 'custom':
                func = self._create_custom_constraint(constraint)
            else:
                raise ValueError(f"未知的约束类型: {constraint.constraint_type}")
            
            functions.append(func)
        
        return functions
    
    def _create_linear_constraint(self, constraint: ConstraintDefinition) -> Callable:
        """
        创建线性约束函数
        
        Args:
            constraint: 约束定义
            
        Returns:
            Callable: 约束函数
        """
        # 获取参数索引
        param_indices = [self.parameter_indices[name] for name in constraint.parameters]
        coefficients = np.array(constraint.coefficients)
        bound = constraint.bound
        operator = constraint.operator
        
        def linear_constraint(samples: np.ndarray) -> np.ndarray:
            """线性约束: a1*x1 + a2*x2 + ... op bound"""
            constraint_values = np.dot(samples[:, param_indices], coefficients)
            
            if operator == "le":
                return constraint_values <= bound
            elif operator == "ge":
                return constraint_values >= bound
            elif operator == "eq":
                return np.abs(constraint_values - bound) < 1e-6
            else:
                raise ValueError(f"未知的操作符: {operator}")
        
        return linear_constraint
    
    def _create_ratio_constraint(self, constraint: ConstraintDefinition) -> Callable:
        """
        创建比例约束函数
        
        Args:
            constraint: 约束定义
            
        Returns:
            Callable: 约束函数
        """
        if len(constraint.parameters) != 2:
            raise ValueError("比例约束必须涉及恰好两个参数")
        
        idx1 = self.parameter_indices[constraint.parameters[0]]
        idx2 = self.parameter_indices[constraint.parameters[1]]
        bound = constraint.bound
        operator = constraint.operator
        
        def ratio_constraint(samples: np.ndarray) -> np.ndarray:
            """比例约束: x1/x2 op bound"""
            # 避免除零错误
            denominator = samples[:, idx2]
            denominator = np.where(np.abs(denominator) < 1e-10, 1e-10, denominator)
            
            ratio_values = samples[:, idx1] / denominator
            
            if operator == "le":
                return ratio_values <= bound
            elif operator == "ge":
                return ratio_values >= bound
            elif operator == "eq":
                return np.abs(ratio_values - bound) < 1e-6
            else:
                raise ValueError(f"未知的操作符: {operator}")
        
        return ratio_constraint
    
    def _create_boundary_constraint(self, constraint: ConstraintDefinition) -> Callable:
        """
        创建边界约束函数
        
        Args:
            constraint: 约束定义
            
        Returns:
            Callable: 约束函数
        """
        param_indices = [self.parameter_indices[name] for name in constraint.parameters]
        
        def boundary_constraint(samples: np.ndarray) -> np.ndarray:
            """边界约束: 检查参数是否在定义的范围内"""
            valid_mask = np.ones(samples.shape[0], dtype=bool)
            
            for idx in param_indices:
                param_def = self.parameter_config.parameters[idx]
                param_values = samples[:, idx]
                
                # 检查是否在边界内
                within_bounds = (param_values >= param_def.min_value) & \
                               (param_values <= param_def.max_value)
                valid_mask &= within_bounds
            
            return valid_mask
        
        return boundary_constraint
    
    def _create_custom_constraint(self, constraint: ConstraintDefinition) -> Callable:
        """
        创建自定义约束函数
        
        Args:
            constraint: 约束定义
            
        Returns:
            Callable: 约束函数
        """
        if not constraint.function_code:
            raise ValueError("自定义约束必须提供函数代码")
        
        # 创建安全的执行环境
        safe_globals = {
            'np': np,
            'abs': abs,
            'min': min,
            'max': max,
            'sum': sum,
            'len': len,
            '__builtins__': {}
        }
        
        # 编译自定义函数
        exec(constraint.function_code, safe_globals)
        
        # 获取函数名（假设函数名为constraint_function）
        if 'constraint_function' not in safe_globals:
            raise ValueError("自定义约束函数必须命名为'constraint_function'")
        
        custom_func = safe_globals['constraint_function']
        param_indices = [self.parameter_indices[name] for name in constraint.parameters]
        
        def custom_constraint(samples: np.ndarray) -> np.ndarray:
            """自定义约束函数包装器"""
            # 提取相关参数
            relevant_samples = samples[:, param_indices]
            return custom_func(relevant_samples)
        
        return custom_constraint
    
    def check_constraints(self, samples: np.ndarray) -> np.ndarray:
        """
        检查样本是否满足所有约束条件
        
        Args:
            samples: 样本数组
            
        Returns:
            np.ndarray: 布尔掩码，True表示满足所有约束
        """
        valid_mask = np.ones(samples.shape[0], dtype=bool)
        
        for constraint_func in self.constraint_functions:
            try:
                constraint_satisfied = constraint_func(samples)
                valid_mask &= constraint_satisfied
            except Exception as e:
                print(f"约束检查失败: {e}")
                # 如果约束检查失败，假设所有样本都不满足该约束
                valid_mask &= False
        
        return valid_mask
    
    def get_constraint_violations(self, samples: np.ndarray) -> Dict[str, np.ndarray]:
        """
        获取每个约束的违反情况
        
        Args:
            samples: 样本数组
            
        Returns:
            Dict[str, np.ndarray]: 每个约束的违反掩码
        """
        violations = {}
        
        for i, (constraint, constraint_func) in enumerate(zip(self.constraints, self.constraint_functions)):
            try:
                constraint_satisfied = constraint_func(samples)
                violations[constraint.name] = ~constraint_satisfied
            except Exception as e:
                print(f"约束 {constraint.name} 检查失败: {e}")
                violations[constraint.name] = np.ones(samples.shape[0], dtype=bool)
        
        return violations
    
    def repair_samples(self, samples: np.ndarray, max_iterations: int = 100) -> np.ndarray:
        """
        修复违反约束的样本
        
        Args:
            samples: 原始样本
            max_iterations: 最大修复迭代次数
            
        Returns:
            np.ndarray: 修复后的样本
        """
        repaired_samples = samples.copy()
        
        for iteration in range(max_iterations):
            valid_mask = self.check_constraints(repaired_samples)
            
            if np.all(valid_mask):
                break  # 所有样本都满足约束
            
            # 获取违反约束的样本索引
            invalid_indices = np.where(~valid_mask)[0]
            
            # 对每个违反约束的样本进行修复
            for idx in invalid_indices:
                repaired_samples[idx] = self._repair_single_sample(
                    repaired_samples[idx], iteration
                )
        
        return repaired_samples
    
    def _repair_single_sample(self, sample: np.ndarray, iteration: int) -> np.ndarray:
        """
        修复单个样本
        
        Args:
            sample: 单个样本
            iteration: 当前迭代次数
            
        Returns:
            np.ndarray: 修复后的样本
        """
        repaired_sample = sample.copy()
        
        # 简单修复策略：在参数范围内随机重新抽样
        for i, param in enumerate(self.parameter_config.parameters):
            # 添加一些随机扰动
            perturbation = np.random.uniform(-0.1, 0.1) * (param.max_value - param.min_value)
            new_value = sample[i] + perturbation
            
            # 确保在边界内
            new_value = np.clip(new_value, param.min_value, param.max_value)
            repaired_sample[i] = new_value
        
        return repaired_sample
    
    def generate_constraint_report(self, samples: np.ndarray) -> Dict[str, Any]:
        """
        生成约束检查报告
        
        Args:
            samples: 样本数组
            
        Returns:
            Dict[str, Any]: 约束报告
        """
        violations = self.get_constraint_violations(samples)
        valid_mask = self.check_constraints(samples)
        
        report = {
            'total_samples': samples.shape[0],
            'valid_samples': np.sum(valid_mask),
            'invalid_samples': np.sum(~valid_mask),
            'validity_rate': np.mean(valid_mask),
            'constraint_details': {}
        }
        
        for constraint_name, violation_mask in violations.items():
            constraint_detail = {
                'violations': np.sum(violation_mask),
                'violation_rate': np.mean(violation_mask),
                'violation_indices': np.where(violation_mask)[0].tolist()
            }
            report['constraint_details'][constraint_name] = constraint_detail
        
        return report
    
    def save_constraints_config(self, filepath: str):
        """
        保存约束配置到文件
        
        Args:
            filepath: 文件路径
        """
        config_data = {
            'constraints': [
                {
                    'name': c.name,
                    'constraint_type': c.constraint_type,
                    'parameters': c.parameters,
                    'coefficients': c.coefficients,
                    'bound': c.bound,
                    'operator': c.operator,
                    'function_code': c.function_code,
                    'description': c.description
                } for c in self.constraints
            ]
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    @classmethod
    def load_constraints_config(cls, filepath: str, parameter_config: SamplingConfig):
        """
        从文件加载约束配置
        
        Args:
            filepath: 文件路径
            parameter_config: 参数配置
            
        Returns:
            ParameterConstraints: 约束管理器实例
        """
        with open(filepath, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        constraints = []
        for c_data in config_data['constraints']:
            constraint = ConstraintDefinition(
                name=c_data['name'],
                constraint_type=c_data['constraint_type'],
                parameters=c_data['parameters'],
                coefficients=c_data.get('coefficients'),
                bound=c_data.get('bound'),
                operator=c_data.get('operator', 'le'),
                function_code=c_data.get('function_code'),
                description=c_data.get('description', '')
            )
            constraints.append(constraint)
        
        return cls(constraints, parameter_config)


class ConstraintValidator:
    """约束验证器"""
    
    def __init__(self, constraints: ParameterConstraints):
        """
        初始化验证器
        
        Args:
            constraints: 约束管理器
        """
        self.constraints = constraints
    
    def validate_constraint_consistency(self) -> Dict[str, Any]:
        """
        验证约束的一致性
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        results = {
            'is_consistent': True,
            'conflicts': [],
            'warnings': []
        }
        
        # 检查约束是否过于严格（可能导致无解）
        test_samples = self._generate_test_samples(1000)
        valid_mask = self.constraints.check_constraints(test_samples)
        valid_rate = np.mean(valid_mask)
        
        if valid_rate < 0.01:
            results['is_consistent'] = False
            results['conflicts'].append(
                f"约束过于严格，只有 {valid_rate:.2%} 的随机样本满足约束"
            )
        elif valid_rate < 0.1:
            results['warnings'].append(
                f"约束较为严格，只有 {valid_rate:.2%} 的随机样本满足约束"
            )
        
        return results
    
    def _generate_test_samples(self, n_samples: int) -> np.ndarray:
        """
        生成测试样本
        
        Args:
            n_samples: 样本数量
            
        Returns:
            np.ndarray: 测试样本
        """
        n_params = len(self.constraints.parameter_config.parameters)
        samples = np.random.random((n_samples, n_params))
        
        # 缩放到参数范围
        for i, param in enumerate(self.constraints.parameter_config.parameters):
            samples[:, i] = (
                param.min_value + 
                samples[:, i] * (param.max_value - param.min_value)
            )
        
        return samples