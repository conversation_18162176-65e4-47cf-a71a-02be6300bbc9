"""
训练监控器模块
提供训练过程监控、早停机制和训练日志功能
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Optional, Any, Callable, Union
import logging
import json
import os
import time
from datetime import datetime
import warnings

# 导入TensorFlow相关
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras.callbacks import Callback

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class TrainingMonitor:
    """训练监控器类，提供全面的训练过程监控功能"""
    
    def __init__(
        self,
        patience: int = 20,
        min_delta: float = 0.001,
        monitor: str = 'val_loss',
        mode: str = 'min',
        restore_best_weights: bool = True,
        verbose: int = 1
    ):
        """
        初始化训练监控器
        
        Args:
            patience: 早停耐心值（epoch数）
            min_delta: 最小改进阈值
            monitor: 监控的指标名称
            mode: 监控模式 ('min' 或 'max')
            restore_best_weights: 是否恢复最佳权重
            verbose: 详细程度
        """
        self.patience = patience
        self.min_delta = min_delta
        self.monitor = monitor
        self.mode = mode
        self.restore_best_weights = restore_best_weights
        self.verbose = verbose
        
        # 监控状态
        self.best_score = float('inf') if mode == 'min' else float('-inf')
        self.wait = 0
        self.stopped_epoch = 0
        self.best_weights = None
        
        # 训练历史记录
        self.training_history = []
        self.epoch_logs = []
        self.training_start_time = None
        self.training_end_time = None
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 回调函数列表
        self.callbacks = []
        
        self.logger.info(f"训练监控器初始化完成 - 监控指标: {monitor}, 模式: {mode}, 耐心值: {patience}")
    
    def create_callbacks(
        self,
        model_save_path: str,
        log_dir: Optional[str] = None,
        checkpoint_freq: int = 10,
        reduce_lr_patience: int = 10,
        reduce_lr_factor: float = 0.5,
        min_lr: float = 1e-7
    ) -> List[keras.callbacks.Callback]:
        """
        创建训练回调函数列表
        
        Args:
            model_save_path: 模型保存路径
            log_dir: 日志目录
            checkpoint_freq: 检查点保存频率
            reduce_lr_patience: 学习率衰减耐心值
            reduce_lr_factor: 学习率衰减因子
            min_lr: 最小学习率
            
        Returns:
            List[keras.callbacks.Callback]: 回调函数列表
        """
        callbacks = []
        
        # 1. 早停回调
        early_stopping = keras.callbacks.EarlyStopping(
            monitor=self.monitor,
            patience=self.patience,
            min_delta=self.min_delta,
            mode=self.mode,
            restore_best_weights=self.restore_best_weights,
            verbose=self.verbose
        )
        callbacks.append(early_stopping)
        
        # 2. 模型检查点回调
        checkpoint = keras.callbacks.ModelCheckpoint(
            filepath=model_save_path,
            monitor=self.monitor,
            save_best_only=True,
            save_weights_only=False,
            mode=self.mode,
            verbose=self.verbose
        )
        callbacks.append(checkpoint)
        
        # 3. 学习率调度回调
        lr_scheduler = keras.callbacks.ReduceLROnPlateau(
            monitor=self.monitor,
            factor=reduce_lr_factor,
            patience=reduce_lr_patience,
            mode=self.mode,
            min_lr=min_lr,
            verbose=self.verbose
        )
        callbacks.append(lr_scheduler)
        
        # 4. 自定义训练日志回调
        training_logger = self.CustomTrainingLogger(self)
        callbacks.append(training_logger)
        
        # 5. TensorBoard回调（如果提供了日志目录）
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
            tensorboard = keras.callbacks.TensorBoard(
                log_dir=log_dir,
                histogram_freq=1,
                write_graph=True,
                write_images=True,
                update_freq='epoch'
            )
            callbacks.append(tensorboard)
        
        # 6. CSV日志回调
        csv_log_path = model_save_path.replace('.h5', '_training_log.csv').replace('.keras', '_training_log.csv')
        csv_logger = keras.callbacks.CSVLogger(
            filename=csv_log_path,
            separator=',',
            append=False
        )
        callbacks.append(csv_logger)
        
        # 7. 自定义性能监控回调
        performance_monitor = self.PerformanceMonitor(self, checkpoint_freq)
        callbacks.append(performance_monitor)
        
        self.callbacks = callbacks
        self.logger.info(f"创建了 {len(callbacks)} 个训练回调函数")
        
        return callbacks
    
    class CustomTrainingLogger(Callback):
        """自定义训练日志回调"""
        
        def __init__(self, monitor: 'TrainingMonitor'):
            super().__init__()
            self.monitor = monitor
            self.epoch_start_time = None
        
        def on_train_begin(self, logs=None):
            """训练开始时的回调"""
            self.monitor.training_start_time = time.time()
            self.monitor.logger.info("开始训练...")
            self.monitor.logger.info(f"监控指标: {self.monitor.monitor}")
            self.monitor.logger.info(f"早停耐心值: {self.monitor.patience}")
        
        def on_epoch_begin(self, epoch, logs=None):
            """每个epoch开始时的回调"""
            self.epoch_start_time = time.time()
        
        def on_epoch_end(self, epoch, logs=None):
            """每个epoch结束时的回调"""
            logs = logs or {}
            epoch_time = time.time() - self.epoch_start_time
            
            # 记录epoch信息
            epoch_info = {
                'epoch': epoch + 1,
                'epoch_time': epoch_time,
                'timestamp': datetime.now().isoformat(),
                **logs
            }
            
            self.monitor.epoch_logs.append(epoch_info)
            
            # 详细日志输出（每10个epoch或重要epoch）
            if (epoch + 1) % 10 == 0 or epoch < 5:
                self.monitor.logger.info(f"\nEpoch {epoch + 1} 详细信息:")
                self.monitor.logger.info(f"  训练时间: {epoch_time:.2f}s")
                
                if 'loss' in logs:
                    self.monitor.logger.info(f"  训练损失: {logs['loss']:.6f}")
                if 'val_loss' in logs:
                    self.monitor.logger.info(f"  验证损失: {logs['val_loss']:.6f}")
                if 'mae' in logs:
                    self.monitor.logger.info(f"  训练MAE: {logs['mae']:.6f}")
                if 'val_mae' in logs:
                    self.monitor.logger.info(f"  验证MAE: {logs['val_mae']:.6f}")
                
                # 自定义指标
                for key, value in logs.items():
                    if 'r2' in key.lower():
                        self.monitor.logger.info(f"  {key}: {value:.4f}")
            
            # 检查是否有改进
            current_score = logs.get(self.monitor.monitor)
            if current_score is not None:
                improved = self._check_improvement(current_score)
                if improved:
                    self.monitor.logger.info(f"  ✓ {self.monitor.monitor} 改进: {current_score:.6f}")
                elif (epoch + 1) % 5 == 0:
                    self.monitor.logger.info(f"  等待改进: {self.monitor.wait}/{self.monitor.patience}")
        
        def on_train_end(self, logs=None):
            """训练结束时的回调"""
            self.monitor.training_end_time = time.time()
            total_time = self.monitor.training_end_time - self.monitor.training_start_time
            
            self.monitor.logger.info(f"\n训练完成!")
            self.monitor.logger.info(f"总训练时间: {total_time:.2f}s ({total_time/60:.2f}min)")
            self.monitor.logger.info(f"总epoch数: {len(self.monitor.epoch_logs)}")
            
            if self.monitor.stopped_epoch > 0:
                self.monitor.logger.info(f"早停于epoch {self.monitor.stopped_epoch}")
                self.monitor.logger.info(f"最佳{self.monitor.monitor}: {self.monitor.best_score:.6f}")
        
        def _check_improvement(self, current_score: float) -> bool:
            """检查是否有改进"""
            if self.monitor.mode == 'min':
                improved = current_score < (self.monitor.best_score - self.monitor.min_delta)
            else:
                improved = current_score > (self.monitor.best_score + self.monitor.min_delta)
            
            if improved:
                self.monitor.best_score = current_score
                self.monitor.wait = 0
                return True
            else:
                self.monitor.wait += 1
                return False
    
    class PerformanceMonitor(Callback):
        """性能监控回调"""
        
        def __init__(self, monitor: 'TrainingMonitor', checkpoint_freq: int = 10):
            super().__init__()
            self.monitor = monitor
            self.checkpoint_freq = checkpoint_freq
            self.performance_history = []
        
        def on_epoch_end(self, epoch, logs=None):
            """每个epoch结束时监控性能"""
            logs = logs or {}
            
            # 记录性能指标
            performance = {
                'epoch': epoch + 1,
                'timestamp': time.time(),
                'memory_usage': self._get_memory_usage(),
                'gpu_usage': self._get_gpu_usage(),
                **logs
            }
            
            self.performance_history.append(performance)
            
            # 定期性能报告
            if (epoch + 1) % self.checkpoint_freq == 0:
                self._generate_performance_report(epoch + 1)
        
        def _get_memory_usage(self) -> Dict[str, float]:
            """获取内存使用情况"""
            try:
                import psutil
                process = psutil.Process()
                memory_info = process.memory_info()
                return {
                    'rss_mb': memory_info.rss / 1024 / 1024,  # 物理内存
                    'vms_mb': memory_info.vms / 1024 / 1024   # 虚拟内存
                }
            except ImportError:
                return {'rss_mb': 0, 'vms_mb': 0}
        
        def _get_gpu_usage(self) -> Dict[str, float]:
            """获取GPU使用情况"""
            try:
                gpus = tf.config.experimental.list_physical_devices('GPU')
                if gpus:
                    # 获取GPU内存使用情况
                    gpu_info = tf.config.experimental.get_memory_info('GPU:0')
                    return {
                        'gpu_memory_current_mb': gpu_info['current'] / 1024 / 1024,
                        'gpu_memory_peak_mb': gpu_info['peak'] / 1024 / 1024
                    }
            except:
                pass
            return {'gpu_memory_current_mb': 0, 'gpu_memory_peak_mb': 0}
        
        def _generate_performance_report(self, epoch: int):
            """生成性能报告"""
            if not self.performance_history:
                return
            
            recent_performance = self.performance_history[-self.checkpoint_freq:]
            
            # 计算平均性能
            avg_memory = np.mean([p['memory_usage']['rss_mb'] for p in recent_performance])
            avg_gpu_memory = np.mean([p['gpu_usage']['gpu_memory_current_mb'] for p in recent_performance])
            
            self.monitor.logger.info(f"\n性能报告 (Epoch {epoch}):")
            self.monitor.logger.info(f"  平均内存使用: {avg_memory:.1f} MB")
            if avg_gpu_memory > 0:
                self.monitor.logger.info(f"  平均GPU内存使用: {avg_gpu_memory:.1f} MB")
    
    def start_monitoring(self):
        """开始监控"""
        self.training_start_time = time.time()
        self.epoch_logs = []
        self.best_score = float('inf') if self.mode == 'min' else float('-inf')
        self.wait = 0
        self.stopped_epoch = 0
        
        self.logger.info("开始训练监控...")
    
    def stop_monitoring(self):
        """停止监控"""
        self.training_end_time = time.time()
        self.logger.info("训练监控结束")
    
    def get_training_summary(self) -> Dict[str, Any]:
        """获取训练摘要"""
        if not self.epoch_logs:
            return {}
        
        total_epochs = len(self.epoch_logs)
        total_time = self.training_end_time - self.training_start_time if self.training_end_time else 0
        
        # 计算平均每epoch时间
        epoch_times = [log.get('epoch_time', 0) for log in self.epoch_logs]
        avg_epoch_time = np.mean(epoch_times) if epoch_times else 0
        
        # 获取最终指标
        final_metrics = self.epoch_logs[-1] if self.epoch_logs else {}
        
        # 获取最佳指标
        best_epoch_idx = self._find_best_epoch()
        best_metrics = self.epoch_logs[best_epoch_idx] if best_epoch_idx >= 0 else {}
        
        summary = {
            'total_epochs': total_epochs,
            'total_time_seconds': total_time,
            'total_time_minutes': total_time / 60,
            'avg_epoch_time_seconds': avg_epoch_time,
            'stopped_early': self.stopped_epoch > 0,
            'stopped_epoch': self.stopped_epoch,
            'best_epoch': best_epoch_idx + 1 if best_epoch_idx >= 0 else None,
            'best_score': self.best_score,
            'final_metrics': final_metrics,
            'best_metrics': best_metrics,
            'improvement_history': self._get_improvement_history()
        }
        
        return summary
    
    def _find_best_epoch(self) -> int:
        """找到最佳epoch"""
        if not self.epoch_logs:
            return -1
        
        monitor_values = [log.get(self.monitor) for log in self.epoch_logs]
        monitor_values = [v for v in monitor_values if v is not None]
        
        if not monitor_values:
            return -1
        
        if self.mode == 'min':
            best_idx = np.argmin(monitor_values)
        else:
            best_idx = np.argmax(monitor_values)
        
        return best_idx
    
    def _get_improvement_history(self) -> List[Dict[str, Any]]:
        """获取改进历史"""
        improvements = []
        current_best = float('inf') if self.mode == 'min' else float('-inf')
        
        for i, log in enumerate(self.epoch_logs):
            monitor_value = log.get(self.monitor)
            if monitor_value is None:
                continue
            
            improved = False
            if self.mode == 'min':
                if monitor_value < current_best - self.min_delta:
                    current_best = monitor_value
                    improved = True
            else:
                if monitor_value > current_best + self.min_delta:
                    current_best = monitor_value
                    improved = True
            
            if improved:
                improvements.append({
                    'epoch': i + 1,
                    'value': monitor_value,
                    'improvement': abs(monitor_value - current_best) if len(improvements) > 0 else 0
                })
        
        return improvements
    
    def plot_training_history(
        self,
        save_path: Optional[str] = None,
        figsize: tuple = (15, 10)
    ):
        """
        绘制训练历史图表
        
        Args:
            save_path: 保存路径
            figsize: 图形大小
        """
        if not self.epoch_logs:
            self.logger.warning("没有训练历史数据可供绘制")
            return
        
        # 提取数据
        epochs = [log['epoch'] for log in self.epoch_logs]
        
        # 创建子图
        fig, axes = plt.subplots(2, 3, figsize=figsize)
        fig.suptitle('训练历史监控', fontsize=16)
        
        # 1. 损失曲线
        train_loss = [log.get('loss') for log in self.epoch_logs]
        val_loss = [log.get('val_loss') for log in self.epoch_logs]
        
        if any(v is not None for v in train_loss):
            axes[0, 0].plot(epochs, train_loss, label='训练损失', color='blue')
        if any(v is not None for v in val_loss):
            axes[0, 0].plot(epochs, val_loss, label='验证损失', color='red')
        
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('损失')
        axes[0, 0].set_title('损失曲线')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 标记最佳epoch
        best_epoch_idx = self._find_best_epoch()
        if best_epoch_idx >= 0:
            best_epoch = epochs[best_epoch_idx]
            best_loss = self.epoch_logs[best_epoch_idx].get(self.monitor)
            if best_loss is not None:
                axes[0, 0].axvline(x=best_epoch, color='green', linestyle='--', alpha=0.7, label='最佳epoch')
                axes[0, 0].legend()
        
        # 2. MAE曲线
        train_mae = [log.get('mae') for log in self.epoch_logs]
        val_mae = [log.get('val_mae') for log in self.epoch_logs]
        
        if any(v is not None for v in train_mae):
            axes[0, 1].plot(epochs, train_mae, label='训练MAE', color='blue')
        if any(v is not None for v in val_mae):
            axes[0, 1].plot(epochs, val_mae, label='验证MAE', color='red')
        
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('MAE')
        axes[0, 1].set_title('平均绝对误差')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # 3. R²曲线（如果有）
        r2_metrics = [key for key in self.epoch_logs[0].keys() if 'r2' in key.lower()]
        if r2_metrics:
            for metric in r2_metrics[:2]:  # 最多显示2个R²指标
                values = [log.get(metric) for log in self.epoch_logs]
                if any(v is not None for v in values):
                    axes[0, 2].plot(epochs, values, label=metric)
        
        axes[0, 2].set_xlabel('Epoch')
        axes[0, 2].set_ylabel('R²')
        axes[0, 2].set_title('决定系数')
        axes[0, 2].legend()
        axes[0, 2].grid(True)
        
        # 4. 学习率曲线
        learning_rates = [log.get('lr') for log in self.epoch_logs]
        if any(v is not None for v in learning_rates):
            axes[1, 0].plot(epochs, learning_rates, color='orange')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('学习率')
            axes[1, 0].set_title('学习率变化')
            axes[1, 0].set_yscale('log')
            axes[1, 0].grid(True)
        else:
            axes[1, 0].text(0.5, 0.5, '学习率数据不可用', ha='center', va='center', transform=axes[1, 0].transAxes)
            axes[1, 0].set_title('学习率变化')
        
        # 5. 训练时间分析
        epoch_times = [log.get('epoch_time', 0) for log in self.epoch_logs]
        if epoch_times:
            axes[1, 1].plot(epochs, epoch_times, color='purple')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('时间 (秒)')
            axes[1, 1].set_title('每Epoch训练时间')
            axes[1, 1].grid(True)
            
            # 添加平均时间线
            avg_time = np.mean(epoch_times)
            axes[1, 1].axhline(y=avg_time, color='red', linestyle='--', alpha=0.7, label=f'平均: {avg_time:.2f}s')
            axes[1, 1].legend()
        
        # 6. 改进历史
        improvements = self._get_improvement_history()
        if improvements:
            improvement_epochs = [imp['epoch'] for imp in improvements]
            improvement_values = [imp['value'] for imp in improvements]
            
            axes[1, 2].plot(improvement_epochs, improvement_values, 'go-', markersize=8)
            axes[1, 2].set_xlabel('Epoch')
            axes[1, 2].set_ylabel(self.monitor)
            axes[1, 2].set_title('模型改进历史')
            axes[1, 2].grid(True)
            
            # 标注改进点
            for i, (epoch, value) in enumerate(zip(improvement_epochs, improvement_values)):
                if i % max(1, len(improvement_epochs) // 5) == 0:  # 避免标注过密
                    axes[1, 2].annotate(f'{value:.4f}', (epoch, value), 
                                       textcoords="offset points", xytext=(0,10), ha='center')
        else:
            axes[1, 2].text(0.5, 0.5, '无改进记录', ha='center', va='center', transform=axes[1, 2].transAxes)
            axes[1, 2].set_title('模型改进历史')
        
        plt.tight_layout()
        
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"训练历史图表已保存到: {save_path}")
        
        plt.show()
    
    def generate_training_report(self) -> str:
        """
        生成训练报告
        
        Returns:
            str: 详细的训练报告
        """
        summary = self.get_training_summary()
        
        report = []
        report.append("=" * 80)
        report.append("训练监控报告")
        report.append("=" * 80)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 基本信息
        report.append("训练基本信息:")
        report.append("-" * 40)
        report.append(f"总epoch数: {summary.get('total_epochs', 0)}")
        report.append(f"总训练时间: {summary.get('total_time_minutes', 0):.2f} 分钟")
        report.append(f"平均每epoch时间: {summary.get('avg_epoch_time_seconds', 0):.2f} 秒")
        report.append(f"是否早停: {'是' if summary.get('stopped_early', False) else '否'}")
        
        if summary.get('stopped_early', False):
            report.append(f"早停epoch: {summary.get('stopped_epoch', 0)}")
        
        report.append(f"监控指标: {self.monitor}")
        report.append(f"最佳分数: {summary.get('best_score', 0):.6f}")
        report.append(f"最佳epoch: {summary.get('best_epoch', 0)}")
        report.append("")
        
        # 最终性能
        final_metrics = summary.get('final_metrics', {})
        if final_metrics:
            report.append("最终性能指标:")
            report.append("-" * 40)
            for key, value in final_metrics.items():
                if isinstance(value, (int, float)) and key != 'epoch':
                    report.append(f"{key}: {value:.6f}")
            report.append("")
        
        # 最佳性能
        best_metrics = summary.get('best_metrics', {})
        if best_metrics:
            report.append("最佳性能指标:")
            report.append("-" * 40)
            for key, value in best_metrics.items():
                if isinstance(value, (int, float)) and key != 'epoch':
                    report.append(f"{key}: {value:.6f}")
            report.append("")
        
        # 改进历史
        improvements = summary.get('improvement_history', [])
        if improvements:
            report.append("模型改进历史:")
            report.append("-" * 40)
            report.append(f"总改进次数: {len(improvements)}")
            report.append("主要改进点:")
            
            # 显示前5个最重要的改进
            for i, imp in enumerate(improvements[:5]):
                report.append(f"  Epoch {imp['epoch']}: {imp['value']:.6f}")
            
            if len(improvements) > 5:
                report.append(f"  ... 还有 {len(improvements) - 5} 个改进点")
            report.append("")
        
        # 训练建议
        report.append("训练分析和建议:")
        report.append("-" * 40)
        
        suggestions = self._generate_training_suggestions(summary)
        for suggestion in suggestions:
            report.append(f"• {suggestion}")
        
        report.append("")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def _generate_training_suggestions(self, summary: Dict[str, Any]) -> List[str]:
        """
        基于训练摘要生成建议
        
        Args:
            summary: 训练摘要
            
        Returns:
            List[str]: 建议列表
        """
        suggestions = []
        
        # 检查是否早停
        if summary.get('stopped_early', False):
            stopped_epoch = summary.get('stopped_epoch', 0)
            total_epochs = summary.get('total_epochs', 0)
            if stopped_epoch < total_epochs * 0.3:
                suggestions.append("模型很早就停止训练，可能学习率过大或数据质量有问题")
            else:
                suggestions.append("模型正常早停，防止了过拟合")
        else:
            suggestions.append("模型未触发早停，可能需要更多训练epoch或调整早停参数")
        
        # 检查训练时间
        avg_epoch_time = summary.get('avg_epoch_time_seconds', 0)
        if avg_epoch_time > 60:
            suggestions.append("每epoch训练时间较长，考虑减少批次大小或使用GPU加速")
        elif avg_epoch_time < 1:
            suggestions.append("训练速度很快，可以考虑增加模型复杂度或数据量")
        
        # 检查改进频率
        improvements = summary.get('improvement_history', [])
        total_epochs = summary.get('total_epochs', 1)
        improvement_rate = len(improvements) / total_epochs
        
        if improvement_rate < 0.1:
            suggestions.append("模型改进频率较低，可能需要调整学习率或模型架构")
        elif improvement_rate > 0.5:
            suggestions.append("模型改进频率很高，训练效果良好")
        
        # 检查最佳epoch位置
        best_epoch = summary.get('best_epoch', 0)
        if best_epoch and best_epoch < total_epochs * 0.2:
            suggestions.append("最佳性能出现较早，可能存在过拟合风险")
        elif best_epoch and best_epoch > total_epochs * 0.8:
            suggestions.append("最佳性能出现较晚，可能需要更多训练时间")
        
        # 检查监控指标
        best_score = summary.get('best_score', 0)
        if self.monitor == 'val_loss' and best_score > 1.0:
            suggestions.append("验证损失较高，考虑增加正则化或改进数据预处理")
        elif 'r2' in self.monitor.lower() and best_score < 0.8:
            suggestions.append("R²分数较低，模型拟合效果有待提升")
        
        if not suggestions:
            suggestions.append("训练过程正常，各项指标均在合理范围内")
        
        return suggestions
    
    def save_training_log(self, save_path: str):
        """
        保存训练日志
        
        Args:
            save_path: 保存路径
        """
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # 保存详细日志
        log_data = {
            'monitor_config': {
                'patience': self.patience,
                'min_delta': self.min_delta,
                'monitor': self.monitor,
                'mode': self.mode,
                'restore_best_weights': self.restore_best_weights
            },
            'training_summary': self.get_training_summary(),
            'epoch_logs': self.epoch_logs,
            'training_start_time': self.training_start_time,
            'training_end_time': self.training_end_time
        }
        
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, indent=2, ensure_ascii=False, default=str)
        
        # 保存训练报告
        report_path = save_path.replace('.json', '_report.txt')
        report = self.generate_training_report()
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        self.logger.info(f"训练日志已保存到: {save_path}")
        self.logger.info(f"训练报告已保存到: {report_path}")
    
    def load_training_log(self, load_path: str):
        """
        加载训练日志
        
        Args:
            load_path: 加载路径
        """
        try:
            with open(load_path, 'r', encoding='utf-8') as f:
                log_data = json.load(f)
            
            # 恢复配置
            monitor_config = log_data.get('monitor_config', {})
            self.patience = monitor_config.get('patience', self.patience)
            self.min_delta = monitor_config.get('min_delta', self.min_delta)
            self.monitor = monitor_config.get('monitor', self.monitor)
            self.mode = monitor_config.get('mode', self.mode)
            
            # 恢复训练历史
            self.epoch_logs = log_data.get('epoch_logs', [])
            self.training_start_time = log_data.get('training_start_time')
            self.training_end_time = log_data.get('training_end_time')
            
            # 恢复最佳状态
            training_summary = log_data.get('training_summary', {})
            self.best_score = training_summary.get('best_score', self.best_score)
            self.stopped_epoch = training_summary.get('stopped_epoch', 0)
            
            self.logger.info(f"训练日志已从 {load_path} 加载")
            
        except Exception as e:
            self.logger.error(f"加载训练日志失败: {e}")
            raise


class EarlyStoppingMonitor:
    """独立的早停监控器"""
    
    def __init__(
        self,
        patience: int = 20,
        min_delta: float = 0.001,
        monitor: str = 'val_loss',
        mode: str = 'min',
        baseline: Optional[float] = None
    ):
        """
        初始化早停监控器
        
        Args:
            patience: 耐心值
            min_delta: 最小改进阈值
            monitor: 监控指标
            mode: 监控模式
            baseline: 基线值
        """
        self.patience = patience
        self.min_delta = min_delta
        self.monitor = monitor
        self.mode = mode
        self.baseline = baseline
        
        self.best_score = baseline if baseline is not None else (float('inf') if mode == 'min' else float('-inf'))
        self.wait = 0
        self.stopped_epoch = 0
        self.should_stop = False
        
        self.logger = logging.getLogger(__name__)
    
    def check_early_stopping(self, current_score: float, epoch: int) -> bool:
        """
        检查是否应该早停
        
        Args:
            current_score: 当前分数
            epoch: 当前epoch
            
        Returns:
            bool: 是否应该早停
        """
        improved = False
        
        if self.mode == 'min':
            if current_score < (self.best_score - self.min_delta):
                self.best_score = current_score
                self.wait = 0
                improved = True
        else:
            if current_score > (self.best_score + self.min_delta):
                self.best_score = current_score
                self.wait = 0
                improved = True
        
        if not improved:
            self.wait += 1
            if self.wait >= self.patience:
                self.stopped_epoch = epoch
                self.should_stop = True
                self.logger.info(f"早停触发于epoch {epoch}, 最佳{self.monitor}: {self.best_score:.6f}")
                return True
        
        return False
    
    def reset(self):
        """重置早停状态"""
        self.best_score = self.baseline if self.baseline is not None else (float('inf') if self.mode == 'min' else float('-inf'))
        self.wait = 0
        self.stopped_epoch = 0
        self.should_stop = False


class LearningRateScheduler:
    """学习率调度器"""
    
    def __init__(
        self,
        schedule_type: str = 'plateau',
        patience: int = 10,
        factor: float = 0.5,
        min_lr: float = 1e-7,
        monitor: str = 'val_loss',
        mode: str = 'min'
    ):
        """
        初始化学习率调度器
        
        Args:
            schedule_type: 调度类型 ('plateau', 'exponential', 'step')
            patience: 耐心值（用于plateau）
            factor: 衰减因子
            min_lr: 最小学习率
            monitor: 监控指标
            mode: 监控模式
        """
        self.schedule_type = schedule_type
        self.patience = patience
        self.factor = factor
        self.min_lr = min_lr
        self.monitor = monitor
        self.mode = mode
        
        self.best_score = float('inf') if mode == 'min' else float('-inf')
        self.wait = 0
        self.current_lr = None
        
        self.logger = logging.getLogger(__name__)
    
    def step(self, current_score: float, optimizer: tf.keras.optimizers.Optimizer) -> float:
        """
        执行学习率调度步骤
        
        Args:
            current_score: 当前分数
            optimizer: 优化器
            
        Returns:
            float: 新的学习率
        """
        if self.current_lr is None:
            self.current_lr = float(optimizer.learning_rate.numpy())
        
        if self.schedule_type == 'plateau':
            return self._plateau_schedule(current_score, optimizer)
        elif self.schedule_type == 'exponential':
            return self._exponential_schedule(optimizer)
        elif self.schedule_type == 'step':
            return self._step_schedule(optimizer)
        else:
            return self.current_lr
    
    def _plateau_schedule(self, current_score: float, optimizer: tf.keras.optimizers.Optimizer) -> float:
        """基于平台的学习率调度"""
        improved = False
        
        if self.mode == 'min':
            if current_score < self.best_score:
                self.best_score = current_score
                self.wait = 0
                improved = True
        else:
            if current_score > self.best_score:
                self.best_score = current_score
                self.wait = 0
                improved = True
        
        if not improved:
            self.wait += 1
            if self.wait >= self.patience:
                new_lr = max(self.current_lr * self.factor, self.min_lr)
                if new_lr < self.current_lr:
                    optimizer.learning_rate.assign(new_lr)
                    self.current_lr = new_lr
                    self.wait = 0
                    self.logger.info(f"学习率降低到: {new_lr:.2e}")
        
        return self.current_lr
    
    def _exponential_schedule(self, optimizer: tf.keras.optimizers.Optimizer) -> float:
        """指数衰减学习率调度"""
        new_lr = max(self.current_lr * self.factor, self.min_lr)
        if new_lr < self.current_lr:
            optimizer.learning_rate.assign(new_lr)
            self.current_lr = new_lr
        return self.current_lr
    
    def _step_schedule(self, optimizer: tf.keras.optimizers.Optimizer) -> float:
        """步进学习率调度"""
        # 简单的步进调度，每patience个epoch降低一次
        self.wait += 1
        if self.wait >= self.patience:
            new_lr = max(self.current_lr * self.factor, self.min_lr)
            if new_lr < self.current_lr:
                optimizer.learning_rate.assign(new_lr)
                self.current_lr = new_lr
                self.logger.info(f"步进学习率降低到: {new_lr:.2e}")
            self.wait = 0
        return self.current_lr


def create_training_monitor(
    patience: int = 20,
    min_delta: float = 0.001,
    monitor: str = 'val_loss',
    mode: str = 'min',
    verbose: int = 1
) -> TrainingMonitor:
    """
    创建训练监控器的便捷函数
    
    Args:
        patience: 早停耐心值
        min_delta: 最小改进阈值
        monitor: 监控指标
        mode: 监控模式
        verbose: 详细程度
        
    Returns:
        TrainingMonitor: 训练监控器实例
    """
    return TrainingMonitor(
        patience=patience,
        min_delta=min_delta,
        monitor=monitor,
        mode=mode,
        verbose=verbose
    )


if __name__ == "__main__":
    # 示例用法
    import numpy as np
    from sklearn.datasets import make_regression
    from sklearn.model_selection import train_test_split
    from sklearn.preprocessing import StandardScaler
    from tensorflow import keras
    
    # 生成示例数据
    X, y = make_regression(n_samples=1000, n_features=10, noise=0.1, random_state=42)
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    X_train, X_val, y_train, y_val = train_test_split(X_train, y_train, test_size=0.2, random_state=42)
    
    # 数据标准化
    scaler_X = StandardScaler()
    scaler_y = StandardScaler()
    
    X_train_scaled = scaler_X.fit_transform(X_train)
    X_val_scaled = scaler_X.transform(X_val)
    X_test_scaled = scaler_X.transform(X_test)
    y_train_scaled = scaler_y.fit_transform(y_train.reshape(-1, 1)).flatten()
    y_val_scaled = scaler_y.transform(y_val.reshape(-1, 1)).flatten()
    y_test_scaled = scaler_y.transform(y_test.reshape(-1, 1)).flatten()
    
    # 创建模型
    model = keras.Sequential([
        keras.layers.Dense(64, activation='relu', input_shape=(10,)),
        keras.layers.Dropout(0.2),
        keras.layers.Dense(32, activation='relu'),
        keras.layers.Dropout(0.2),
        keras.layers.Dense(1)
    ])
    
    model.compile(optimizer='adam', loss='mse', metrics=['mae'])
    
    # 创建训练监控器
    monitor = create_training_monitor(
        patience=15,
        min_delta=0.001,
        monitor='val_loss',
        mode='min',
        verbose=1
    )
    
    # 创建回调函数
    callbacks = monitor.create_callbacks(
        model_save_path='models/best_model.keras',
        log_dir='logs/training',
        checkpoint_freq=5
    )
    
    # 开始监控
    monitor.start_monitoring()
    
    # 训练模型
    print("开始训练模型...")
    history = model.fit(
        X_train_scaled, y_train_scaled,
        validation_data=(X_val_scaled, y_val_scaled),
        epochs=100,
        batch_size=32,
        callbacks=callbacks,
        verbose=1
    )
    
    # 停止监控
    monitor.stop_monitoring()
    
    # 生成训练摘要
    summary = monitor.get_training_summary()
    print(f"\n训练摘要:")
    print(f"总epoch数: {summary['total_epochs']}")
    print(f"总训练时间: {summary['total_time_minutes']:.2f} 分钟")
    print(f"最佳分数: {summary['best_score']:.6f}")
    print(f"最佳epoch: {summary['best_epoch']}")
    
    # 生成训练报告
    report = monitor.generate_training_report()
    print("\n" + "="*50)
    print("训练报告:")
    print("="*50)
    print(report)
    
    # 绘制训练历史
    monitor.plot_training_history(save_path='results/training_history.png')
    
    # 保存训练日志
    monitor.save_training_log('logs/training_monitor.json')
    
    print("\n训练监控示例完成！")
