"""
Integration tests for health outcomes calculation components.
"""
import pytest
import numpy as np
from src.modules.economics.qaly_calculator import QALYCalculator, HealthState
from src.modules.economics.lyg_calculator import LYGCalculator
from src.modules.economics.utility_values import UtilityValueManager
from src.modules.economics.uncertainty_analysis import UncertaintyAnalyzer
from src.core.individual import Individual
from src.core.population import Population


class TestHealthOutcomesIntegration:
    """Integration tests for health outcomes calculation components."""
    
    def test_qaly_calculation_pipeline(self):
        """Test the complete QALY calculation pipeline."""
        # Initialize components
        utility_manager = UtilityValueManager()
        qaly_calculator = QALYCalculator(discount_rate=0.03)
        uncertainty_analyzer = UncertaintyAnalyzer(n_simulations=100)
        
        # Create a population
        population = Population()
        population.individuals = []
        
        # Create individuals with different health states
        from src.core.enums import Gender
        for i in range(10):
            individual = Individual(birth_year=1980, gender=Gender.FEMALE)
            individual.age = 50 + i * 2
            individual.baseline_age = 45 + i * 2
            
            # Assign different disease states
            disease_states = [
                'normal', 'low_risk_adenoma', 'high_risk_adenoma',
                'clinical_cancer_stage_i', 'clinical_cancer_stage_ii'
            ]
            individual.disease_state = disease_states[i % len(disease_states)]
            
            population.individuals.append(individual)
        
        # Calculate QALYs for population
        scenario_results = qaly_calculator.calculate_population_qalys(population, "test_scenario")
        
        # Verify results structure
        assert 'total_population_qalys' in scenario_results
        assert 'mean_individual_qalys' in scenario_results
        assert 'individual_results' in scenario_results
        assert 'qaly_distribution' in scenario_results
        
        # Verify individual results
        individual_results = scenario_results['individual_results']
        assert len(individual_results) == len(population.individuals)
        
        # Verify QALY results are reasonable
        for result in individual_results:
            assert result.total_qalys >= 0
            assert result.undiscounted_qalys >= result.discounted_qalys  # Discounting should reduce values
        
        # Perform uncertainty analysis
        if individual_results:
            ci_lower, ci_upper = uncertainty_analyzer.bootstrap_qaly_ci(individual_results)
            assert ci_lower <= scenario_results['mean_individual_qalys'] <= ci_upper
    
    def test_lyg_calculation_pipeline(self):
        """Test the complete LYG calculation pipeline."""
        # Initialize components
        lyg_calculator = LYGCalculator()
        
        # Create sample data representing two different screening strategies
        ages = list(range(50, 80))
        
        # Strategy 1: More effective screening (higher survival)
        intervention_survival = [np.exp(-0.01 * (age - 50)) for age in ages]
        
        # Strategy 2: Less effective screening (lower survival)
        control_survival = [np.exp(-0.015 * (age - 50)) for age in ages]
        
        # Calculate LYG
        lyg_result = lyg_calculator.calculate_lyg(intervention_survival, control_survival, ages)
        
        # Verify basic structure
        assert 'total_lyg' in lyg_result
        assert 'lyg_by_age' in lyg_result
        assert 'mean_lyg_per_person' in lyg_result
        
        # Verify results are reasonable
        assert lyg_result['total_lyg'] >= 0  # Intervention should be better
        assert lyg_result['mean_lyg_per_person'] >= 0
        
        # Apply discounting
        discounted_result = lyg_calculator.calculate_discounted_lyg(lyg_result)
        
        # Verify discounted results
        assert 'total_discounted_lyg' in discounted_result
        assert discounted_result['total_discounted_lyg'] <= lyg_result['total_lyg']  # Discounting reduces values
    
    def test_utility_management_integration(self):
        """Test integration of utility value management with QALY calculation."""
        # Initialize components
        utility_manager = UtilityValueManager()
        qaly_calculator = QALYCalculator()
        
        # Test loading utilities from manager
        cancer_utility = utility_manager.get_utility_value('clinical_cancer_stage_ii')
        age_adjusted_utility = utility_manager.get_age_adjusted_utility(cancer_utility, 65)
        
        # Create health state using utility values
        health_state = HealthState(
            state_name='clinical_cancer_stage_ii',
            utility_value=age_adjusted_utility,
            duration_years=3.0,
            age_at_start=65
        )
        
        # Create individual and calculate QALYs
        from src.core.enums import Gender
        individual = Individual(birth_year=1980, gender=Gender.FEMALE)
        individual.age = 68
        individual.baseline_age = 65
        individual.disease_state = 'clinical_cancer_stage_ii'
        
        qaly_result = qaly_calculator.calculate_individual_qalys(individual, [health_state])
        
        # Verify results
        from src.modules.economics.qaly_calculator import QALYResult
        assert isinstance(qaly_result, QALYResult)
        assert qaly_result.total_qalys >= 0
    
    def test_uncertainty_analysis_integration(self):
        """Test integration of uncertainty analysis with health outcomes."""
        # Initialize components
        uncertainty_analyzer = UncertaintyAnalyzer(n_simulations=50)
        utility_manager = UtilityValueManager()
        
        # Test probabilistic sensitivity analysis
        base_parameters = {
            'base_utility': 0.85,
            'treatment_duration': 2.0
        }
        
        # Define parameter distributions for sensitivity analysis
        from scipy import stats
        parameter_distributions = {
            'base_utility': stats.norm(0.85, 0.05),  # Normal distribution around base utility
            'treatment_duration': stats.uniform(1.0, 3.0)  # Uniform between 1 and 4 years
        }
        
        # Define outcome function
        def outcome_function(params):
            utility = params['base_utility']
            duration = params['treatment_duration']
            return utility * duration  # Simple QALY calculation
        
        # Perform probabilistic sensitivity analysis
        psa_result = uncertainty_analyzer.probabilistic_sensitivity_analysis(
            base_parameters,
            parameter_distributions,
            outcome_function
        )
        
        # Verify results
        from src.modules.economics.uncertainty_analysis import UncertaintyResult
        assert isinstance(psa_result, UncertaintyResult)
        assert psa_result.mean_outcome >= 0
        assert psa_result.sample_size > 0
        assert psa_result.confidence_interval[0] <= psa_result.mean_outcome <= psa_result.confidence_interval[1]
    
    def test_complete_health_outcomes_workflow(self):
        """Test a complete workflow combining all health outcomes components."""
        # Initialize all components
        utility_manager = UtilityValueManager()
        qaly_calculator = QALYCalculator(discount_rate=0.03)
        lyg_calculator = LYGCalculator()
        uncertainty_analyzer = UncertaintyAnalyzer(n_simulations=50)
        
        # Create a simple population
        population = Population()
        population.individuals = []
        
        # Create individuals
        from src.core.enums import Gender
        for i in range(5):
            individual = Individual(birth_year=1980, gender=Gender.FEMALE)
            individual.age = 55 + i * 3
            individual.baseline_age = 50 + i * 3
            individual.disease_state = 'clinical_cancer_stage_i' if i % 2 == 0 else 'clinical_cancer_stage_ii'
            population.individuals.append(individual)
        
        # Calculate QALYs
        qaly_results = qaly_calculator.calculate_population_qalys(population, "intervention")
        
        # Verify QALY results
        assert qaly_results['total_population_qalys'] >= 0
        assert len(qaly_results['individual_results']) == len(population.individuals)
        
        # Perform uncertainty analysis on QALY results
        if qaly_results['individual_results']:
            ci_lower, ci_upper = uncertainty_analyzer.bootstrap_qaly_ci(
                qaly_results['individual_results']
            )
            assert ci_lower <= qaly_results['mean_individual_qalys'] <= ci_upper
        
        # Create simple LYG analysis
        ages = [50, 55, 60, 65, 70]
        intervention_survival = [0.95, 0.90, 0.80, 0.65, 0.45]
        control_survival = [0.90, 0.80, 0.65, 0.50, 0.30]
        
        lyg_result = lyg_calculator.calculate_lyg(intervention_survival, control_survival, ages)
        discounted_lyg_result = lyg_calculator.calculate_discounted_lyg(lyg_result)
        
        # Verify LYG results
        assert discounted_lyg_result['total_discounted_lyg'] >= 0
        assert len(discounted_lyg_result['lyg_by_age']) == len(ages)


if __name__ == "__main__":
    pytest.main([__file__])