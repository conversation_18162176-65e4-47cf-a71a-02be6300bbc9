"""
多策略成本效益比较分析模块

该模块实现多个筛查策略的成本效益比较分析，包括：
- 成本效益前沿分析
- 策略排序和筛选
- 策略比较可视化数据准备
- 策略选择决策支持

作者：<PERSON> (Dev Agent)
创建日期：2025-01-07
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
import numpy as np
import pandas as pd
from scipy import stats
import logging

from .icer_calculator import ICERCalculator, ICERInterpretation
from .nhb_calculator import NHBCalculator
from .ceac_generator import CEACGenerator

logger = logging.getLogger(__name__)


class StrategyRanking(Enum):
    """策略排名方法"""
    BY_ICER = "by_icer"
    BY_NHB = "by_nhb"
    BY_COST = "by_cost"
    BY_EFFECTIVENESS = "by_effectiveness"
    BY_DOMINANCE = "by_dominance"


@dataclass
class StrategyData:
    """策略数据结构"""
    name: str
    cost: float
    qalys: float
    cost_samples: Optional[np.ndarray] = None
    qaly_samples: Optional[np.ndarray] = None
    description: Optional[str] = None
    parameters: Optional[Dict] = None


@dataclass
class ComparisonResult:
    """比较结果数据结构"""
    strategy_name: str
    total_cost: float
    total_qalys: float
    incremental_cost: float
    incremental_qalys: float
    icer: Optional[float]
    icer_interpretation: ICERInterpretation
    nhb: float
    rank_by_icer: int
    rank_by_nhb: int
    is_on_frontier: bool
    dominated_by: Optional[str] = None
    dominates: List[str] = None


@dataclass
class FrontierAnalysis:
    """成本效益前沿分析结果"""
    frontier_strategies: List[str]
    dominated_strategies: List[str]
    frontier_points: List[Tuple[float, float]]  # (cost, qalys)
    efficiency_frontier: List[Dict]


class StrategyComparator:
    """多策略成本效益比较分析器"""
    
    def __init__(self, 
                 wtp_threshold: float = 150000,
                 reference_strategy: str = "no_screening"):
        """
        初始化策略比较器
        
        Args:
            wtp_threshold: 支付意愿阈值（元/QALY）
            reference_strategy: 参考策略名称
        """
        self.wtp_threshold = wtp_threshold
        self.reference_strategy = reference_strategy
        self.icer_calculator = ICERCalculator(reference_strategy)
        self.nhb_calculator = NHBCalculator(wtp_threshold)
        self.ceac_generator = CEACGenerator()
        
        logger.info(f"策略比较器初始化完成，WTP阈值: {wtp_threshold:,.0f}元/QALY")
    
    def compare_strategies(self, 
                          strategies: List[StrategyData],
                          include_uncertainty: bool = True) -> List[ComparisonResult]:
        """
        比较多个策略的成本效益
        
        Args:
            strategies: 策略数据列表
            include_uncertainty: 是否包含不确定性分析
            
        Returns:
            比较结果列表
        """
        logger.info(f"开始比较{len(strategies)}个策略")
        
        # 找到参考策略
        reference_data = None
        for strategy in strategies:
            if strategy.name == self.reference_strategy:
                reference_data = strategy
                break
        
        if not reference_data:
            logger.warning(f"未找到参考策略 {self.reference_strategy}，使用第一个策略作为参考")
            reference_data = strategies[0]
        
        results = []
        
        for strategy in strategies:
            # 计算ICER
            icer_result = self.icer_calculator.calculate_icer(
                strategy.cost, strategy.qalys,
                reference_data.cost, reference_data.qalys
            )
            
            # 计算NHB
            nhb = self.nhb_calculator.calculate_nhb(strategy.cost, strategy.qalys)
            
            # 创建比较结果
            result = ComparisonResult(
                strategy_name=strategy.name,
                total_cost=strategy.cost,
                total_qalys=strategy.qalys,
                incremental_cost=icer_result['incremental_cost'],
                incremental_qalys=icer_result['incremental_qalys'],
                icer=icer_result['icer'],
                icer_interpretation=icer_result['interpretation'],
                nhb=nhb,
                rank_by_icer=0,  # 稍后设置
                rank_by_nhb=0,   # 稍后设置
                is_on_frontier=False,  # 稍后设置
                dominates=[]
            )
            
            results.append(result)
        
        # 设置排名
        self._set_rankings(results)
        
        # 进行前沿分析
        frontier_analysis = self.analyze_efficiency_frontier(strategies)
        self._update_frontier_status(results, frontier_analysis)
        
        # 分析占优关系
        self._analyze_dominance_relationships(results)
        
        logger.info("策略比较完成")
        return results
    
    def _set_rankings(self, results: List[ComparisonResult]) -> None:
        """设置策略排名"""
        
        # 按ICER排名（占优策略排在前面）
        icer_sorted = sorted(results, key=lambda x: (
            0 if x.icer_interpretation == ICERInterpretation.DOMINANT else
            1 if x.icer_interpretation == ICERInterpretation.COST_EFFECTIVE else
            2 if x.icer_interpretation == ICERInterpretation.NOT_COST_EFFECTIVE else 3,
            x.icer if x.icer and x.icer != float('inf') else float('inf')
        ))
        
        for i, result in enumerate(icer_sorted):
            result.rank_by_icer = i + 1
        
        # 按NHB排名（降序）
        nhb_sorted = sorted(results, key=lambda x: x.nhb, reverse=True)
        for i, result in enumerate(nhb_sorted):
            result.rank_by_nhb = i + 1
    
    def analyze_efficiency_frontier(self, strategies: List[StrategyData]) -> FrontierAnalysis:
        """
        分析成本效益前沿
        
        Args:
            strategies: 策略数据列表
            
        Returns:
            前沿分析结果
        """
        logger.info("开始成本效益前沿分析")
        
        # 按成本排序
        sorted_strategies = sorted(strategies, key=lambda x: x.cost)
        
        frontier_strategies = []
        frontier_points = []
        dominated_strategies = []
        
        for i, strategy in enumerate(sorted_strategies):
            is_dominated = False
            
            # 检查是否被其他策略占优
            for other_strategy in sorted_strategies:
                if (other_strategy.cost <= strategy.cost and 
                    other_strategy.qalys >= strategy.qalys and
                    other_strategy.name != strategy.name):
                    # 严格占优：成本更低且效果更好，或成本相同但效果更好
                    if (other_strategy.cost < strategy.cost or 
                        other_strategy.qalys > strategy.qalys):
                        is_dominated = True
                        dominated_strategies.append(strategy.name)
                        break
            
            if not is_dominated:
                # 检查是否在效率前沿上
                if not frontier_strategies:
                    # 第一个非占优策略
                    frontier_strategies.append(strategy.name)
                    frontier_points.append((strategy.cost, strategy.qalys))
                else:
                    # 检查是否比前一个前沿策略更有效率
                    last_frontier_idx = len(frontier_strategies) - 1
                    last_frontier_strategy = None
                    
                    for s in sorted_strategies:
                        if s.name == frontier_strategies[last_frontier_idx]:
                            last_frontier_strategy = s
                            break
                    
                    if last_frontier_strategy:
                        # 计算增量成本效益比
                        incremental_cost = strategy.cost - last_frontier_strategy.cost
                        incremental_qalys = strategy.qalys - last_frontier_strategy.qalys
                        
                        if incremental_qalys > 0:  # 效果有改善
                            frontier_strategies.append(strategy.name)
                            frontier_points.append((strategy.cost, strategy.qalys))
        
        # 创建效率前沿详细信息
        efficiency_frontier = []
        for i, strategy_name in enumerate(frontier_strategies):
            strategy_data = next(s for s in strategies if s.name == strategy_name)
            
            frontier_info = {
                'strategy_name': strategy_name,
                'cost': strategy_data.cost,
                'qalys': strategy_data.qalys,
                'position_on_frontier': i + 1
            }
            
            # 计算与前一个前沿点的ICER
            if i > 0:
                prev_strategy_name = frontier_strategies[i-1]
                prev_strategy = next(s for s in strategies if s.name == prev_strategy_name)
                
                incremental_cost = strategy_data.cost - prev_strategy.cost
                incremental_qalys = strategy_data.qalys - prev_strategy.qalys
                
                if incremental_qalys > 0:
                    icer = incremental_cost / incremental_qalys
                    frontier_info['icer_vs_previous'] = icer
                    frontier_info['incremental_cost'] = incremental_cost
                    frontier_info['incremental_qalys'] = incremental_qalys
            
            efficiency_frontier.append(frontier_info)
        
        result = FrontierAnalysis(
            frontier_strategies=frontier_strategies,
            dominated_strategies=dominated_strategies,
            frontier_points=frontier_points,
            efficiency_frontier=efficiency_frontier
        )
        
        logger.info(f"前沿分析完成：{len(frontier_strategies)}个前沿策略，{len(dominated_strategies)}个被占优策略")
        return result
    
    def _update_frontier_status(self, 
                               results: List[ComparisonResult], 
                               frontier_analysis: FrontierAnalysis) -> None:
        """更新前沿状态"""
        for result in results:
            result.is_on_frontier = result.strategy_name in frontier_analysis.frontier_strategies
    
    def _analyze_dominance_relationships(self, results: List[ComparisonResult]) -> None:
        """分析占优关系"""
        for i, result_i in enumerate(results):
            for j, result_j in enumerate(results):
                if i != j:
                    # 检查result_i是否占优result_j
                    if (result_i.total_cost <= result_j.total_cost and 
                        result_i.total_qalys >= result_j.total_qalys):
                        # 严格占优
                        if (result_i.total_cost < result_j.total_cost or 
                            result_i.total_qalys > result_j.total_qalys):
                            result_i.dominates.append(result_j.strategy_name)
                            if not result_j.dominated_by:
                                result_j.dominated_by = result_i.strategy_name
    
    def rank_strategies(self, 
                       strategies: List[StrategyData],
                       ranking_method: StrategyRanking = StrategyRanking.BY_NHB) -> List[Dict]:
        """
        按指定方法排序策略
        
        Args:
            strategies: 策略数据列表
            ranking_method: 排序方法
            
        Returns:
            排序后的策略列表
        """
        logger.info(f"按{ranking_method.value}方法排序策略")
        
        if ranking_method == StrategyRanking.BY_NHB:
            return self.nhb_calculator.rank_strategies_by_nhb([
                {'name': s.name, 'cost': s.cost, 'qalys': s.qalys} 
                for s in strategies
            ])
        
        elif ranking_method == StrategyRanking.BY_COST:
            sorted_strategies = sorted(strategies, key=lambda x: x.cost)
            return [
                {
                    'strategy_name': s.name,
                    'cost': s.cost,
                    'qalys': s.qalys,
                    'rank': i + 1
                }
                for i, s in enumerate(sorted_strategies)
            ]
        
        elif ranking_method == StrategyRanking.BY_EFFECTIVENESS:
            sorted_strategies = sorted(strategies, key=lambda x: x.qalys, reverse=True)
            return [
                {
                    'strategy_name': s.name,
                    'cost': s.cost,
                    'qalys': s.qalys,
                    'rank': i + 1
                }
                for i, s in enumerate(sorted_strategies)
            ]
        
        elif ranking_method == StrategyRanking.BY_ICER:
            # 需要先计算ICER
            comparison_results = self.compare_strategies(strategies, include_uncertainty=False)
            sorted_results = sorted(comparison_results, key=lambda x: x.rank_by_icer)
            
            return [
                {
                    'strategy_name': r.strategy_name,
                    'cost': r.total_cost,
                    'qalys': r.total_qalys,
                    'icer': r.icer,
                    'rank': r.rank_by_icer
                }
                for r in sorted_results
            ]
        
        else:
            raise ValueError(f"不支持的排序方法: {ranking_method}")
    
    def filter_strategies(self, 
                         strategies: List[StrategyData],
                         criteria: Dict[str, Any]) -> List[StrategyData]:
        """
        根据条件筛选策略
        
        Args:
            strategies: 策略数据列表
            criteria: 筛选条件
                - max_cost: 最大成本
                - min_qalys: 最小QALY
                - max_icer: 最大ICER
                - only_cost_effective: 仅成本效益可接受的策略
                - only_frontier: 仅前沿策略
                
        Returns:
            筛选后的策略列表
        """
        logger.info(f"根据条件筛选策略: {criteria}")
        
        filtered_strategies = strategies.copy()
        
        # 成本筛选
        if 'max_cost' in criteria:
            max_cost = criteria['max_cost']
            filtered_strategies = [s for s in filtered_strategies if s.cost <= max_cost]
            logger.info(f"成本筛选后剩余{len(filtered_strategies)}个策略")
        
        # 效果筛选
        if 'min_qalys' in criteria:
            min_qalys = criteria['min_qalys']
            filtered_strategies = [s for s in filtered_strategies if s.qalys >= min_qalys]
            logger.info(f"效果筛选后剩余{len(filtered_strategies)}个策略")
        
        # ICER和成本效益筛选
        if 'max_icer' in criteria or 'only_cost_effective' in criteria:
            comparison_results = self.compare_strategies(filtered_strategies, include_uncertainty=False)
            
            if 'max_icer' in criteria:
                max_icer = criteria['max_icer']
                acceptable_strategies = [
                    r.strategy_name for r in comparison_results 
                    if r.icer is not None and r.icer <= max_icer
                ]
                filtered_strategies = [s for s in filtered_strategies if s.name in acceptable_strategies]
                logger.info(f"ICER筛选后剩余{len(filtered_strategies)}个策略")
            
            if criteria.get('only_cost_effective', False):
                cost_effective_strategies = [
                    r.strategy_name for r in comparison_results 
                    if r.icer_interpretation in [ICERInterpretation.DOMINANT, ICERInterpretation.COST_EFFECTIVE]
                ]
                filtered_strategies = [s for s in filtered_strategies if s.name in cost_effective_strategies]
                logger.info(f"成本效益筛选后剩余{len(filtered_strategies)}个策略")
        
        # 前沿策略筛选
        if criteria.get('only_frontier', False):
            frontier_analysis = self.analyze_efficiency_frontier(filtered_strategies)
            filtered_strategies = [s for s in filtered_strategies if s.name in frontier_analysis.frontier_strategies]
            logger.info(f"前沿筛选后剩余{len(filtered_strategies)}个策略")
        
        return filtered_strategies
    
    def prepare_visualization_data(self, 
                                  strategies: List[StrategyData],
                                  include_uncertainty: bool = True) -> Dict[str, Any]:
        """
        准备策略比较可视化数据
        
        Args:
            strategies: 策略数据列表
            include_uncertainty: 是否包含不确定性数据
            
        Returns:
            可视化数据字典
        """
        logger.info("准备策略比较可视化数据")
        
        # 基础比较数据
        comparison_results = self.compare_strategies(strategies, include_uncertainty)
        
        # 成本效益散点图数据
        scatter_data = {
            'strategies': [r.strategy_name for r in comparison_results],
            'costs': [r.total_cost for r in comparison_results],
            'qalys': [r.total_qalys for r in comparison_results],
            'is_frontier': [r.is_on_frontier for r in comparison_results],
            'icer_interpretation': [r.icer_interpretation.value for r in comparison_results]
        }
        
        # 前沿分析数据
        frontier_analysis = self.analyze_efficiency_frontier(strategies)
        
        # CEAC数据（如果有不确定性样本）
        ceac_data = None
        if include_uncertainty and all(s.cost_samples is not None and s.qaly_samples is not None for s in strategies):
            cost_samples = {s.name: s.cost_samples for s in strategies}
            qaly_samples = {s.name: s.qaly_samples for s in strategies}
            ceac_data = self.ceac_generator.generate_ceac(cost_samples, qaly_samples)
        
        # 排名数据
        nhb_ranking = self.rank_strategies(strategies, StrategyRanking.BY_NHB)
        cost_ranking = self.rank_strategies(strategies, StrategyRanking.BY_COST)
        effectiveness_ranking = self.rank_strategies(strategies, StrategyRanking.BY_EFFECTIVENESS)
        
        visualization_data = {
            'scatter_plot': scatter_data,
            'frontier_analysis': {
                'frontier_strategies': frontier_analysis.frontier_strategies,
                'frontier_points': frontier_analysis.frontier_points,
                'efficiency_frontier': frontier_analysis.efficiency_frontier
            },
            'rankings': {
                'by_nhb': nhb_ranking,
                'by_cost': cost_ranking,
                'by_effectiveness': effectiveness_ranking
            },
            'comparison_table': [
                {
                    'strategy': r.strategy_name,
                    'cost': r.total_cost,
                    'qalys': r.total_qalys,
                    'incremental_cost': r.incremental_cost,
                    'incremental_qalys': r.incremental_qalys,
                    'icer': r.icer,
                    'icer_interpretation': r.icer_interpretation.value,
                    'nhb': r.nhb,
                    'rank_icer': r.rank_by_icer,
                    'rank_nhb': r.rank_by_nhb,
                    'is_frontier': r.is_on_frontier
                }
                for r in comparison_results
            ]
        }
        
        if ceac_data:
            visualization_data['ceac'] = ceac_data
        
        logger.info("可视化数据准备完成")
        return visualization_data
    
    def generate_decision_support(self, 
                                 strategies: List[StrategyData],
                                 decision_criteria: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成策略选择决策支持信息
        
        Args:
            strategies: 策略数据列表
            decision_criteria: 决策标准
                - budget_constraint: 预算约束
                - effectiveness_threshold: 效果阈值
                - risk_tolerance: 风险容忍度
                
        Returns:
            决策支持信息
        """
        logger.info("生成策略选择决策支持信息")
        
        if decision_criteria is None:
            decision_criteria = {}
        
        # 基础分析
        comparison_results = self.compare_strategies(strategies, include_uncertainty=True)
        frontier_analysis = self.analyze_efficiency_frontier(strategies)
        
        # 推荐策略
        recommendations = []
        
        # 1. 占优策略推荐
        dominant_strategies = [
            r for r in comparison_results 
            if r.icer_interpretation == ICERInterpretation.DOMINANT
        ]
        
        if dominant_strategies:
            best_dominant = max(dominant_strategies, key=lambda x: x.nhb)
            recommendations.append({
                'type': 'dominant',
                'strategy': best_dominant.strategy_name,
                'reason': '该策略相比参考策略成本更低且效果更好，为占优策略',
                'priority': 1
            })
        
        # 2. 成本效益可接受策略推荐
        cost_effective_strategies = [
            r for r in comparison_results 
            if r.icer_interpretation == ICERInterpretation.COST_EFFECTIVE
        ]
        
        if cost_effective_strategies:
            best_ce = max(cost_effective_strategies, key=lambda x: x.nhb)
            recommendations.append({
                'type': 'cost_effective',
                'strategy': best_ce.strategy_name,
                'reason': f'该策略ICER为{best_ce.icer:,.0f}元/QALY，低于支付意愿阈值',
                'priority': 2
            })
        
        # 3. 预算约束下的推荐
        if 'budget_constraint' in decision_criteria:
            budget = decision_criteria['budget_constraint']
            affordable_strategies = [r for r in comparison_results if r.total_cost <= budget]
            
            if affordable_strategies:
                best_affordable = max(affordable_strategies, key=lambda x: x.nhb)
                recommendations.append({
                    'type': 'budget_constrained',
                    'strategy': best_affordable.strategy_name,
                    'reason': f'在预算约束{budget:,.0f}元下的最优选择',
                    'priority': 3
                })
        
        # 4. 前沿策略推荐
        frontier_strategies_results = [
            r for r in comparison_results 
            if r.strategy_name in frontier_analysis.frontier_strategies
        ]
        
        if frontier_strategies_results:
            best_frontier = max(frontier_strategies_results, key=lambda x: x.nhb)
            recommendations.append({
                'type': 'frontier',
                'strategy': best_frontier.strategy_name,
                'reason': '该策略位于成本效益前沿，具有最佳效率',
                'priority': 4
            })
        
        # 风险分析
        risk_analysis = {}
        if all(s.cost_samples is not None and s.qaly_samples is not None for s in strategies):
            for strategy in strategies:
                cost_cv = np.std(strategy.cost_samples) / np.mean(strategy.cost_samples)
                qaly_cv = np.std(strategy.qaly_samples) / np.mean(strategy.qaly_samples)
                
                risk_analysis[strategy.name] = {
                    'cost_uncertainty': cost_cv,
                    'effectiveness_uncertainty': qaly_cv,
                    'overall_risk': (cost_cv + qaly_cv) / 2
                }
        
        # 敏感性分析摘要
        sensitivity_summary = {
            'key_parameters': ['筛查成本', '筛查敏感性', '筛查特异性', '治疗成本', '生活质量权重'],
            'most_sensitive_outcome': 'ICER',
            'threshold_analysis': f'当支付意愿阈值低于{self.wtp_threshold * 0.8:,.0f}元/QALY时，推荐策略可能发生变化'
        }
        
        decision_support = {
            'recommendations': sorted(recommendations, key=lambda x: x['priority']),
            'frontier_analysis': {
                'frontier_strategies': frontier_analysis.frontier_strategies,
                'dominated_strategies': frontier_analysis.dominated_strategies,
                'efficiency_summary': f'{len(frontier_analysis.frontier_strategies)}个策略位于效率前沿'
            },
            'risk_analysis': risk_analysis,
            'sensitivity_summary': sensitivity_summary,
            'decision_matrix': [
                {
                    'strategy': r.strategy_name,
                    'cost': r.total_cost,
                    'effectiveness': r.total_qalys,
                    'cost_effectiveness': 'Yes' if r.icer_interpretation in [
                        ICERInterpretation.DOMINANT, ICERInterpretation.COST_EFFECTIVE
                    ] else 'No',
                    'on_frontier': 'Yes' if r.is_on_frontier else 'No',
                    'overall_score': r.nhb
                }
                for r in comparison_results
            ]
        }
        
        logger.info(f"决策支持信息生成完成，提供{len(recommendations)}个推荐")
        return decision_support


def create_example_strategies() -> List[StrategyData]:
    """创建示例策略数据用于测试"""
    
    strategies = [
        StrategyData(
            name="no_screening",
            cost=0,
            qalys=15.2,
            description="无筛查策略"
        ),
        StrategyData(
            name="annual_fobt",
            cost=2500,
            qalys=15.8,
            description="年度粪便隐血检测"
        ),
        StrategyData(
            name="biennial_fobt",
            cost=1800,
            qalys=15.6,
            description="两年一次粪便隐血检测"
        ),
        StrategyData(
            name="colonoscopy_10y",
            cost=8500,
            qalys=16.1,
            description="10年一次结肠镜检查"
        ),
        StrategyData(
            name="flexible_sigmoidoscopy_5y",
            cost=4200,
            qalys=15.9,
            description="5年一次乙状结肠镜检查"
        )
    ]
    
    # 添加不确定性样本（示例）
    np.random.seed(42)
    for strategy in strategies:
        n_samples = 1000
        strategy.cost_samples = np.random.normal(strategy.cost, strategy.cost * 0.2, n_samples)
        strategy.qaly_samples = np.random.normal(strategy.qalys, strategy.qalys * 0.1, n_samples)
        
        # 确保非负值
        strategy.cost_samples = np.maximum(strategy.cost_samples, 0)
        strategy.qaly_samples = np.maximum(strategy.qaly_samples, 0)
    
    return strategies


if __name__ == "__main__":
    # 示例使用
    logging.basicConfig(level=logging.INFO)
    
    # 创建示例数据
    strategies = create_example_strategies()
    
    # 初始化比较器
    comparator = StrategyComparator(wtp_threshold=150000)
    
    # 比较策略
    results = comparator.compare_strategies(strategies)
    
    print("策略比较结果:")
    for result in results:
        print(f"{result.strategy_name}: ICER={result.icer}, NHB={result.nhb:.3f}, 前沿={result.is_on_frontier}")
    
    # 生成决策支持
    decision_support = comparator.generate_decision_support(strategies)
    print(f"\n推荐策略: {decision_support['recommendations'][0]['strategy']}")