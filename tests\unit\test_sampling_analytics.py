"""
抽样分析模块单元测试
"""

import unittest
import numpy as np
from unittest.mock import patch, MagicMock
import tempfile
import shutil

# 尝试导入matplotlib，如果失败则跳过相关测试
try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False
    plt = None
"""
抽样分析模块单元测试
"""


from src.calibration.parameter_sampler import (
    ParameterDefinition, SamplingConfig, SamplingResult, LatinHypercubeSampler
)
from src.calibration.sampling_analytics import (
    QualityThresholds, SamplingAnalyzer
)


class TestQualityThresholds(unittest.TestCase):
    """质量阈值配置测试类"""
    
    def test_quality_thresholds_default(self):
        """测试默认质量阈值"""
        thresholds = QualityThresholds()
        
        self.assertEqual(thresholds.min_distance, 0.01)
        self.assertEqual(thresholds.max_correlation, 0.1)
        self.assertEqual(thresholds.min_coverage, 0.9)
        self.assertEqual(thresholds.uniformity_pvalue, 0.05)
    
    def test_quality_thresholds_custom(self):
        """测试自定义质量阈值"""
        thresholds = QualityThresholds(
            min_distance=0.02,
            max_correlation=0.15,
            min_coverage=0.85,
            uniformity_pvalue=0.01
        )
        
        self.assertEqual(thresholds.min_distance, 0.02)
        self.assertEqual(thresholds.max_correlation, 0.15)
        self.assertEqual(thresholds.min_coverage, 0.85)
        self.assertEqual(thresholds.uniformity_pvalue, 0.01)


class TestSamplingAnalyzer(unittest.TestCase):
    """抽样质量分析器测试类"""
    
    def setUp(self):
        """设置测试数据"""
        # 创建测试参数
        self.params = [
            ParameterDefinition("param1", 0.0, 1.0),
            ParameterDefinition("param2", -1.0, 1.0),
            ParameterDefinition("param3", 0.1, 10.0)
        ]
        
        self.config = SamplingConfig(
            parameters=self.params,
            n_samples=100,
            random_seed=42
        )
        
        # 生成测试抽样结果
        sampler = LatinHypercubeSampler(self.config)
        self.sampling_result = sampler.generate_samples()
        
        # 创建分析器
        self.analyzer = SamplingAnalyzer()
    
    def test_analyzer_initialization(self):
        """测试分析器初始化"""
        self.assertIsInstance(self.analyzer.quality_thresholds, QualityThresholds)
        
        # 测试自定义阈值初始化
        custom_thresholds = QualityThresholds(min_distance=0.02)
        custom_analyzer = SamplingAnalyzer(custom_thresholds)
        self.assertEqual(custom_analyzer.quality_thresholds.min_distance, 0.02)
    
    def test_analyze_sampling_quality(self):
        """测试抽样质量分析"""
        analysis = self.analyzer.analyze_sampling_quality(self.sampling_result)
        
        # 检查分析结果结构
        required_keys = [
            'overall_quality', 'dimension_analysis', 'correlation_analysis',
            'space_filling', 'uniformity_tests', 'recommendations'
        ]
        
        for key in required_keys:
            self.assertIn(key, analysis)
        
        # 检查总体质量结构
        overall_quality = analysis['overall_quality']
        quality_keys = [
            'overall_score', 'distance_score', 'correlation_score',
            'coverage_score', 'quality_grade', 'performance_metrics'
        ]
        
        for key in quality_keys:
            self.assertIn(key, overall_quality)
        
        # 检查分数范围
        self.assertGreaterEqual(overall_quality['overall_score'], 0.0)
        self.assertLessEqual(overall_quality['overall_score'], 1.0)
    
    def test_calculate_overall_quality(self):
        """测试总体质量计算"""
        overall_quality = self.analyzer._calculate_overall_quality(self.sampling_result)
        
        # 检查所有分数都在[0,1]范围内
        self.assertGreaterEqual(overall_quality['overall_score'], 0.0)
        self.assertLessEqual(overall_quality['overall_score'], 1.0)
        self.assertGreaterEqual(overall_quality['distance_score'], 0.0)
        self.assertLessEqual(overall_quality['distance_score'], 1.0)
        self.assertGreaterEqual(overall_quality['correlation_score'], 0.0)
        self.assertLessEqual(overall_quality['correlation_score'], 1.0)
        self.assertGreaterEqual(overall_quality['coverage_score'], 0.0)
        self.assertLessEqual(overall_quality['coverage_score'], 1.0)
        
        # 检查质量等级
        valid_grades = ["优秀", "良好", "中等", "及格", "不及格"]
        self.assertIn(overall_quality['quality_grade'], valid_grades)
        
        # 检查性能指标
        perf_metrics = overall_quality['performance_metrics']
        self.assertIn('generation_time', perf_metrics)
        self.assertIn('samples_per_second', perf_metrics)
        self.assertIn('memory_efficiency', perf_metrics)
    
    def test_get_quality_grade(self):
        """测试质量等级评定"""
        test_cases = [
            (0.95, "优秀"),
            (0.85, "良好"),
            (0.75, "中等"),
            (0.65, "及格"),
            (0.45, "不及格")
        ]
        
        for score, expected_grade in test_cases:
            grade = self.analyzer._get_quality_grade(score)
            self.assertEqual(grade, expected_grade)
    
    def test_estimate_memory_usage(self):
        """测试内存使用估算"""
        memory_info = self.analyzer._estimate_memory_usage(self.sampling_result)
        
        required_keys = ['samples_size_mb', 'estimated_total_mb', 'memory_per_sample_kb']
        for key in required_keys:
            self.assertIn(key, memory_info)
            self.assertGreater(memory_info[key], 0)
        
        # 检查估算的合理性
        self.assertGreater(memory_info['estimated_total_mb'], memory_info['samples_size_mb'])
    
    def test_analyze_dimensions(self):
        """测试维度分析"""
        dimension_analysis = self.analyzer._analyze_dimensions(self.sampling_result)
        
        # 应该有3个参数的分析结果
        self.assertEqual(len(dimension_analysis), 3)
        
        for param_name in self.sampling_result.parameter_names:
            self.assertIn(param_name, dimension_analysis)
            
            param_analysis = dimension_analysis[param_name]
            
            # 检查统计信息
            self.assertIn('statistics', param_analysis)
            stats = param_analysis['statistics']
            required_stats = ['mean', 'std', 'min', 'max', 'median', 'skewness', 'kurtosis']
            for stat in required_stats:
                self.assertIn(stat, stats)
                self.assertIsInstance(stats[stat], float)
            
            # 检查分布检验
            self.assertIn('distribution_tests', param_analysis)
            
            # 检查覆盖度分析
            self.assertIn('coverage', param_analysis)
    
    def test_test_distribution(self):
        """测试分布检验"""
        # 创建均匀分布样本
        uniform_samples = np.random.uniform(0, 1, 1000)
        
        tests = self.analyzer._test_distribution(uniform_samples)
        
        # 检查KS检验结果
        self.assertIn('ks_uniform', tests)
        ks_result = tests['ks_uniform']
        self.assertIn('statistic', ks_result)
        self.assertIn('pvalue', ks_result)
        self.assertIn('is_uniform', ks_result)
        
        # 检查Anderson-Darling均匀分布检验结果
        self.assertIn('anderson_uniform', tests)
        ad_result = tests['anderson_uniform']
        self.assertIn('statistic', ad_result)
        self.assertIn('pvalue', ad_result)
        self.assertIn('is_uniform', ad_result)
    
    def test_analyze_coverage(self):
        """测试覆盖度分析"""
        # 创建测试样本
        test_samples = np.random.uniform(0, 1, 200)
        
        coverage_analysis = self.analyzer._analyze_coverage(test_samples, n_bins=10)
        
        required_keys = [
            'bin_counts', 'expected_count', 'max_deviation', 'mean_deviation',
            'coverage_uniformity', 'empty_bins', 'coverage_ratio'
        ]
        
        for key in required_keys:
            self.assertIn(key, coverage_analysis)
        
        # 检查分箱计数
        self.assertEqual(len(coverage_analysis['bin_counts']), 10)
        self.assertEqual(coverage_analysis['expected_count'], 20.0)  # 200/10
        
        # 检查覆盖度比例
        self.assertGreaterEqual(coverage_analysis['coverage_ratio'], 0.0)
        self.assertLessEqual(coverage_analysis['coverage_ratio'], 1.0)
    
    def test_analyze_correlations(self):
        """测试相关性分析"""
        correlation_analysis = self.analyzer._analyze_correlations(self.sampling_result)
        
        required_keys = [
            'correlation_matrix', 'max_correlation', 'mean_correlation',
            'std_correlation', 'high_correlation_pairs', 'correlation_distribution'
        ]
        
        for key in required_keys:
            self.assertIn(key, correlation_analysis)
        
        # 检查相关性矩阵
        corr_matrix = correlation_analysis['correlation_matrix']
        n_params = len(self.sampling_result.parameter_names)
        self.assertEqual(len(corr_matrix), n_params)
        self.assertEqual(len(corr_matrix[0]), n_params)
        
        # 检查相关性统计
        self.assertGreaterEqual(correlation_analysis['max_correlation'], 0.0)
        self.assertLessEqual(correlation_analysis['max_correlation'], 1.0)
        self.assertGreaterEqual(correlation_analysis['mean_correlation'], 0.0)
        self.assertLessEqual(correlation_analysis['mean_correlation'], 1.0)
    
    def test_find_high_correlation_pairs(self):
        """测试高相关性参数对查找"""
        # 创建有高相关性的测试矩阵
        test_matrix = np.array([
            [1.0, 0.15, 0.05],
            [0.15, 1.0, 0.02],
            [0.05, 0.02, 1.0]
        ])
        
        param_names = ["param1", "param2", "param3"]
        
        high_corr_pairs = self.analyzer._find_high_correlation_pairs(
            test_matrix, param_names, threshold=0.1
        )
        
        # 应该找到一个高相关性对 (param1, param2)
        self.assertEqual(len(high_corr_pairs), 1)
        self.assertEqual(high_corr_pairs[0]['param1'], 'param1')
        self.assertEqual(high_corr_pairs[0]['param2'], 'param2')
        self.assertEqual(high_corr_pairs[0]['correlation'], 0.15)
    
    def test_analyze_space_filling(self):
        """测试空间填充分析"""
        space_filling = self.analyzer._analyze_space_filling(self.sampling_result)
        
        required_keys = [
            'min_distance', 'max_distance', 'mean_distance', 'std_distance',
            'distance_percentiles', 'space_filling_efficiency', 'nearest_neighbor_analysis'
        ]
        
        for key in required_keys:
            self.assertIn(key, space_filling)
        
        # 检查距离统计
        self.assertGreater(space_filling['min_distance'], 0)
        self.assertGreater(space_filling['max_distance'], space_filling['min_distance'])
        self.assertGreater(space_filling['mean_distance'], space_filling['min_distance'])
        
        # 检查百分位数
        percentiles = space_filling['distance_percentiles']
        required_percentiles = ['5th', '25th', '50th', '75th', '95th']
        for p in required_percentiles:
            self.assertIn(p, percentiles)
        
        # 检查空间填充效率
        self.assertGreaterEqual(space_filling['space_filling_efficiency'], 0.0)
        self.assertLessEqual(space_filling['space_filling_efficiency'], 1.0)
    
    def test_calculate_space_filling_efficiency(self):
        """测试空间填充效率计算"""
        # 创建简单的2D测试样本
        test_samples = np.array([
            [0.1, 0.1],
            [0.9, 0.1],
            [0.1, 0.9],
            [0.9, 0.9]
        ])
        
        efficiency = self.analyzer._calculate_space_filling_efficiency(test_samples)
        
        self.assertGreaterEqual(efficiency, 0.0)
        self.assertLessEqual(efficiency, 1.0)
    
    def test_analyze_nearest_neighbors(self):
        """测试最近邻分析"""
        # 创建简单的距离矩阵
        distance_matrix = np.array([
            [0.0, 1.0, 2.0],
            [1.0, 0.0, 1.5],
            [2.0, 1.5, 0.0]
        ])
        
        nn_analysis = self.analyzer._analyze_nearest_neighbors(distance_matrix)
        
        required_keys = [
            'mean_nearest_distance', 'std_nearest_distance',
            'min_nearest_distance', 'max_nearest_distance', 'nearest_distance_cv'
        ]
        
        for key in required_keys:
            self.assertIn(key, nn_analysis)
            self.assertIsInstance(nn_analysis[key], float)
    
    def test_perform_uniformity_tests(self):
        """测试均匀性检验"""
        uniformity_results = self.analyzer._perform_uniformity_tests(self.sampling_result)
        
        # 检查每个参数的检验结果
        for param_name in self.sampling_result.parameter_names:
            self.assertIn(param_name, uniformity_results)
            
            param_tests = uniformity_results[param_name]
            self.assertIn('ks_test', param_tests)
            self.assertIn('chi2_test', param_tests)
            
            # 检查KS检验结果
            ks_test = param_tests['ks_test']
            self.assertIn('statistic', ks_test)
            self.assertIn('pvalue', ks_test)
            self.assertIn('is_uniform', ks_test)
        
        # 检查多维检验结果
        self.assertIn('multivariate', uniformity_results)
        multivariate_test = uniformity_results['multivariate']
        self.assertIn('theoretical_mean_distance', multivariate_test)
        self.assertIn('actual_mean_distance', multivariate_test)
        self.assertIn('is_uniform_multivariate', multivariate_test)
    
    def test_chi_square_uniformity_test(self):
        """测试Chi-square均匀性检验"""
        # 创建均匀分布样本
        uniform_samples = np.random.uniform(0, 1, 1000)
        
        chi2_result = self.analyzer._chi_square_uniformity_test(uniform_samples, n_bins=10)
        
        required_keys = [
            'statistic', 'pvalue', 'degrees_of_freedom', 'is_uniform',
            'observed_frequencies', 'expected_frequency'
        ]
        
        for key in required_keys:
            self.assertIn(key, chi2_result)
        
        # 检查自由度
        self.assertEqual(chi2_result['degrees_of_freedom'], 9)  # n_bins - 1
        
        # 检查期望频数
        self.assertEqual(chi2_result['expected_frequency'], 100.0)  # 1000/10
        
        # 检查观察频数
        self.assertEqual(len(chi2_result['observed_frequencies']), 10)
    
    def test_multivariate_uniformity_test(self):
        """测试多维均匀性检验"""
        multivariate_test = self.analyzer._multivariate_uniformity_test(self.sampling_result.samples)
        
        required_keys = [
            'theoretical_mean_distance', 'actual_mean_distance',
            'distance_deviation', 'normalized_deviation', 'is_uniform_multivariate'
        ]
        
        for key in required_keys:
            self.assertIn(key, multivariate_test)
            self.assertIsInstance(multivariate_test[key], (float, bool))
        
        # 检查理论距离为正数
        self.assertGreater(multivariate_test['theoretical_mean_distance'], 0)
        self.assertGreater(multivariate_test['actual_mean_distance'], 0)
    
    def test_generate_recommendations(self):
        """测试生成改进建议"""
        recommendations = self.analyzer._generate_recommendations(self.sampling_result)
        
        self.assertIsInstance(recommendations, list)
        
        # 每个建议都应该是字符串
        for recommendation in recommendations:
            self.assertIsInstance(recommendation, str)
            self.assertGreater(len(recommendation), 0)
    
    def test_generate_quality_report(self):
        """测试生成质量报告"""
        report = self.analyzer.generate_quality_report(self.sampling_result)
        
        self.assertIsInstance(report, str)
        self.assertGreater(len(report), 0)
        
        # 检查报告包含关键信息
        self.assertIn("参数抽样质量报告", report)
        self.assertIn("抽样配置", report)
        self.assertIn("总体质量评估", report)
        self.assertIn("关键指标", report)
        self.assertIn("改进建议", report)
    
    def test_recommendations_based_on_quality(self):
        """测试基于质量的建议生成"""
        # 创建质量较差的抽样结果
        poor_quality_metrics = {
            'min_distance': 0.005,  # 低于阈值
            'max_correlation': 0.15,  # 高于阈值
            'min_coverage': 0.8,  # 低于阈值
            'mean_distance': 0.1,
            'mean_correlation': 0.05,
            'mean_coverage': 0.85
        }
        
        poor_result = SamplingResult(
            samples=self.sampling_result.samples,
            parameter_names=self.sampling_result.parameter_names,
            config=self.sampling_result.config,
            quality_metrics=poor_quality_metrics,
            generation_time=65.0,  # 超过60秒
            hash_signature="test_hash"
        )
        
        recommendations = self.analyzer._generate_recommendations(poor_result)
        
        # 应该有多个建议
        self.assertGreater(len(recommendations), 0)
        
        # 检查是否包含预期的建议类型
        recommendation_text = " ".join(recommendations)
        self.assertIn("最小距离", recommendation_text)
        self.assertIn("相关性", recommendation_text)
        self.assertIn("覆盖度", recommendation_text)
        self.assertIn("生成时间", recommendation_text)


class TestEdgeCases(unittest.TestCase):
    """边界情况测试类"""
    
    def setUp(self):
        """设置测试数据"""
        self.analyzer = SamplingAnalyzer()
    
    def test_single_parameter_analysis(self):
        """测试单参数分析"""
        # 创建单参数抽样结果
        single_param = [ParameterDefinition("param1", 0.0, 1.0)]
        single_config = SamplingConfig(single_param, 50, 42)
        single_sampler = LatinHypercubeSampler(single_config)
        single_result = single_sampler.generate_samples()
        
        analysis = self.analyzer.analyze_sampling_quality(single_result)
        
        # 应该能正常分析单参数
        self.assertIn('overall_quality', analysis)
        self.assertIn('dimension_analysis', analysis)
        self.assertEqual(len(analysis['dimension_analysis']), 1)
    
    def test_small_sample_analysis(self):
        """测试小样本分析"""
        # 创建小样本抽样结果
        small_params = [
            ParameterDefinition("param1", 0.0, 1.0),
            ParameterDefinition("param2", 0.0, 1.0)
        ]
        small_config = SamplingConfig(small_params, 10, 42)  # 只有10个样本
        small_sampler = LatinHypercubeSampler(small_config)
        small_result = small_sampler.generate_samples()
        
        analysis = self.analyzer.analyze_sampling_quality(small_result)
        
        # 应该能处理小样本
        self.assertIn('overall_quality', analysis)
        self.assertIn('recommendations', analysis)
        
        # 应该有关于样本数量的建议
        recommendations = analysis['recommendations']
        recommendation_text = " ".join(recommendations)
        self.assertIn("样本数量", recommendation_text)
    
    def test_perfect_samples_analysis(self):
        """测试理想样本分析"""
        # 创建理想的网格样本
        x = np.linspace(0, 1, 10)
        y = np.linspace(0, 1, 10)
        xx, yy = np.meshgrid(x, y)
        perfect_samples = np.column_stack([xx.ravel(), yy.ravel()])
        
        # 创建模拟的抽样结果
        params = [
            ParameterDefinition("param1", 0.0, 1.0),
            ParameterDefinition("param2", 0.0, 1.0)
        ]
        config = SamplingConfig(params, 100, 42)
        
        # 计算质量指标
        from scipy.spatial.distance import pdist
        distances = pdist(perfect_samples)
        correlation_matrix = np.corrcoef(perfect_samples.T)
        off_diagonal = correlation_matrix[np.triu_indices_from(correlation_matrix, k=1)]
        
        perfect_metrics = {
            'min_distance': float(np.min(distances)),
            'mean_distance': float(np.mean(distances)),
            'max_correlation': float(np.max(np.abs(off_diagonal))),
            'mean_correlation': float(np.mean(np.abs(off_diagonal))),
            'min_coverage': 0.95,
            'mean_coverage': 0.98
        }
        
        perfect_result = SamplingResult(
            samples=perfect_samples,
            parameter_names=["param1", "param2"],
            config=config,
            quality_metrics=perfect_metrics,
            generation_time=1.0,
            hash_signature="perfect_hash"
        )
        
        analysis = self.analyzer.analyze_sampling_quality(perfect_result)
        
        # 理想样本应该有高质量评分
        overall_quality = analysis['overall_quality']
        self.assertGreater(overall_quality['overall_score'], 0.7)
    
    def test_degenerate_samples_analysis(self):
        """测试退化样本分析"""
        # 创建所有样本都相同的退化情况
        degenerate_samples = np.ones((50, 2)) * 0.5  # 所有样本都是(0.5, 0.5)
        
        params = [
            ParameterDefinition("param1", 0.0, 1.0),
            ParameterDefinition("param2", 0.0, 1.0)
        ]
        config = SamplingConfig(params, 50, 42)
        
        # 退化样本的质量指标
        degenerate_metrics = {
            'min_distance': 0.0,  # 所有点重合
            'mean_distance': 0.0,
            'max_correlation': 0.0,  # 无变化，相关性未定义
            'mean_correlation': 0.0,
            'min_coverage': 0.0,  # 覆盖度极差
            'mean_coverage': 0.0
        }
        
        degenerate_result = SamplingResult(
            samples=degenerate_samples,
            parameter_names=["param1", "param2"],
            config=config,
            quality_metrics=degenerate_metrics,
            generation_time=1.0,
            hash_signature="degenerate_hash"
        )
        
        # 应该能处理退化情况而不崩溃
        try:
            analysis = self.analyzer.analyze_sampling_quality(degenerate_result)
            self.assertIn('overall_quality', analysis)
            
            # 退化样本应该有很低的质量评分
            overall_quality = analysis['overall_quality']
            self.assertLessEqual(overall_quality['overall_score'], 0.3)
            
        except Exception as e:
            self.fail(f"退化样本分析失败: {e}")


if __name__ == '__main__':
    # 关闭matplotlib的交互模式以避免测试时弹出窗口
    if HAS_MATPLOTLIB:
        plt.ioff()
    unittest.main()
