"""
抽样分析模块
实现抽样统计分析和可视化功能
"""

from typing import Dict, List, Optional, Any, Tuple
import numpy as np
from scipy import stats
from scipy.stats import kstest, shapiro, anderson
from dataclasses import dataclass
import warnings

# 可选依赖
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    import pandas as pd
    HAS_PLOTTING = True
except ImportError:
    HAS_PLOTTING = False
    plt = None
    sns = None
    pd = None

from .parameter_sampler import SamplingResult, SamplingConfig


@dataclass
class QualityThresholds:
    """质量阈值配置"""
    min_distance: float = 0.01      # 最小距离阈值
    max_correlation: float = 0.1    # 最大相关性阈值
    min_coverage: float = 0.9       # 最小覆盖度阈值
    uniformity_pvalue: float = 0.05 # 均匀性检验p值阈值


class SamplingAnalyzer:
    """抽样质量分析器"""
    
    def __init__(self, quality_thresholds: Optional[QualityThresholds] = None):
        """
        初始化分析器
        
        Args:
            quality_thresholds: 质量阈值配置
        """
        self.quality_thresholds = quality_thresholds or QualityThresholds()
        
        # 设置中文字体（如果matplotlib可用）
        if HAS_PLOTTING:
            plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
    
    def analyze_sampling_quality(self, sampling_result: SamplingResult) -> Dict[str, Any]:
        """
        分析抽样质量
        
        Args:
            sampling_result: 抽样结果
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        analysis = {
            'overall_quality': self._calculate_overall_quality(sampling_result),
            'dimension_analysis': self._analyze_dimensions(sampling_result),
            'correlation_analysis': self._analyze_correlations(sampling_result),
            'space_filling': self._analyze_space_filling(sampling_result),
            'uniformity_tests': self._perform_uniformity_tests(sampling_result),
            'recommendations': self._generate_recommendations(sampling_result)
        }
        
        return analysis
    
    def _calculate_overall_quality(self, result: SamplingResult) -> Dict[str, Any]:
        """
        计算总体质量评分
        
        Args:
            result: 抽样结果
            
        Returns:
            Dict[str, Any]: 总体质量评分
        """
        metrics = result.quality_metrics
        
        # 归一化各项指标到[0,1]区间
        distance_score = min(
            metrics['min_distance'] / self.quality_thresholds.min_distance, 1.0
        )
        correlation_score = max(
            0, 1 - metrics['max_correlation'] / self.quality_thresholds.max_correlation
        )
        coverage_score = metrics['min_coverage']
        
        # 加权平均
        overall_score = (distance_score * 0.4 + correlation_score * 0.3 + coverage_score * 0.3)
        
        return {
            'overall_score': overall_score,
            'distance_score': distance_score,
            'correlation_score': correlation_score,
            'coverage_score': coverage_score,
            'quality_grade': self._get_quality_grade(overall_score),
            'performance_metrics': {
                'generation_time': result.generation_time,
                'samples_per_second': result.config.n_samples / result.generation_time,
                'memory_efficiency': self._estimate_memory_usage(result)
            }
        }
    
    def _get_quality_grade(self, score: float) -> str:
        """
        根据分数给出质量等级
        
        Args:
            score: 质量分数
            
        Returns:
            str: 质量等级
        """
        if score >= 0.9:
            return "优秀"
        elif score >= 0.8:
            return "良好"
        elif score >= 0.7:
            return "中等"
        elif score >= 0.6:
            return "及格"
        else:
            return "不及格"
    
    def _estimate_memory_usage(self, result: SamplingResult) -> Dict[str, float]:
        """
        估算内存使用情况
        
        Args:
            result: 抽样结果
            
        Returns:
            Dict[str, float]: 内存使用估算
        """
        samples_size_mb = result.samples.nbytes / (1024 * 1024)
        
        return {
            'samples_size_mb': samples_size_mb,
            'estimated_total_mb': samples_size_mb * 1.5,  # 考虑其他数据结构
            'memory_per_sample_kb': (samples_size_mb * 1024) / result.config.n_samples
        }
    
    def _analyze_dimensions(self, result: SamplingResult) -> Dict[str, Any]:
        """
        分析各维度的抽样质量
        
        Args:
            result: 抽样结果
            
        Returns:
            Dict[str, Any]: 维度分析结果
        """
        samples = result.samples
        dimension_analysis = {}
        
        for i, param_name in enumerate(result.parameter_names):
            param_samples = samples[:, i]
            
            # 基本统计信息
            stats_info = {
                'mean': float(np.mean(param_samples)),
                'std': float(np.std(param_samples)),
                'min': float(np.min(param_samples)),
                'max': float(np.max(param_samples)),
                'median': float(np.median(param_samples)),
                'skewness': float(stats.skew(param_samples)),
                'kurtosis': float(stats.kurtosis(param_samples))
            }
            
            # 分布检验
            distribution_tests = self._test_distribution(param_samples)
            
            # 覆盖度分析
            coverage_analysis = self._analyze_coverage(param_samples)
            
            dimension_analysis[param_name] = {
                'statistics': stats_info,
                'distribution_tests': distribution_tests,
                'coverage': coverage_analysis
            }
        
        return dimension_analysis
    
    def _test_distribution(self, samples: np.ndarray) -> Dict[str, Any]:
        """
        测试样本分布
        
        Args:
            samples: 样本数据
            
        Returns:
            Dict[str, Any]: 分布检验结果
        """
        tests = {}
        
        # Kolmogorov-Smirnov检验（均匀分布）
        ks_stat, ks_pvalue = kstest(samples, 'uniform')
        tests['ks_uniform'] = {
            'statistic': float(ks_stat),
            'pvalue': float(ks_pvalue),
            'is_uniform': bool(ks_pvalue > self.quality_thresholds.uniformity_pvalue)
        }

        # Shapiro-Wilk检验（正态分布）
        if len(samples) <= 5000:  # Shapiro-Wilk对大样本不适用
            sw_stat, sw_pvalue = shapiro(samples)
            tests['shapiro_normal'] = {
                'statistic': float(sw_stat),
                'pvalue': float(sw_pvalue),
                'is_normal': bool(sw_pvalue > self.quality_thresholds.uniformity_pvalue)
            }
        
        # Anderson-Darling检验（正态分布）
        try:
            ad_result = anderson(samples, dist='norm')
            tests['anderson_normal'] = {
                'statistic': float(ad_result.statistic),
                'critical_values': ad_result.critical_values.tolist(),
                'significance_levels': ad_result.significance_level.tolist()
            }
        except Exception as e:
            # 如果Anderson-Darling检验失败，跳过
            tests['anderson_normal'] = {
                'statistic': 0.0,
                'critical_values': [],
                'significance_levels': [],
                'error': str(e)
            }

        # 添加Anderson-Darling均匀分布检验（手动实现）
        try:
            # 将样本转换为[0,1]区间
            normalized_samples = (samples - np.min(samples)) / (np.max(samples) - np.min(samples))
            # 使用KS检验作为替代
            ks_uniform_stat, ks_uniform_pvalue = kstest(normalized_samples, 'uniform')
            tests['anderson_uniform'] = {
                'statistic': float(ks_uniform_stat),
                'pvalue': float(ks_uniform_pvalue),
                'is_uniform': bool(ks_uniform_pvalue > self.quality_thresholds.uniformity_pvalue)
            }
        except Exception as e:
            tests['anderson_uniform'] = {
                'statistic': 0.0,
                'pvalue': 0.0,
                'is_uniform': False,
                'error': str(e)
            }
        
        return tests
    
    def _analyze_coverage(self, samples: np.ndarray, n_bins: int = 20) -> Dict[str, Any]:
        """
        分析覆盖度
        
        Args:
            samples: 样本数据
            n_bins: 分箱数量
            
        Returns:
            Dict[str, Any]: 覆盖度分析结果
        """
        # 计算直方图
        hist, bin_edges = np.histogram(samples, bins=n_bins)
        expected_count = len(samples) / n_bins
        
        # 计算覆盖度指标
        coverage_metrics = {
            'bin_counts': hist.tolist(),
            'expected_count': expected_count,
            'max_deviation': float(np.max(np.abs(hist - expected_count))),
            'mean_deviation': float(np.mean(np.abs(hist - expected_count))),
            'coverage_uniformity': 1.0 - (np.std(hist) / expected_count),
            'empty_bins': int(np.sum(hist == 0)),
            'coverage_ratio': float((n_bins - np.sum(hist == 0)) / n_bins)
        }
        
        return coverage_metrics
    
    def _analyze_correlations(self, result: SamplingResult) -> Dict[str, Any]:
        """
        分析参数间相关性

        Args:
            result: 抽样结果

        Returns:
            Dict[str, Any]: 相关性分析结果
        """
        samples = result.samples

        # 处理单参数情况
        if samples.ndim == 1 or samples.shape[1] == 1:
            return {
                'correlation_matrix': [[1.0]],
                'max_correlation': 0.0,
                'mean_correlation': 0.0,
                'std_correlation': 0.0,
                'high_correlation_pairs': [],
                'correlation_summary': '单参数无相关性分析'
            }

        correlation_matrix = np.corrcoef(samples.T)

        # 确保correlation_matrix是2D数组
        if correlation_matrix.ndim == 0:
            correlation_matrix = np.array([[1.0]])
        elif correlation_matrix.ndim == 1:
            correlation_matrix = correlation_matrix.reshape(1, 1)

        # 提取上三角矩阵（排除对角线）
        upper_triangle = np.triu(correlation_matrix, k=1)
        off_diagonal_correlations = upper_triangle[upper_triangle != 0]
        
        correlation_analysis = {
            'correlation_matrix': correlation_matrix.tolist(),
            'max_correlation': float(np.max(np.abs(off_diagonal_correlations))),
            'mean_correlation': float(np.mean(np.abs(off_diagonal_correlations))),
            'std_correlation': float(np.std(off_diagonal_correlations)),
            'high_correlation_pairs': self._find_high_correlation_pairs(
                correlation_matrix, result.parameter_names
            ),
            'correlation_distribution': {
                'percentiles': {
                    '25th': float(np.percentile(np.abs(off_diagonal_correlations), 25)),
                    '50th': float(np.percentile(np.abs(off_diagonal_correlations), 50)),
                    '75th': float(np.percentile(np.abs(off_diagonal_correlations), 75)),
                    '95th': float(np.percentile(np.abs(off_diagonal_correlations), 95))
                }
            }
        }
        
        return correlation_analysis
    
    def _find_high_correlation_pairs(self, correlation_matrix: np.ndarray, 
                                   parameter_names: List[str], 
                                   threshold: float = 0.1) -> List[Dict[str, Any]]:
        """
        找出高相关性的参数对
        
        Args:
            correlation_matrix: 相关性矩阵
            parameter_names: 参数名称列表
            threshold: 相关性阈值
            
        Returns:
            List[Dict[str, Any]]: 高相关性参数对列表
        """
        high_corr_pairs = []
        n_params = len(parameter_names)
        
        for i in range(n_params):
            for j in range(i + 1, n_params):
                corr_value = correlation_matrix[i, j]
                if abs(corr_value) > threshold:
                    high_corr_pairs.append({
                        'param1': parameter_names[i],
                        'param2': parameter_names[j],
                        'correlation': float(corr_value),
                        'abs_correlation': float(abs(corr_value))
                    })
        
        # 按绝对相关性降序排列
        high_corr_pairs.sort(key=lambda x: x['abs_correlation'], reverse=True)
        
        return high_corr_pairs
    
    def _analyze_space_filling(self, result: SamplingResult) -> Dict[str, Any]:
        """
        分析空间填充性质
        
        Args:
            result: 抽样结果
            
        Returns:
            Dict[str, Any]: 空间填充分析结果
        """
        from scipy.spatial.distance import pdist, squareform
        
        samples = result.samples
        
        # 计算所有样本点之间的距离
        distances = pdist(samples)
        distance_matrix = squareform(distances)
        
        # 空间填充指标
        space_filling_metrics = {
            'min_distance': float(np.min(distances)),
            'max_distance': float(np.max(distances)),
            'mean_distance': float(np.mean(distances)),
            'std_distance': float(np.std(distances)),
            'distance_percentiles': {
                '5th': float(np.percentile(distances, 5)),
                '25th': float(np.percentile(distances, 25)),
                '50th': float(np.percentile(distances, 50)),
                '75th': float(np.percentile(distances, 75)),
                '95th': float(np.percentile(distances, 95))
            },
            'space_filling_efficiency': self._calculate_space_filling_efficiency(samples),
            'nearest_neighbor_analysis': self._analyze_nearest_neighbors(distance_matrix)
        }
        
        return space_filling_metrics
    
    def _calculate_space_filling_efficiency(self, samples: np.ndarray) -> float:
        """
        计算空间填充效率
        
        Args:
            samples: 样本数据
            
        Returns:
            float: 空间填充效率
        """
        n_samples, n_dims = samples.shape
        
        # 计算理论最优最小距离（假设均匀分布）
        unit_volume = 1.0  # 假设单位超立方体
        theoretical_min_distance = (unit_volume / n_samples) ** (1.0 / n_dims)
        
        # 计算实际最小距离
        from scipy.spatial.distance import pdist
        actual_min_distance = np.min(pdist(samples))
        
        # 效率 = 实际最小距离 / 理论最小距离
        efficiency = actual_min_distance / theoretical_min_distance
        
        return float(min(efficiency, 1.0))  # 限制在[0,1]范围内
    
    def _analyze_nearest_neighbors(self, distance_matrix: np.ndarray) -> Dict[str, Any]:
        """
        分析最近邻特性
        
        Args:
            distance_matrix: 距离矩阵
            
        Returns:
            Dict[str, Any]: 最近邻分析结果
        """
        # 对每个点找到最近邻距离（排除自身）
        np.fill_diagonal(distance_matrix, np.inf)
        nearest_distances = np.min(distance_matrix, axis=1)
        
        nearest_neighbor_analysis = {
            'mean_nearest_distance': float(np.mean(nearest_distances)),
            'std_nearest_distance': float(np.std(nearest_distances)),
            'min_nearest_distance': float(np.min(nearest_distances)),
            'max_nearest_distance': float(np.max(nearest_distances)),
            'nearest_distance_cv': float(np.std(nearest_distances) / np.mean(nearest_distances))
        }
        
        return nearest_neighbor_analysis
    
    def _perform_uniformity_tests(self, result: SamplingResult) -> Dict[str, Any]:
        """
        执行均匀性检验
        
        Args:
            result: 抽样结果
            
        Returns:
            Dict[str, Any]: 均匀性检验结果
        """
        samples = result.samples
        uniformity_results = {}
        
        # 对每个维度进行均匀性检验
        for i, param_name in enumerate(result.parameter_names):
            param_samples = samples[:, i]
            
            # 标准化到[0,1]区间
            param_min, param_max = np.min(param_samples), np.max(param_samples)
            normalized_samples = (param_samples - param_min) / (param_max - param_min)
            
            # KS检验
            ks_stat, ks_pvalue = kstest(normalized_samples, 'uniform')
            
            # Chi-square拟合优度检验
            chi2_result = self._chi_square_uniformity_test(normalized_samples)
            
            uniformity_results[param_name] = {
                'ks_test': {
                    'statistic': float(ks_stat),
                    'pvalue': float(ks_pvalue),
                    'is_uniform': ks_pvalue > self.quality_thresholds.uniformity_pvalue
                },
                'chi2_test': chi2_result
            }
        
        # 多维均匀性检验
        multivariate_test = self._multivariate_uniformity_test(samples)
        uniformity_results['multivariate'] = multivariate_test
        
        return uniformity_results
    
    def _chi_square_uniformity_test(self, samples: np.ndarray, n_bins: int = 20) -> Dict[str, Any]:
        """
        Chi-square均匀性检验
        
        Args:
            samples: 标准化样本（[0,1]区间）
            n_bins: 分箱数量
            
        Returns:
            Dict[str, Any]: Chi-square检验结果
        """
        # 计算观察频数
        observed, _ = np.histogram(samples, bins=n_bins, range=(0, 1))
        
        # 期望频数（均匀分布）
        expected = len(samples) / n_bins
        
        # Chi-square统计量
        chi2_stat = np.sum((observed - expected) ** 2 / expected)
        
        # 自由度
        df = n_bins - 1
        
        # p值
        pvalue = 1 - stats.chi2.cdf(chi2_stat, df)
        
        return {
            'statistic': float(chi2_stat),
            'pvalue': float(pvalue),
            'degrees_of_freedom': df,
            'is_uniform': pvalue > self.quality_thresholds.uniformity_pvalue,
            'observed_frequencies': observed.tolist(),
            'expected_frequency': expected
        }
    
    def _multivariate_uniformity_test(self, samples: np.ndarray) -> Dict[str, Any]:
        """
        多维均匀性检验
        
        Args:
            samples: 样本数据
            
        Returns:
            Dict[str, Any]: 多维均匀性检验结果
        """
        # 使用能量统计检验多维均匀性
        n_samples, n_dims = samples.shape
        
        # 标准化样本到[0,1]^d
        normalized_samples = np.zeros_like(samples)
        for i in range(n_dims):
            col_min, col_max = np.min(samples[:, i]), np.max(samples[:, i])
            normalized_samples[:, i] = (samples[:, i] - col_min) / (col_max - col_min)
        
        # 计算到原点的距离分布
        distances_to_origin = np.linalg.norm(normalized_samples, axis=1)
        
        # 理论上，d维单位超立方体中点到原点距离的期望
        theoretical_mean_distance = np.sqrt(n_dims) / 2
        
        # 实际平均距离
        actual_mean_distance = np.mean(distances_to_origin)
        
        # 简单的偏差度量
        distance_deviation = abs(actual_mean_distance - theoretical_mean_distance)
        
        return {
            'theoretical_mean_distance': float(theoretical_mean_distance),
            'actual_mean_distance': float(actual_mean_distance),
            'distance_deviation': float(distance_deviation),
            'normalized_deviation': float(distance_deviation / theoretical_mean_distance),
            'is_uniform_multivariate': bool(distance_deviation < 0.1 * theoretical_mean_distance)
        }
    
    def _generate_recommendations(self, result: SamplingResult) -> List[str]:
        """
        生成改进建议
        
        Args:
            result: 抽样结果
            
        Returns:
            List[str]: 建议列表
        """
        recommendations = []
        metrics = result.quality_metrics
        
        # 基于质量指标的建议
        if metrics['min_distance'] < self.quality_thresholds.min_distance:
            recommendations.append(
                f"最小距离过小 ({metrics['min_distance']:.4f})，建议增加样本数量或使用maximin优化"
            )
        
        if metrics['max_correlation'] > self.quality_thresholds.max_correlation:
            recommendations.append(
                f"参数间相关性过高 ({metrics['max_correlation']:.4f})，建议检查参数定义或使用相关性优化"
            )
        
        if metrics['min_coverage'] < self.quality_thresholds.min_coverage:
            recommendations.append(
                f"覆盖度不足 ({metrics['min_coverage']:.4f})，建议增加样本数量或调整抽样算法"
            )
        
        # 基于性能的建议
        if result.generation_time > 60:  # 超过1分钟
            recommendations.append(
                f"生成时间较长 ({result.generation_time:.2f}秒)，建议使用批量处理或并行计算"
            )
        
        # 基于样本数量的建议
        if result.config.n_samples < 1000:
            recommendations.append(
                "样本数量较少，可能影响抽样质量，建议增加到至少1000个样本"
            )
        elif result.config.n_samples > 50000:
            recommendations.append(
                "样本数量很大，建议评估是否需要如此多的样本，或考虑使用分层抽样"
            )
        
        return recommendations
    
    def generate_quality_report(self, result: SamplingResult) -> str:
        """
        生成质量报告
        
        Args:
            result: 抽样结果
            
        Returns:
            str: 质量报告文本
        """
        analysis = self.analyze_sampling_quality(result)
        
        report_lines = [
            "=" * 60,
            "参数抽样质量报告",
            "=" * 60,
            "",
            f"抽样配置:",
            f"  - 样本数量: {result.config.n_samples:,}",
            f"  - 参数维度: {len(result.parameter_names)}",
            f"  - 抽样方法: {result.config.sampling_method}",
            f"  - 随机种子: {result.config.random_seed}",
            f"  - 生成时间: {result.generation_time:.2f}秒",
            "",
            f"总体质量评估:",
            f"  - 总体评分: {analysis['overall_quality']['overall_score']:.3f}",
            f"  - 质量等级: {analysis['overall_quality']['quality_grade']}",
            f"  - 距离评分: {analysis['overall_quality']['distance_score']:.3f}",
            f"  - 相关性评分: {analysis['overall_quality']['correlation_score']:.3f}",
            f"  - 覆盖度评分: {analysis['overall_quality']['coverage_score']:.3f}",
            "",
            f"关键指标:",
            f"  - 最小距离: {result.quality_metrics['min_distance']:.6f}",
            f"  - 最大相关性: {result.quality_metrics['max_correlation']:.6f}",
            f"  - 最小覆盖度: {result.quality_metrics['min_coverage']:.6f}",
            "",
            f"改进建议:"
        ]
        
        for i, recommendation in enumerate(analysis['recommendations'], 1):
            report_lines.append(f"  {i}. {recommendation}")
        
        report_lines.extend([
            "",
            "=" * 60
        ])
        
        return "\n".join(report_lines)