"""
参数抽样器模块
实现拉丁超立方抽样（LHS）算法用于模型校准
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
import time
import hashlib
import json
from scipy.stats import qmc
from scipy.stats import norm, lognorm, kstest
from scipy.spatial.distance import pdist


@dataclass
class ParameterDefinition:
    """参数定义数据类"""
    name: str
    min_value: float
    max_value: float
    distribution: str = "uniform"  # uniform, normal, lognormal, etc.
    constraints: Optional[List[str]] = None
    description: str = ""


@dataclass
class SamplingConfig:
    """抽样配置数据类"""
    parameters: List[ParameterDefinition]
    n_samples: int
    random_seed: int
    sampling_method: str = "lhs"
    optimization_criterion: str = "maximin"  # maximin, correlation, etc.


@dataclass
class SamplingResult:
    """抽样结果数据类"""
    samples: np.ndarray
    parameter_names: List[str]
    config: SamplingConfig
    quality_metrics: Dict[str, float]
    generation_time: float
    hash_signature: str


class LatinHypercubeSampler:
    """拉丁超立方抽样器类"""
    
    def __init__(self, config: SamplingConfig):
        """
        初始化抽样器
        
        Args:
            config: 抽样配置
        """
        self.config = config
        self.parameters = {p.name: p for p in config.parameters}
        self.n_dimensions = len(config.parameters)
        self.rng = np.random.RandomState(config.random_seed)
    
    def generate_samples(self) -> SamplingResult:
        """
        生成拉丁超立方抽样
        
        Returns:
            SamplingResult: 抽样结果
        """
        start_time = time.time()
        
        # 处理单维度情况 - scipy的lloyd优化要求维度>=2
        if self.n_dimensions == 1:
            # 对于单维度，使用简单的均匀分层抽样
            unit_samples = self._generate_single_dimension_samples()
        else:
            # 使用scipy的拉丁超立方抽样
            # 将maximin映射到lloyd，correlation映射到random-cd
            optimization_mapping = {
                'maximin': 'lloyd',
                'correlation': 'random-cd',
                'lloyd': 'lloyd',
                'random-cd': 'random-cd'
            }
            
            scipy_optimization = optimization_mapping.get(
                self.config.optimization_criterion, 'lloyd'
            )
            
            # 对于单维度或小样本，避免使用lloyd优化
            if self.n_dimensions == 1 or self.config.n_samples < 2:
                scipy_optimization = None
            
            sampler = qmc.LatinHypercube(
                d=self.n_dimensions, 
                seed=self.config.random_seed,
                optimization=scipy_optimization
            )
            
            # 生成[0,1]区间的样本
            unit_samples = sampler.random(n=self.config.n_samples)
        
        # 转换到实际参数范围
        scaled_samples = self._scale_samples(unit_samples)
        
        # 计算质量指标
        quality_metrics = self._calculate_quality_metrics(scaled_samples)
        
        generation_time = time.time() - start_time
        
        # 生成哈希签名
        hash_signature = self._generate_hash_signature(scaled_samples)
        
        return SamplingResult(
            samples=scaled_samples,
            parameter_names=[p.name for p in self.config.parameters],
            config=self.config,
            quality_metrics=quality_metrics,
            generation_time=generation_time,
            hash_signature=hash_signature
        )
    
    def _generate_single_dimension_samples(self) -> np.ndarray:
        """
        生成单维度拉丁超立方样本
        
        Returns:
            np.ndarray: 单维度样本
        """
        # 创建分层样本
        strata = np.arange(self.config.n_samples, dtype=float)
        
        # 在每个层内随机抽样
        np.random.seed(self.config.random_seed)
        random_offsets = np.random.random(self.config.n_samples)
        
        # 生成[0,1]区间的分层样本
        unit_samples = (strata + random_offsets) / self.config.n_samples
        
        # 随机打乱顺序
        np.random.shuffle(unit_samples)
        
        return unit_samples.reshape(-1, 1)
    
    def _scale_samples(self, unit_samples: np.ndarray) -> np.ndarray:
        """
        将[0,1]区间样本缩放到实际参数范围
        
        Args:
            unit_samples: [0,1]区间的单位样本
            
        Returns:
            np.ndarray: 缩放后的样本
        """
        scaled_samples = np.zeros_like(unit_samples)
        
        for i, param in enumerate(self.config.parameters):
            # 处理零范围参数（min == max）
            if param.min_value == param.max_value:
                scaled_samples[:, i] = param.min_value
                continue
                
            if param.distribution == "uniform":
                scaled_samples[:, i] = (
                    param.min_value + 
                    unit_samples[:, i] * (param.max_value - param.min_value)
                )
            elif param.distribution == "normal":
                # 使用正态分布的逆累积分布函数
                # 避免极值导致的inf问题
                clipped_samples = np.clip(unit_samples[:, i], 1e-10, 1-1e-10)
                scaled_samples[:, i] = norm.ppf(
                    clipped_samples, 
                    loc=(param.min_value + param.max_value) / 2,
                    scale=(param.max_value - param.min_value) / 6  # 3-sigma规则
                )
            elif param.distribution == "lognormal":
                # 避免极值导致的inf问题
                clipped_samples = np.clip(unit_samples[:, i], 1e-10, 1-1e-10)
                if param.min_value <= 0:
                    raise ValueError(f"对数正态分布参数 {param.name} 的最小值必须大于0")
                scaled_samples[:, i] = lognorm.ppf(
                    clipped_samples,
                    s=0.5,  # 形状参数
                    scale=np.exp((np.log(param.min_value) + np.log(param.max_value)) / 2)
                )
            else:
                # 默认使用均匀分布
                scaled_samples[:, i] = (
                    param.min_value + 
                    unit_samples[:, i] * (param.max_value - param.min_value)
                )
        
        return scaled_samples
    
    def _calculate_quality_metrics(self, samples: np.ndarray) -> Dict[str, float]:
        """
        计算抽样质量指标
        
        Args:
            samples: 抽样样本
            
        Returns:
            Dict[str, float]: 质量指标字典
        """
        metrics = {}
        
        # 计算最小距离（Maximin准则）
        distances = pdist(samples)
        if len(distances) == 0:
            # 处理单样本或零样本情况
            metrics['min_distance'] = 0.0
            metrics['mean_distance'] = 0.0
        else:
            metrics['min_distance'] = float(np.min(distances))
            metrics['mean_distance'] = float(np.mean(distances))
        
        # 计算相关性
        if samples.shape[1] > 1:
            correlation_matrix = np.corrcoef(samples.T)
            off_diagonal = correlation_matrix[np.triu_indices_from(correlation_matrix, k=1)]
            if len(off_diagonal) > 0:
                metrics['max_correlation'] = float(np.max(np.abs(off_diagonal)))
                metrics['mean_correlation'] = float(np.mean(np.abs(off_diagonal)))
            else:
                metrics['max_correlation'] = 0.0
                metrics['mean_correlation'] = 0.0
        else:
            # 单参数情况，相关性为0
            metrics['max_correlation'] = 0.0
            metrics['mean_correlation'] = 0.0
        
        # 计算覆盖度（每个维度的分布均匀性）
        coverage_scores = []
        for i in range(samples.shape[1]):
            # 使用Kolmogorov-Smirnov检验
            ks_stat, _ = kstest(samples[:, i], 'uniform')
            coverage_scores.append(1 - ks_stat)  # 转换为覆盖度分数
        
        metrics['mean_coverage'] = float(np.mean(coverage_scores))
        metrics['min_coverage'] = float(np.min(coverage_scores))
        
        return metrics
    
    def _generate_hash_signature(self, samples: np.ndarray) -> str:
        """
        生成抽样结果的哈希签名
        
        Args:
            samples: 抽样样本
            
        Returns:
            str: 哈希签名
        """
        # 创建包含配置和样本的字符串
        config_str = json.dumps({
            'n_samples': self.config.n_samples,
            'random_seed': self.config.random_seed,
            'sampling_method': self.config.sampling_method,
            'optimization_criterion': self.config.optimization_criterion,
            'parameters': [
                {
                    'name': p.name,
                    'min_value': p.min_value,
                    'max_value': p.max_value,
                    'distribution': p.distribution
                } for p in self.config.parameters
            ]
        }, sort_keys=True)
        
        # 添加样本数据的哈希
        samples_hash = hashlib.md5(samples.tobytes()).hexdigest()
        
        # 生成最终哈希
        combined_str = config_str + samples_hash
        return hashlib.sha256(combined_str.encode()).hexdigest()
    
    def validate_orthogonality(self, samples: np.ndarray, tolerance: float = 1e-6) -> Dict[str, Any]:
        """
        验证抽样点的正交性
        
        Args:
            samples: 抽样样本
            tolerance: 容差值
            
        Returns:
            Dict[str, Any]: 正交性验证结果
        """
        n_samples, n_dims = samples.shape
        
        # 检查每个维度的分层性
        stratification_results = {}
        for dim in range(n_dims):
            # 将样本分为n_samples个层
            sorted_samples = np.sort(samples[:, dim])
            expected_intervals = np.linspace(
                sorted_samples[0], sorted_samples[-1], n_samples + 1
            )
            
            # 检查每个层是否包含恰好一个样本
            layer_counts = []
            for i in range(n_samples):
                count = np.sum(
                    (samples[:, dim] >= expected_intervals[i]) & 
                    (samples[:, dim] < expected_intervals[i + 1])
                )
                layer_counts.append(count)
            
            # 最后一个区间包含右端点
            layer_counts[-1] += np.sum(samples[:, dim] == expected_intervals[-1])
            
            stratification_results[f'dimension_{dim}'] = {
                'layer_counts': layer_counts,
                'is_stratified': all(count == 1 for count in layer_counts),
                'max_deviation': max(abs(count - 1) for count in layer_counts)
            }
        
        # 计算整体正交性分数
        orthogonality_score = np.mean([
            result['is_stratified'] for result in stratification_results.values()
        ])
        
        return {
            'orthogonality_score': orthogonality_score,
            'is_orthogonal': orthogonality_score >= (1.0 - tolerance),
            'dimension_results': stratification_results
        }


class BatchSampler:
    """批量抽样器，用于处理大规模抽样"""
    
    def __init__(self, base_sampler: LatinHypercubeSampler, batch_size: int = 1000):
        """
        初始化批量抽样器
        
        Args:
            base_sampler: 基础抽样器
            batch_size: 批次大小
        """
        self.base_sampler = base_sampler
        self.batch_size = batch_size
    
    def generate_large_samples(self, total_samples: int, 
                             progress_callback: Optional[callable] = None) -> SamplingResult:
        """
        生成大规模抽样
        
        Args:
            total_samples: 总样本数
            progress_callback: 进度回调函数
            
        Returns:
            SamplingResult: 合并的抽样结果
        """
        start_time = time.time()
        all_samples = []
        all_quality_metrics = []
        
        n_batches = (total_samples + self.batch_size - 1) // self.batch_size
        
        for batch_idx in range(n_batches):
            # 计算当前批次的样本数
            current_batch_size = min(
                self.batch_size, 
                total_samples - batch_idx * self.batch_size
            )
            
            # 更新配置中的样本数和随机种子
            batch_config = SamplingConfig(
                parameters=self.base_sampler.config.parameters,
                n_samples=current_batch_size,
                random_seed=self.base_sampler.config.random_seed + batch_idx,
                sampling_method=self.base_sampler.config.sampling_method,
                optimization_criterion=self.base_sampler.config.optimization_criterion
            )
            
            # 创建批次抽样器
            batch_sampler = LatinHypercubeSampler(batch_config)
            batch_result = batch_sampler.generate_samples()
            
            all_samples.append(batch_result.samples)
            all_quality_metrics.append(batch_result.quality_metrics)
            
            # 调用进度回调
            if progress_callback:
                progress = (batch_idx + 1) / n_batches
                progress_callback(progress, batch_idx + 1, n_batches)
        
        # 合并所有批次的样本
        combined_samples = np.vstack(all_samples)
        
        # 计算合并后的质量指标
        combined_metrics = self.base_sampler._calculate_quality_metrics(combined_samples)
        
        generation_time = time.time() - start_time
        hash_signature = self.base_sampler._generate_hash_signature(combined_samples)
        
        # 创建最终配置
        final_config = SamplingConfig(
            parameters=self.base_sampler.config.parameters,
            n_samples=total_samples,
            random_seed=self.base_sampler.config.random_seed,
            sampling_method=self.base_sampler.config.sampling_method,
            optimization_criterion=self.base_sampler.config.optimization_criterion
        )
        
        return SamplingResult(
            samples=combined_samples,
            parameter_names=[p.name for p in self.base_sampler.config.parameters],
            config=final_config,
            quality_metrics=combined_metrics,
            generation_time=generation_time,
            hash_signature=hash_signature
        )