"""
内存管理模块
实现内存使用优化和垃圾回收机制
"""

import gc
import psutil
import time
import threading
import weakref
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass
import numpy as np
import logging
from contextlib import contextmanager
from collections import defaultdict
import sys

logger = logging.getLogger(__name__)

@dataclass
class MemorySnapshot:
    """内存快照数据类"""
    timestamp: float
    total_mb: float
    available_mb: float
    used_mb: float
    percent: float
    process_mb: float
    process_percent: float
    gc_counts: Dict[int, int]
    
@dataclass
class MemoryThresholds:
    """内存阈值配置"""
    warning_percent: float = 75.0
    critical_percent: float = 85.0
    emergency_percent: float = 95.0
    process_limit_mb: float = 2000.0
    gc_trigger_percent: float = 70.0

class MemoryTracker:
    """内存跟踪器"""
    
    def __init__(self, thresholds: Optional[MemoryThresholds] = None):
        self.thresholds = thresholds or MemoryThresholds()
        self.snapshots = []
        self.process = psutil.Process()
        self.tracking_active = False
        self.tracking_thread = None
        self.tracking_interval = 1.0  # 秒
        self.callbacks = {
            'warning': [],
            'critical': [],
            'emergency': []
        }
        
    def start_tracking(self, interval: float = 1.0):
        """开始内存跟踪"""
        if self.tracking_active:
            return
            
        self.tracking_interval = interval
        self.tracking_active = True
        self.tracking_thread = threading.Thread(target=self._tracking_loop, daemon=True)
        self.tracking_thread.start()
        logger.info("内存跟踪已启动")
    
    def stop_tracking(self):
        """停止内存跟踪"""
        self.tracking_active = False
        if self.tracking_thread:
            self.tracking_thread.join(timeout=2.0)
        logger.info("内存跟踪已停止")
    
    def _tracking_loop(self):
        """跟踪循环"""
        while self.tracking_active:
            try:
                snapshot = self.take_snapshot()
                self._check_thresholds(snapshot)
                time.sleep(self.tracking_interval)
            except Exception as e:
                logger.error(f"内存跟踪错误: {e}")
                time.sleep(self.tracking_interval)
    
    def take_snapshot(self) -> MemorySnapshot:
        """获取内存快照"""
        # 系统内存信息
        system_memory = psutil.virtual_memory()
        
        # 进程内存信息
        process_memory = self.process.memory_info()
        process_percent = self.process.memory_percent()
        
        # 垃圾回收统计
        gc_counts = {i: gc.get_count()[i] for i in range(3)}
        
        snapshot = MemorySnapshot(
            timestamp=time.time(),
            total_mb=system_memory.total / (1024 * 1024),
            available_mb=system_memory.available / (1024 * 1024),
            used_mb=system_memory.used / (1024 * 1024),
            percent=system_memory.percent,
            process_mb=process_memory.rss / (1024 * 1024),
            process_percent=process_percent,
            gc_counts=gc_counts
        )
        
        self.snapshots.append(snapshot)
        
        # 保持最近1000个快照
        if len(self.snapshots) > 1000:
            self.snapshots = self.snapshots[-1000:]
        
        return snapshot
    
    def _check_thresholds(self, snapshot: MemorySnapshot):
        """检查内存阈值"""
        # 检查系统内存
        if snapshot.percent >= self.thresholds.emergency_percent:
            self._trigger_callbacks('emergency', snapshot)
        elif snapshot.percent >= self.thresholds.critical_percent:
            self._trigger_callbacks('critical', snapshot)
        elif snapshot.percent >= self.thresholds.warning_percent:
            self._trigger_callbacks('warning', snapshot)
        
        # 检查进程内存
        if snapshot.process_mb >= self.thresholds.process_limit_mb:
            self._trigger_callbacks('critical', snapshot)
    
    def _trigger_callbacks(self, level: str, snapshot: MemorySnapshot):
        """触发回调函数"""
        for callback in self.callbacks[level]:
            try:
                callback(snapshot)
            except Exception as e:
                logger.error(f"内存回调函数执行失败 ({level}): {e}")
    
    def add_callback(self, level: str, callback: Callable[[MemorySnapshot], None]):
        """添加内存阈值回调"""
        if level in self.callbacks:
            self.callbacks[level].append(callback)
    
    def get_memory_trend(self, window_minutes: float = 5.0) -> Dict[str, float]:
        """获取内存使用趋势"""
        if len(self.snapshots) < 2:
            return {'trend': 0.0, 'volatility': 0.0}
        
        # 获取指定时间窗口内的快照
        cutoff_time = time.time() - window_minutes * 60
        recent_snapshots = [s for s in self.snapshots if s.timestamp >= cutoff_time]
        
        if len(recent_snapshots) < 2:
            recent_snapshots = self.snapshots[-10:]  # 至少使用最近10个快照
        
        # 计算趋势
        times = [s.timestamp for s in recent_snapshots]
        memory_usage = [s.percent for s in recent_snapshots]
        
        # 线性回归计算趋势
        n = len(times)
        if n >= 2:
            time_mean = np.mean(times)
            memory_mean = np.mean(memory_usage)
            
            numerator = sum((t - time_mean) * (m - memory_mean) for t, m in zip(times, memory_usage))
            denominator = sum((t - time_mean) ** 2 for t in times)
            
            trend = numerator / denominator if denominator != 0 else 0.0
            
            # 计算波动性（标准差）
            volatility = np.std(memory_usage)
        else:
            trend = 0.0
            volatility = 0.0
        
        return {
            'trend': trend * 3600,  # 转换为每小时的变化率
            'volatility': volatility,
            'current_usage': recent_snapshots[-1].percent,
            'peak_usage': max(s.percent for s in recent_snapshots),
            'min_usage': min(s.percent for s in recent_snapshots)
        }
    
    def get_gc_statistics(self) -> Dict[str, Any]:
        """获取垃圾回收统计"""
        if not self.snapshots:
            return {}
        
        recent_snapshots = self.snapshots[-100:]  # 最近100个快照
        
        # 计算GC频率
        gc_deltas = defaultdict(list)
        for i in range(1, len(recent_snapshots)):
            prev_counts = recent_snapshots[i-1].gc_counts
            curr_counts = recent_snapshots[i].gc_counts
            time_delta = recent_snapshots[i].timestamp - recent_snapshots[i-1].timestamp
            
            for generation in range(3):
                count_delta = curr_counts[generation] - prev_counts[generation]
                if count_delta > 0 and time_delta > 0:
                    gc_deltas[generation].append(count_delta / time_delta)
        
        # 计算统计信息
        stats = {}
        for generation in range(3):
            if gc_deltas[generation]:
                stats[f'gen_{generation}_frequency'] = np.mean(gc_deltas[generation])
                stats[f'gen_{generation}_max_frequency'] = np.max(gc_deltas[generation])
            else:
                stats[f'gen_{generation}_frequency'] = 0.0
                stats[f'gen_{generation}_max_frequency'] = 0.0
        
        # 当前GC计数
        if recent_snapshots:
            current_counts = recent_snapshots[-1].gc_counts
            stats.update({f'gen_{i}_count': current_counts[i] for i in range(3)})
        
        return stats

class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self, tracker: Optional[MemoryTracker] = None):
        self.tracker = tracker or MemoryTracker()
        self.optimization_history = []
        self.object_pools = {}
        self.weak_references = weakref.WeakSet()
        
    def optimize_array_memory(self, arrays: List[np.ndarray], 
                            target_dtype: Optional[np.dtype] = None) -> List[np.ndarray]:
        """优化数组内存使用"""
        optimized_arrays = []
        memory_saved = 0
        
        for arr in arrays:
            original_size = arr.nbytes
            
            # 选择最优数据类型
            if target_dtype is None:
                if arr.dtype == np.float64:
                    # 检查是否可以安全转换为float32
                    if np.allclose(arr, arr.astype(np.float32), rtol=1e-6):
                        optimized_arr = arr.astype(np.float32)
                    else:
                        optimized_arr = arr
                elif arr.dtype == np.int64:
                    # 检查是否可以使用更小的整数类型
                    if np.all((arr >= np.iinfo(np.int32).min) & (arr <= np.iinfo(np.int32).max)):
                        optimized_arr = arr.astype(np.int32)
                    else:
                        optimized_arr = arr
                else:
                    optimized_arr = arr
            else:
                optimized_arr = arr.astype(target_dtype)
            
            # 确保数组是连续的（提高访问效率）
            if not optimized_arr.flags['C_CONTIGUOUS']:
                optimized_arr = np.ascontiguousarray(optimized_arr)
            
            optimized_arrays.append(optimized_arr)
            memory_saved += original_size - optimized_arr.nbytes
        
        if memory_saved > 0:
            logger.info(f"数组内存优化节省了 {memory_saved / (1024*1024):.2f} MB")
        
        return optimized_arrays
    
    def create_memory_mapped_array(self, shape: tuple, dtype: np.dtype, 
                                 filename: Optional[str] = None) -> np.ndarray:
        """创建内存映射数组"""
        if filename is None:
            # 使用临时文件
            import tempfile
            temp_file = tempfile.NamedTemporaryFile(delete=False)
            filename = temp_file.name
            temp_file.close()
        
        # 创建内存映射数组
        mmap_array = np.memmap(filename, dtype=dtype, mode='w+', shape=shape)
        
        # 注册弱引用以便清理
        self.weak_references.add(mmap_array)
        
        logger.info(f"创建内存映射数组: {shape}, {dtype}, 文件: {filename}")
        return mmap_array
    
    def batch_process_with_memory_limit(self, data: np.ndarray, 
                                      process_func: Callable,
                                      memory_limit_mb: float = 1000,
                                      overlap_ratio: float = 0.1) -> np.ndarray:
        """在内存限制下批量处理数据"""
        # 估算每个样本的内存使用
        sample_size_bytes = data[0:1].nbytes if len(data) > 0 else 0
        max_samples_per_batch = int((memory_limit_mb * 1024 * 1024) / max(sample_size_bytes, 1))
        max_samples_per_batch = max(1, max_samples_per_batch)
        
        # 计算重叠大小
        overlap_size = int(max_samples_per_batch * overlap_ratio)
        
        results = []
        start_idx = 0
        
        while start_idx < len(data):
            # 计算批次范围
            end_idx = min(start_idx + max_samples_per_batch, len(data))
            
            # 提取批次数据
            batch_data = data[start_idx:end_idx]
            
            # 处理批次
            batch_result = process_func(batch_data)
            
            # 处理重叠部分
            if start_idx > 0 and overlap_size > 0:
                # 移除重叠部分
                batch_result = batch_result[overlap_size:]
            
            results.append(batch_result)
            
            # 更新起始索引
            start_idx = end_idx - overlap_size
            
            # 强制垃圾回收
            del batch_data
            gc.collect()
        
        # 合并结果
        if results:
            return np.concatenate(results, axis=0)
        else:
            return np.array([])
    
    def optimize_sampling_memory(self, sampler_func: Callable, 
                               n_samples: int,
                               memory_limit_mb: float = 1000) -> np.ndarray:
        """优化抽样过程的内存使用"""
        # 估算单个样本的内存使用
        test_sample = sampler_func(1)
        sample_memory_mb = test_sample.nbytes / (1024 * 1024)
        
        # 计算最优批次大小
        optimal_batch_size = int(memory_limit_mb / max(sample_memory_mb, 0.001))
        optimal_batch_size = max(100, min(optimal_batch_size, n_samples))
        
        logger.info(f"优化抽样: 样本内存 {sample_memory_mb:.4f}MB, 批次大小 {optimal_batch_size}")
        
        # 批量生成样本
        all_samples = []
        remaining_samples = n_samples
        
        while remaining_samples > 0:
            current_batch_size = min(optimal_batch_size, remaining_samples)
            
            # 监控内存使用
            memory_before = psutil.Process().memory_info().rss / (1024 * 1024)
            
            # 生成批次样本
            batch_samples = sampler_func(current_batch_size)
            all_samples.append(batch_samples)
            
            memory_after = psutil.Process().memory_info().rss / (1024 * 1024)
            memory_used = memory_after - memory_before
            
            # 如果内存使用超出预期，调整批次大小
            if memory_used > memory_limit_mb * 1.2:
                optimal_batch_size = max(50, int(optimal_batch_size * 0.8))
                logger.warning(f"内存使用超出预期，调整批次大小到 {optimal_batch_size}")
            
            remaining_samples -= current_batch_size
            
            # 定期垃圾回收
            if len(all_samples) % 10 == 0:
                gc.collect()
        
        # 合并所有样本
        try:
            combined_samples = np.vstack(all_samples)
        except MemoryError:
            logger.error("合并样本时内存不足，尝试逐步合并")
            # 逐步合并以减少内存峰值
            combined_samples = all_samples[0]
            for i in range(1, len(all_samples)):
                combined_samples = np.vstack([combined_samples, all_samples[i]])
                if i % 5 == 0:  # 每5次合并后清理
                    gc.collect()
        
        return combined_samples
    
    def cleanup_memory(self, force: bool = False):
        """清理内存"""
        # 清理对象池
        for pool_name in list(self.object_pools.keys()):
            self.object_pools[pool_name].clear()
        
        # 强制垃圾回收
        if force:
            # 多次垃圾回收以确保彻底清理
            for _ in range(3):
                collected = gc.collect()
                if collected == 0:
                    break
                logger.info(f"垃圾回收清理了 {collected} 个对象")
        else:
            gc.collect()
        
        # 记录清理后的内存状态
        if self.tracker:
            snapshot = self.tracker.take_snapshot()
            logger.info(f"内存清理后: {snapshot.process_mb:.1f}MB ({snapshot.process_percent:.1f}%)")

class GarbageCollectionManager:
    """垃圾回收管理器"""
    
    def __init__(self, auto_gc: bool = True):
        self.auto_gc = auto_gc
        self.gc_stats = []
        self.gc_thresholds = gc.get_threshold()
        self.original_thresholds = self.gc_thresholds
        
    def set_aggressive_gc(self):
        """设置激进的垃圾回收策略"""
        # 降低GC阈值，更频繁地触发垃圾回收
        new_thresholds = (
            self.gc_thresholds[0] // 2,  # 第0代
            self.gc_thresholds[1] // 2,  # 第1代
            self.gc_thresholds[2] // 2   # 第2代
        )
        gc.set_threshold(*new_thresholds)
        logger.info(f"设置激进GC阈值: {new_thresholds}")
    
    def set_conservative_gc(self):
        """设置保守的垃圾回收策略"""
        # 提高GC阈值，减少垃圾回收频率
        new_thresholds = (
            self.gc_thresholds[0] * 2,
            self.gc_thresholds[1] * 2,
            self.gc_thresholds[2] * 2
        )
        gc.set_threshold(*new_thresholds)
        logger.info(f"设置保守GC阈值: {new_thresholds}")
    
    def restore_default_gc(self):
        """恢复默认垃圾回收设置"""
        gc.set_threshold(*self.original_thresholds)
        logger.info(f"恢复默认GC阈值: {self.original_thresholds}")
    
    def force_full_gc(self) -> Dict[str, int]:
        """强制完整垃圾回收"""
        start_time = time.time()
        
        # 记录GC前的计数
        before_counts = gc.get_count()
        
        # 执行完整的垃圾回收
        collected_objects = []
        for generation in range(3):
            collected = gc.collect(generation)
            collected_objects.append(collected)
        
        # 记录GC后的计数
        after_counts = gc.get_count()
        
        gc_time = time.time() - start_time
        
        # 记录统计信息
        gc_stat = {
            'timestamp': time.time(),
            'execution_time': gc_time,
            'before_counts': before_counts,
            'after_counts': after_counts,
            'collected_objects': collected_objects,
            'total_collected': sum(collected_objects)
        }
        
        self.gc_stats.append(gc_stat)
        
        logger.info(f"完整GC完成: 耗时 {gc_time:.3f}s, 清理对象 {sum(collected_objects)}")
        
        return gc_stat
    
    def get_gc_recommendations(self) -> List[str]:
        """获取垃圾回收建议"""
        recommendations = []
        
        if not self.gc_stats:
            return ["暂无足够的GC统计数据"]
        
        recent_stats = self.gc_stats[-10:]  # 最近10次GC
        
        # 分析GC频率
        if len(recent_stats) >= 2:
            time_intervals = []
            for i in range(1, len(recent_stats)):
                interval = recent_stats[i]['timestamp'] - recent_stats[i-1]['timestamp']
                time_intervals.append(interval)
            
            avg_interval = np.mean(time_intervals)
            
            if avg_interval < 10:  # 10秒内频繁GC
                recommendations.append("GC过于频繁，考虑优化内存使用或调整GC阈值")
            elif avg_interval > 300:  # 5分钟以上才GC
                recommendations.append("GC间隔较长，可能存在内存泄漏")
        
        # 分析GC效果
        avg_collected = np.mean([stat['total_collected'] for stat in recent_stats])
        if avg_collected < 10:
            recommendations.append("GC清理对象较少，可能不需要频繁GC")
        elif avg_collected > 10000:
            recommendations.append("GC清理大量对象，建议优化对象生命周期管理")
        
        # 分析GC时间
        avg_gc_time = np.mean([stat['execution_time'] for stat in recent_stats])
        if avg_gc_time > 1.0:
            recommendations.append("GC耗时较长，考虑减少对象引用复杂度")
        
        return recommendations if recommendations else ["GC性能正常"]

@contextmanager
def memory_limit_context(limit_mb: float, cleanup_func: Optional[Callable] = None):
    """内存限制上下文管理器"""
    tracker = MemoryTracker()
    initial_memory = psutil.Process().memory_info().rss / (1024 * 1024)
    
    def memory_warning(snapshot: MemorySnapshot):
        logger.warning(f"内存使用接近限制: {snapshot.process_mb:.1f}MB / {limit_mb}MB")
    
    def memory_critical(snapshot: MemorySnapshot):
        logger.error(f"内存使用超出限制: {snapshot.process_mb:.1f}MB / {limit_mb}MB")
        if cleanup_func:
            cleanup_func()
        gc.collect()
    
    # 设置内存阈值
    thresholds = MemoryThresholds(
        warning_percent=75.0,
        critical_percent=85.0,
        process_limit_mb=limit_mb
    )
    tracker.thresholds = thresholds
    
    # 添加回调
    tracker.add_callback('warning', memory_warning)
    tracker.add_callback('critical', memory_critical)
    
    try:
        tracker.start_tracking(interval=0.5)  # 0.5秒检查一次
        yield tracker
    finally:
        tracker.stop_tracking()
        final_memory = psutil.Process().memory_info().rss / (1024 * 1024)
        memory_delta = final_memory - initial_memory
        
        if memory_delta > 0:
            logger.info(f"内存使用增加: {memory_delta:.1f}MB")
        else:
            logger.info(f"内存使用减少: {abs(memory_delta):.1f}MB")

class ObjectPool:
    """对象池"""
    
    def __init__(self, factory_func: Callable, max_size: int = 100):
        self.factory_func = factory_func
        self.max_size = max_size
        self.pool = []
        self.created_count = 0
        self.reused_count = 0
    
    def get_object(self):
        """获取对象"""
        if self.pool:
            obj = self.pool.pop()
            self.reused_count += 1
            return obj
        else:
            obj = self.factory_func()
            self.created_count += 1
            return obj
    
    def return_object(self, obj):
        """归还对象"""
        if len(self.pool) < self.max_size:
            # 重置对象状态（如果需要）
            if hasattr(obj, 'reset'):
                obj.reset()
            self.pool.append(obj)
    
    def clear(self):
        """清空对象池"""
        self.pool.clear()
    
    def get_stats(self) -> Dict[str, int]:
        """获取对象池统计"""
        return {
            'pool_size': len(self.pool),
            'max_size': self.max_size,
            'created_count': self.created_count,
            'reused_count': self.reused_count,
            'reuse_rate': self.reused_count / max(self.created_count + self.reused_count, 1)
        }