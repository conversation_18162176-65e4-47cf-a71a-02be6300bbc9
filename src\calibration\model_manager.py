"""
模型管理器模块
实现深度神经网络模型的保存、加载和版本管理功能
"""

import os
import json
import pickle
import shutil
import hashlib
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field, asdict
import time
import logging
import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow import keras
import joblib
from datetime import datetime
import zipfile
import warnings

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ModelMetadata:
    """模型元数据"""
    model_name: str
    version: str
    created_date: str
    framework: str = "TensorFlow/Keras"
    model_type: str = "深度神经网络"
    input_shape: Optional[Tuple] = None
    output_shape: Optional[Tuple] = None
    total_params: int = 0
    trainable_params: int = 0
    training_epochs: int = 0
    best_epoch: int = 0
    training_time: float = 0.0
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    hyperparameters: Dict[str, Any] = field(default_factory=dict)
    preprocessing_info: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    description: str = ""
    author: str = ""
    model_hash: str = ""


@dataclass
class ModelManagerConfig:
    """模型管理器配置"""
    base_dir: str = "models"
    backup_dir: str = "models/backups"
    temp_dir: str = "models/temp"
    max_versions: int = 10
    auto_backup: bool = True
    compression: bool = True
    checksum_validation: bool = True
    metadata_format: str = "json"  # json, yaml
    model_format: str = "h5"  # h5, savedmodel, tf
    include_optimizer: bool = False
    save_weights_only: bool = False


class ModelManager:
    """模型管理器类"""
    
    def __init__(self, config: ModelManagerConfig):
        """
        初始化模型管理器
        
        Args:
            config: 管理器配置
        """
        self.config = config
        self.models_registry = {}
        
        # 创建必要的目录
        for dir_path in [config.base_dir, config.backup_dir, config.temp_dir]:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
        
        # 加载现有模型注册表
        self._load_registry()
        
        logger.info(f"模型管理器初始化完成，配置: {config}")
    
    def save_model(
        self,
        model: keras.Model,
        model_name: str,
        version: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        scalers: Optional[Dict[str, Any]] = None,
        training_history: Optional[Dict] = None,
        overwrite: bool = False
    ) -> str:
        """
        保存模型及相关组件
        
        Args:
            model: Keras模型
            model_name: 模型名称
            version: 版本号，如果为None则自动生成
            metadata: 额外的元数据
            scalers: 数据预处理器字典
            training_history: 训练历史
            overwrite: 是否覆盖现有版本
            
        Returns:
            str: 保存路径
        """
        # 生成版本号
        if version is None:
            version = self._generate_version(model_name)
        
        # 检查版本是否已存在
        model_path = Path(self.config.base_dir) / model_name / version
        if model_path.exists() and not overwrite:
            raise ValueError(f"模型版本已存在: {model_path}")
        
        # 创建模型目录
        model_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"开始保存模型: {model_name} v{version}")
        
        try:
            # 1. 保存模型
            model_file_path = self._save_model_file(model, model_path)
            
            # 2. 保存预处理器
            scalers_path = None
            if scalers:
                scalers_path = self._save_scalers(scalers, model_path)
            
            # 3. 保存训练历史
            history_path = None
            if training_history:
                history_path = self._save_training_history(training_history, model_path)
            
            # 4. 生成模型元数据
            model_metadata = self._create_metadata(
                model, model_name, version, metadata, training_history
            )
            
            # 5. 保存元数据
            metadata_path = self._save_metadata(model_metadata, model_path)
            
            # 6. 计算模型哈希
            model_hash = self._calculate_model_hash(model_file_path)
            model_metadata.model_hash = model_hash
            
            # 7. 更新元数据文件
            self._save_metadata(model_metadata, model_path)
            
            # 8. 更新注册表
            self._update_registry(model_name, version, model_metadata)
            
            # 9. 创建压缩包（如果启用）
            if self.config.compression:
                self._create_compressed_archive(model_path)
            
            # 10. 自动备份（如果启用）
            if self.config.auto_backup:
                self._create_backup(model_name, version)
            
            # 11. 清理旧版本
            self._cleanup_old_versions(model_name)
            
            logger.info(f"模型保存成功: {model_path}")
            return str(model_path)
            
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
            # 清理部分保存的文件
            if model_path.exists():
                shutil.rmtree(model_path, ignore_errors=True)
            raise
    
    def load_model(
        self,
        model_name: str,
        version: Optional[str] = None,
        load_scalers: bool = True,
        load_history: bool = False,
        custom_objects: Optional[Dict] = None
    ) -> Tuple[keras.Model, Dict[str, Any]]:
        """
        加载模型及相关组件
        
        Args:
            model_name: 模型名称
            version: 版本号，如果为None则加载最新版本
            load_scalers: 是否加载预处理器
            load_history: 是否加载训练历史
            custom_objects: 自定义对象字典
            
        Returns:
            Tuple[keras.Model, Dict[str, Any]]: 模型和相关组件
        """
        # 获取版本
        if version is None:
            version = self._get_latest_version(model_name)
        
        model_path = Path(self.config.base_dir) / model_name / version
        
        if not model_path.exists():
            raise FileNotFoundError(f"模型不存在: {model_path}")
        
        logger.info(f"开始加载模型: {model_name} v{version}")
        
        try:
            # 1. 加载元数据
            metadata = self._load_metadata(model_path)
            
            # 2. 验证模型完整性（如果启用）
            if self.config.checksum_validation:
                self._validate_model_integrity(model_path, metadata)
            
            # 3. 加载模型
            model = self._load_model_file(model_path, custom_objects)
            
            # 4. 准备返回的组件字典
            components = {
                'metadata': metadata,
                'model_path': str(model_path)
            }
            
            # 5. 加载预处理器（如果需要）
            if load_scalers:
                scalers = self._load_scalers(model_path)
                if scalers:
                    components['scalers'] = scalers
            
            # 6. 加载训练历史（如果需要）
            if load_history:
                history = self._load_training_history(model_path)
                if history:
                    components['training_history'] = history
            
            logger.info(f"模型加载成功: {model_name} v{version}")
            return model, components
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    
    def list_models(self, include_metadata: bool = False) -> Dict[str, Any]:
        """
        列出所有已保存的模型
        
        Args:
            include_metadata: 是否包含详细元数据
            
        Returns:
            Dict[str, Any]: 模型列表
        """
        models_info = {}
        
        for model_name, versions in self.models_registry.items():
            models_info[model_name] = {
                'versions': list(versions.keys()),
                'latest_version': self._get_latest_version(model_name),
                'total_versions': len(versions)
            }
            
            if include_metadata:
                models_info[model_name]['versions_metadata'] = {}
                for version, metadata in versions.items():
                    models_info[model_name]['versions_metadata'][version] = asdict(metadata)
        
        return models_info
    
    def delete_model(
        self,
        model_name: str,
        version: Optional[str] = None,
        create_backup: bool = True
    ) -> bool:
        """
        删除模型
        
        Args:
            model_name: 模型名称
            version: 版本号，如果为None则删除所有版本
            create_backup: 删除前是否创建备份
            
        Returns:
            bool: 是否成功删除
        """
        try:
            if version is None:
                # 删除所有版本
                if create_backup:
                    self._create_full_backup(model_name)
                
                model_dir = Path(self.config.base_dir) / model_name
                if model_dir.exists():
                    shutil.rmtree(model_dir)
                
                # 从注册表中移除
                if model_name in self.models_registry:
                    del self.models_registry[model_name]
                
                logger.info(f"已删除模型的所有版本: {model_name}")
            else:
                # 删除特定版本
                if create_backup:
                    self._create_backup(model_name, version)
                
                version_dir = Path(self.config.base_dir) / model_name / version
                if version_dir.exists():
                    shutil.rmtree(version_dir)
                
                # 从注册表中移除
                if model_name in self.models_registry and version in self.models_registry[model_name]:
                    del self.models_registry[model_name][version]
                    
                    # 如果没有其他版本，删除整个模型条目
                    if not self.models_registry[model_name]:
                        del self.models_registry[model_name]
                
                logger.info(f"已删除模型版本: {model_name} v{version}")
            
            # 保存更新的注册表
            self._save_registry()
            return True
            
        except Exception as e:
            logger.error(f"删除模型失败: {e}")
            return False
    
    def export_model(
        self,
        model_name: str,
        version: str,
        export_path: str,
        export_format: str = "zip",
        include_history: bool = True
    ) -> str:
        """
        导出模型到指定路径
        
        Args:
            model_name: 模型名称
            version: 版本号
            export_path: 导出路径
            export_format: 导出格式 (zip, tar, folder)
            include_history: 是否包含训练历史
            
        Returns:
            str: 导出文件路径
        """
        model_path = Path(self.config.base_dir) / model_name / version
        
        if not model_path.exists():
            raise FileNotFoundError(f"模型不存在: {model_path}")
        
        export_path = Path(export_path)
        export_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            if export_format == "zip":
                # 创建ZIP文件
                with zipfile.ZipFile(export_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for file_path in model_path.rglob('*'):
                        if file_path.is_file():
                            # 跳过训练历史（如果不需要）
                            if not include_history and 'training_history' in file_path.name:
                                continue
                            
                            arcname = file_path.relative_to(model_path)
                            zipf.write(file_path, arcname)
                
            elif export_format == "folder":
                # 复制到文件夹
                if export_path.exists():
                    shutil.rmtree(export_path)
                shutil.copytree(model_path, export_path)
                
                # 删除训练历史（如果不需要）
                if not include_history:
                    history_files = list(export_path.glob('*training_history*'))
                    for file_path in history_files:
                        file_path.unlink()
            
            else:
                raise ValueError(f"不支持的导出格式: {export_format}")
            
            logger.info(f"模型导出成功: {export_path}")
            return str(export_path)
            
        except Exception as e:
            logger.error(f"导出模型失败: {e}")
            raise
    
    def import_model(
        self,
        import_path: str,
        model_name: Optional[str] = None,
        version: Optional[str] = None,
        overwrite: bool = False
    ) -> str:
        """
        从文件导入模型
        
        Args:
            import_path: 导入路径
            model_name: 新的模型名称（可选）
            version: 新的版本号（可选）
            overwrite: 是否覆盖现有模型
            
        Returns:
            str: 导入后的模型路径
        """
        import_path = Path(import_path)
        
        if not import_path.exists():
            raise FileNotFoundError(f"导入文件不存在: {import_path}")
        
        # 创建临时目录
        temp_dir = Path(self.config.temp_dir) / f"import_{int(time.time())}"
        temp_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # 解压或复制文件
            if import_path.suffix == '.zip':
                with zipfile.ZipFile(import_path, 'r') as zipf:
                    zipf.extractall(temp_dir)
            elif import_path.is_dir():
                shutil.copytree(import_path, temp_dir / "model")
                temp_dir = temp_dir / "model"
            else:
                raise ValueError(f"不支持的导入格式: {import_path.suffix}")
            
            # 加载元数据
            metadata_file = temp_dir / "metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata_dict = json.load(f)
                metadata = ModelMetadata(**metadata_dict)
            else:
                raise FileNotFoundError("找不到模型元数据文件")
            
            # 使用提供的名称和版本，或使用原始值
            final_model_name = model_name or metadata.model_name
            final_version = version or metadata.version
            
            # 检查是否已存在
            target_path = Path(self.config.base_dir) / final_model_name / final_version
            if target_path.exists() and not overwrite:
                raise ValueError(f"模型已存在: {target_path}")
            
            # 移动到最终位置
            target_path.parent.mkdir(parents=True, exist_ok=True)
            if target_path.exists():
                shutil.rmtree(target_path)
            shutil.move(str(temp_dir), str(target_path))
            
            # 更新元数据
            metadata.model_name = final_model_name
            metadata.version = final_version
            self._save_metadata(metadata, target_path)
            
            # 更新注册表
            self._update_registry(final_model_name, final_version, metadata)
            
            logger.info(f"模型导入成功: {final_model_name} v{final_version}")
            return str(target_path)
            
        except Exception as e:
            logger.error(f"导入模型失败: {e}")
            raise
        finally:
            # 清理临时目录
            if temp_dir.exists():
                shutil.rmtree(temp_dir, ignore_errors=True)
    
    def compare_models(
        self,
        model1: Tuple[str, str],  # (name, version)
        model2: Tuple[str, str],  # (name, version)
        metrics: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        比较两个模型
        
        Args:
            model1: 第一个模型 (名称, 版本)
            model2: 第二个模型 (名称, 版本)
            metrics: 比较的指标列表
            
        Returns:
            Dict[str, Any]: 比较结果
        """
        # 获取模型元数据
        metadata1 = self._get_model_metadata(model1[0], model1[1])
        metadata2 = self._get_model_metadata(model2[0], model2[1])
        
        if not metadata1 or not metadata2:
            raise ValueError("无法获取模型元数据")
        
        comparison = {
            "model1": {
                "name": model1[0],
                "version": model1[1],
                "metadata": asdict(metadata1)
            },
            "model2": {
                "name": model2[0],
                "version": model2[1],
                "metadata": asdict(metadata2)
            },
            "comparison": {}
        }
        
        # 比较基本信息
        comparison["comparison"]["architecture"] = {
            "model1_params": metadata1.total_params,
            "model2_params": metadata2.total_params,
            "params_difference": metadata2.total_params - metadata1.total_params
        }
        
        # 比较性能指标
        if metrics is None:
            metrics = list(set(metadata1.performance_metrics.keys()) & 
                          set(metadata2.performance_metrics.keys()))
        
        comparison["comparison"]["performance"] = {}
        for metric in metrics:
            if metric in metadata1.performance_metrics and metric in metadata2.performance_metrics:
                val1 = metadata1.performance_metrics[metric]
                val2 = metadata2.performance_metrics[metric]
                comparison["comparison"]["performance"][metric] = {
                    "model1": val1,
                    "model2": val2,
                    "difference": val2 - val1,
                    "improvement": ((val2 - val1) / val1 * 100) if val1 != 0 else 0
                }
        
        return comparison
    
    def get_model_info(self, model_name: str, version: Optional[str] = None) -> Dict[str, Any]:
        """
        获取模型详细信息
        
        Args:
            model_name: 模型名称
            version: 版本号
            
        Returns:
            Dict[str, Any]: 模型信息
        """
        if version is None:
            version = self._get_latest_version(model_name)
        
        metadata = self._get_model_metadata(model_name, version)
        if not metadata:
            raise ValueError(f"模型不存在: {model_name} v{version}")
        
        model_path = Path(self.config.base_dir) / model_name / version
        
        # 获取文件信息
        files_info = []
        total_size = 0
        
        for file_path in model_path.rglob('*'):
            if file_path.is_file():
                size = file_path.stat().st_size
                total_size += size
                files_info.append({
                    "name": file_path.name,
                    "path": str(file_path.relative_to(model_path)),
                    "size": size,
                    "modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
                })
        
        return {
            "metadata": asdict(metadata),
            "files": files_info,
            "total_size": total_size,
            "model_path": str(model_path)
        }
    
    def _save_model_file(self, model: keras.Model, model_path: Path) -> Path:
        """保存模型文件"""
        if self.config.model_format == "h5":
            file_path = model_path / "model.h5"
            model.save(
                str(file_path),
                include_optimizer=self.config.include_optimizer,
                save_weights_only=self.config.save_weights_only
            )
        elif self.config.model_format == "savedmodel":
            file_path = model_path / "saved_model"
            model.save(str(file_path), save_format='tf')
        else:
            raise ValueError(f"不支持的模型格式: {self.config.model_format}")
        
        return file_path
    
    def _load_model_file(self, model_path: Path, custom_objects: Optional[Dict] = None) -> keras.Model:
        """加载模型文件"""
        if self.config.model_format == "h5":
            file_path = model_path / "model.h5"
            if self.config.save_weights_only:
                # 需要先构建模型架构，然后加载权重
                raise NotImplementedError("仅保存权重的模型需要提供模型架构")
            else:
                return keras.models.load_model(str(file_path), custom_objects=custom_objects)
        elif self.config.model_format == "savedmodel":
            file_path = model_path / "saved_model"
            return keras.models.load_model(str(file_path), custom_objects=custom_objects)
        else:
            raise ValueError(f"不支持的模型格式: {self.config.model_format}")
    
    def _save_scalers(self, scalers: Dict[str, Any], model_path: Path) -> Path:
        """保存预处理器"""
        scalers_path = model_path / "scalers.pkl"
        with open(scalers_path, 'wb') as f:
            pickle.dump(scalers, f)
        return scalers_path
    
    def _load_scalers(self, model_path: Path) -> Optional[Dict[str, Any]]:
        """加载预处理器"""
        scalers_path = model_path / "scalers.pkl"
        if scalers_path.exists():
            with open(scalers_path, 'rb') as f:
                return pickle.load(f)
        return None
    
    def _save_training_history(self, history: Dict, model_path: Path) -> Path:
        """保存训练历史"""
        history_path = model_path / "training_history.json"
        
        # 转换numpy数组为列表
        serializable_history = {}
        for key, value in history.items():
            if isinstance(value, np.ndarray):
                serializable_history[key] = value.tolist()
            elif isinstance(value, list):
                serializable_history[key] = value
            else:
                serializable_history[key] = str(value)
        
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump(serializable_history, f, indent=2, ensure_ascii=False)
        
        return history_path
    
    def _load_training_history(self, model_path: Path) -> Optional[Dict]:
        """加载训练历史"""
        history_path = model_path / "training_history.json"
        if history_path.exists():
            with open(history_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
    
    def _create_metadata(
        self,
        model: keras.Model,
        model_name: str,
        version: str,
        additional_metadata: Optional[Dict[str, Any]] = None,
        training_history: Optional[Dict] = None
    ) -> ModelMetadata:
        """创建模型元数据"""
        # 基本信息
        metadata = ModelMetadata(
            model_name=model_name,
            version=version,
            created_date=datetime.now().isoformat(),
            input_shape=model.input_shape,
            output_shape=model.output_shape,
            total_params=model.count_params(),
            trainable_params=sum([tf.keras.backend.count_params(w) for w in model.trainable_weights])
        )
        
        # 从训练历史中提取信息
        if training_history:
            if 'loss' in training_history:
                metadata.training_epochs = len(training_history['loss'])
            
            # 提取性能指标
            for key, values in training_history.items():
                if key.startswith('val_') and isinstance(values, (list, np.ndarray)):
                    metric_name = key[4:]  # 移除 'val_' 前缀
                    if len(values) > 0:
                        metadata.performance_metrics[metric_name] = float(values[-1])
        
        # 添加额外元数据
        if additional_metadata:
            if 'hyperparameters' in additional_metadata:
                metadata.hyperparameters = additional_metadata['hyperparameters']
            if 'preprocessing_info' in additional_metadata:
                metadata.preprocessing_info = additional_metadata['preprocessing_info']
            if 'tags' in additional_metadata:
                metadata.tags = additional_metadata['tags']
            if 'description' in additional_metadata:
                metadata.description = additional_metadata['description']
            if 'author' in additional_metadata:
                metadata.author = additional_metadata['author']
        
        return metadata
    
    def _save_metadata(self, metadata: ModelMetadata, model_path: Path) -> Path:
        """保存元数据"""
        metadata_path = model_path / "metadata.json"
        
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(asdict(metadata), f, indent=2, ensure_ascii=False, default=str)
        
        return metadata_path
    
    def _load_metadata(self, model_path: Path) -> ModelMetadata:
        """加载元数据"""
        metadata_path = model_path / "metadata.json"
        
        if not metadata_path.exists():
            raise FileNotFoundError(f"元数据文件不存在: {metadata_path}")
        
        with open(metadata_path, 'r', encoding='utf-8') as f:
            metadata_dict = json.load(f)
        
        return ModelMetadata(**metadata_dict)
    
    def _calculate_model_hash(self, model_file_path: Path) -> str:
        """计算模型文件哈希"""
        hash_md5 = hashlib.md5()
        
        with open(model_file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        
        return hash_md5.hexdigest()
    
    def _validate_model_integrity(self, model_path: Path, metadata: ModelMetadata):
        """验证模型完整性"""
        if not metadata.model_hash:
            logger.warning("模型哈希不存在，跳过完整性验证")
            return
        
        model_file = model_path / "model.h5" if self.config.model_format == "h5" else model_path / "saved_model"
        
        if not model_file.exists():
            raise FileNotFoundError(f"模型文件不存在: {model_file}")
        
        current_hash = self._calculate_model_hash(model_file)
        
        if current_hash != metadata.model_hash:
            raise ValueError("模型文件完整性验证失败")
    
    def _generate_version(self, model_name: str) -> str:
        """生成版本号"""
        if model_name not in self.models_registry:
            return "1.0.0"
        
        versions = list(self.models_registry[model_name].keys())
        
        # 解析版本号并找到最大值
        max_version = [0, 0, 0]
        for version in versions:
            try:
                parts = [int(x) for x in version.split('.')]
                if len(parts) == 3:
                    if parts > max_version:
                        max_version = parts
            except ValueError:
                continue
        
        # 增加次版本号
        max_version[1] += 1
        return f"{max_version[0]}.{max_version[1]}.{max_version[2]}"
    
    def _get_latest_version(self, model_name: str) -> str:
        """获取最新版本"""
        if model_name not in self.models_registry:
            raise ValueError(f"模型不存在: {model_name}")
        
        versions = list(self.models_registry[model_name].keys())
        
        # 按版本号排序
        def version_key(version):
            try:
                return tuple(int(x) for x in version.split('.'))
            except ValueError:
                return (0, 0, 0)
        
        versions.sort(key=version_key, reverse=True)
        return versions[0]
    
    def _get_model_metadata(self, model_name: str, version: str) -> Optional[ModelMetadata]:
        """获取模型元数据"""
        if model_name in self.models_registry and version in self.models_registry[model_name]:
            return self.models_registry[model_name][version]
        return None
    
    def _update_registry(self, model_name: str, version: str, metadata: ModelMetadata):
        """更新模型注册表"""
        if model_name not in self.models_registry:
            self.models_registry[model_name] = {}
        
        self.models_registry[model_name][version] = metadata
        self._save_registry()
    
    def _load_registry(self):
        """加载模型注册表"""
        registry_path = Path(self.config.base_dir) / "registry.json"
        
        if registry_path.exists():
            try:
                with open(registry_path, 'r', encoding='utf-8') as f:
                    registry_data = json.load(f)
                
                # 重建注册表
                for model_name, versions in registry_data.items():
                    self.models_registry[model_name] = {}
                    for version, metadata_dict in versions.items():
                        self.models_registry[model_name][version] = ModelMetadata(**metadata_dict)
                
                logger.info(f"已加载模型注册表，包含 {len(self.models_registry)} 个模型")
                
            except Exception as e:
                logger.warning(f"加载注册表失败: {e}")
                self.models_registry = {}
        else:
            self.models_registry = {}
    
    def _save_registry(self):
        """保存模型注册表"""
        registry_path = Path(self.config.base_dir) / "registry.json"
        
        # 转换为可序列化的格式
        registry_data = {}
        for model_name, versions in self.models_registry.items():
            registry_data[model_name] = {}
            for version, metadata in versions.items():
                registry_data[model_name][version] = asdict(metadata)
        
        with open(registry_path, 'w', encoding='utf-8') as f:
            json.dump(registry_data, f, indent=2, ensure_ascii=False, default=str)
    
    def _cleanup_old_versions(self, model_name: str):
        """清理旧版本"""
        if model_name not in self.models_registry:
            return
        
        versions = list(self.models_registry[model_name].keys())
        
        if len(versions) <= self.config.max_versions:
            return
        
        # 按版本号排序，保留最新的版本
        def version_key(version):
            try:
                return tuple(int(x) for x in version.split('.'))
            except ValueError:
                return (0, 0, 0)
        
        versions.sort(key=version_key, reverse=True)
        versions_to_delete = versions[self.config.max_versions:]
        
        for version in versions_to_delete:
            logger.info(f"清理旧版本: {model_name} v{version}")
            self.delete_model(model_name, version, create_backup=False)
    
    def _create_backup(self, model_name: str, version: str):
        """创建模型备份"""
        source_path = Path(self.config.base_dir) / model_name / version
        backup_path = Path(self.config.backup_dir) / model_name / f"{version}_{int(time.time())}"
        
        if source_path.exists():
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copytree(source_path, backup_path)
            logger.info(f"已创建备份: {backup_path}")
    
    def _create_full_backup(self, model_name: str):
        """创建完整模型备份"""
        source_path = Path(self.config.base_dir) / model_name
        backup_path = Path(self.config.backup_dir) / f"{model_name}_full_{int(time.time())}"
        
        if source_path.exists():
            shutil.copytree(source_path, backup_path)
            logger.info(f"已创建完整备份: {backup_path}")
    
    def _create_compressed_archive(self, model_path: Path):
        """创建压缩归档"""
        archive_path = model_path.with_suffix('.zip')
        
        with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in model_path.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(model_path)
                    zipf.write(file_path, arcname)
        
        logger.info(f"已创建压缩归档: {archive_path}")


class ModelVersionManager:
    """模型版本管理器"""
    
    def __init__(self, base_dir: str = "models"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
    
    def create_branch(self, model_name: str, base_version: str, branch_name: str) -> str:
        """
        创建模型分支
        
        Args:
            model_name: 模型名称
            base_version: 基础版本
            branch_name: 分支名称
            
        Returns:
            str: 分支版本号
        """
        base_path = self.base_dir / model_name / base_version
        if not base_path.exists():
            raise FileNotFoundError(f"基础版本不存在: {base_path}")
        
        branch_version = f"{base_version}-{branch_name}"
        branch_path = self.base_dir / model_name / branch_version
        
        # 复制基础版本到分支
        shutil.copytree(base_path, branch_path)
        
        # 更新元数据
        metadata_path = branch_path / "metadata.json"
        if metadata_path.exists():
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            metadata['version'] = branch_version
            metadata['branch_info'] = {
                'base_version': base_version,
                'branch_name': branch_name,
                'created_date': datetime.now().isoformat()
            }
            
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        logger.info(f"已创建分支: {model_name} v{branch_version}")
        return branch_version
    
    def merge_branch(self, model_name: str, source_branch: str, target_version: str) -> str:
        """
        合并分支
        
        Args:
            model_name: 模型名称
            source_branch: 源分支版本
            target_version: 目标版本
            
        Returns:
            str: 合并后的版本号
        """
        # 这里可以实现更复杂的合并逻辑
        # 简单实现：复制源分支到新版本
        source_path = self.base_dir / model_name / source_branch
        target_path = self.base_dir / model_name / target_version
        
        if not source_path.exists():
            raise FileNotFoundError(f"源分支不存在: {source_path}")
        
        if target_path.exists():
            raise ValueError(f"目标版本已存在: {target_path}")
        
        shutil.copytree(source_path, target_path)
        
        # 更新元数据
        metadata_path = target_path / "metadata.json"
        if metadata_path.exists():
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            metadata['version'] = target_version
            metadata['merge_info'] = {
                'source_branch': source_branch,
                'merged_date': datetime.now().isoformat()
            }
            
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        logger.info(f"已合并分支: {source_branch} -> {target_version}")
        return target_version


class ModelDeploymentManager:
    """模型部署管理器"""
    
    def __init__(self, deployment_dir: str = "deployments"):
        self.deployment_dir = Path(deployment_dir)
        self.deployment_dir.mkdir(parents=True, exist_ok=True)
    
    def prepare_for_deployment(
        self,
        model_path: str,
        deployment_name: str,
        optimization_config: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        准备模型部署
        
        Args:
            model_path: 模型路径
            deployment_name: 部署名称
            optimization_config: 优化配置
            
        Returns:
            str: 部署包路径
        """
        model_path = Path(model_path)
        deployment_path = self.deployment_dir / deployment_name
        deployment_path.mkdir(parents=True, exist_ok=True)
        
        # 复制模型文件
        shutil.copytree(model_path, deployment_path / "model")
        
        # 创建部署配置
        deployment_config = {
            "deployment_name": deployment_name,
            "created_date": datetime.now().isoformat(),
            "model_source": str(model_path),
            "optimization_config": optimization_config or {}
        }
        
        config_path = deployment_path / "deployment_config.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(deployment_config, f, indent=2, ensure_ascii=False)
        
        # 创建部署脚本
        self._create_deployment_scripts(deployment_path)
        
        logger.info(f"部署包已准备: {deployment_path}")
        return str(deployment_path)
    
    def _create_deployment_scripts(self, deployment_path: Path):
        """创建部署脚本"""
        # 创建Python部署脚本
        deploy_script = deployment_path / "deploy.py"
        script_content = '''#!/usr/bin/env python3
"""
模型部署脚本
"""

import os
import json
import tensorflow as tf
from pathlib import Path

def load_model():
    """加载模型"""
    model_path = Path(__file__).parent / "model" / "model.h5"
    return tf.keras.models.load_model(str(model_path))

def predict(model, input_data):
    """预测函数"""
    return model.predict(input_data)

if __name__ == "__main__":
    # 加载模型
    model = load_model()
    print(f"模型已加载，参数数量: {model.count_params()}")
    
    # 示例预测
    import numpy as np
    sample_input = np.random.randn(1, *model.input_shape[1:])
    prediction = predict(model, sample_input)
    print(f"示例预测结果: {prediction}")
'''
        
        with open(deploy_script, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # 创建Docker文件
        dockerfile = deployment_path / "Dockerfile"
        dockerfile_content = '''FROM tensorflow/tensorflow:2.12.0

WORKDIR /app

COPY . /app/

RUN pip install --no-cache-dir -r requirements.txt

EXPOSE 8080

CMD ["python", "deploy.py"]
'''
        
        with open(dockerfile, 'w', encoding='utf-8') as f:
            f.write(dockerfile_content)
        
        # 创建requirements.txt
        requirements = deployment_path / "requirements.txt"
        requirements_content = '''tensorflow==2.12.0
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0
'''
        
        with open(requirements, 'w', encoding='utf-8') as f:
            f.write(requirements_content)


def create_model_manager(config_dict: Dict[str, Any]) -> ModelManager:
    """
    创建模型管理器的工厂函数
    
    Args:
        config_dict: 配置字典
        
    Returns:
        ModelManager: 模型管理器实例
    """
    config = ModelManagerConfig(**config_dict)
    return ModelManager(config)


if __name__ == "__main__":
    # 示例用法
    config = ModelManagerConfig(
        base_dir="models/test",
        max_versions=5,
        auto_backup=True,
        compression=True
    )
    
    manager = ModelManager(config)
    
    print("模型管理器创建成功")
    print(f"基础目录: {config.base_dir}")
    print(f"最大版本数: {config.max_versions}")
    print(f"自动备份: {config.auto_backup}")
    print(f"压缩存储: {config.compression}")
