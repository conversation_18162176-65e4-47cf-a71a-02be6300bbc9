"""
深度神经网络训练集成测试
"""

import pytest
import numpy as np
import tempfile
import os
import shutil
from unittest.mock import Mock, patch
import tensorflow as tf
from tensorflow import keras

from src.calibration.neural_network import CalibrationDNN, NetworkConfig
from src.calibration.training_data_generator import (
    TrainingDataGenerator, DataGenerationConfig
)
from src.calibration.training_monitor import TrainingMonitor
from src.calibration.model_evaluator import ModelEvaluator
from src.calibration.model_manager import ModelManager, ModelManagerConfig


class TestDNNTrainingIntegration:
    """深度神经网络训练集成测试类"""
    
    def setup_method(self):
        """测试前设置"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        # 设置随机种子
        np.random.seed(42)
        tf.random.set_seed(42)
        
        # 创建合成模拟引擎
        self.simulation_engine = TrainingDataGenerator.create_synthetic_simulation_engine()
        
        # 创建数据生成配置
        self.data_config = DataGenerationConfig(
            n_samples=1000,  # 减少样本数以加快测试
            batch_size=50,
            cache_dir=os.path.join(self.temp_dir, "cache"),
            use_cache=False,
            random_seed=42
        )
        
        # 创建网络配置
        self.network_config = NetworkConfig(
            input_dim=20,
            output_dim=24,
            architecture='feedforward',
            layers=[128, 64, 32],
            dropout_rate=0.1,
            learning_rate=0.01
        )
        
        # 创建模型管理配置
        self.manager_config = ModelManagerConfig(
            base_dir=os.path.join(self.temp_dir, "models"),
            backup_dir=os.path.join(self.temp_dir, "backups"),
            temp_dir=os.path.join(self.temp_dir, "temp")
        )
    
    def teardown_method(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_complete_training_pipeline(self):
        """测试完整的训练流程"""
        # 1. 生成训练数据
        data_generator = TrainingDataGenerator(
            config=self.data_config,
            simulation_engine=self.simulation_engine
        )
        
        dataset = data_generator.generate_training_data()
        
        # 验证数据集
        assert dataset.X_train.shape[0] > 0
        assert dataset.X_val.shape[0] > 0
        assert dataset.X_test.shape[0] > 0
        assert dataset.y_train.shape[1] == 24
        
        # 2. 构建神经网络
        dnn = CalibrationDNN(self.network_config)
        model = dnn.build_model()
        
        # 验证模型
        assert model is not None
        assert model.input_shape == (None, 20)
        assert model.output_shape == (None, 24)
        
        # 3. 设置训练监控
        monitor = TrainingMonitor(
            patience=5,
            min_delta=0.001,
            monitor='val_loss',
            verbose=0
        )
        
        callbacks = monitor.create_callbacks(
            model_save_path=os.path.join(self.temp_dir, "best_model.h5")
        )
        
        # 4. 训练模型
        monitor.start_monitoring()
        
        history = model.fit(
            dataset.X_train, dataset.y_train,
            validation_data=(dataset.X_val, dataset.y_val),
            epochs=5,  # 减少epoch数以加快测试
            batch_size=32,
            callbacks=callbacks,
            verbose=0
        )
        
        monitor.stop_monitoring()
        
        # 验证训练历史
        assert 'loss' in history.history
        assert 'val_loss' in history.history
        assert len(history.history['loss']) > 0
        
        # 5. 评估模型
        evaluator = ModelEvaluator(
            model=model,
            input_scaler=dataset.input_scaler,
            output_scaler=dataset.output_scaler,
            target_names=dataset.target_names
        )
        
        evaluation_results = evaluator.evaluate_comprehensive(
            X_test=dataset.X_test,
            y_test=dataset.y_test,
            X_train=dataset.X_train,
            y_train=dataset.y_train,
            detailed=False  # 简化评估以加快测试
        )
        
        # 验证评估结果
        assert 'test_mse' in evaluation_results
        assert 'test_mae' in evaluation_results
        assert 'test_r2' in evaluation_results
        assert evaluation_results['test_mse'] >= 0
        
        # 6. 保存模型
        model_manager = ModelManager(self.manager_config)
        
        model_info = model_manager.save_model(
            model=model,
            model_name="integration_test_model",
            version="1.0",
            metadata={
                'description': 'Integration test model',
                'performance_metrics': {
                    'test_mse': float(evaluation_results['test_mse']),
                    'test_mae': float(evaluation_results['test_mae'])
                }
            },
            scalers={
                'input_scaler': dataset.input_scaler,
                'output_scaler': dataset.output_scaler
            }
        )
        
        # 验证模型保存
        assert model_info is not None
        assert os.path.exists(model_info['model_path'])
        
        # 7. 加载并验证模型
        loaded_result = model_manager.load_model(
            "integration_test_model", "1.0"
        )
        
        assert loaded_result is not None
        loaded_model = loaded_result['model']
        
        # 验证加载的模型可以预测
        test_prediction = loaded_model.predict(dataset.X_test[:5], verbose=0)
        assert test_prediction.shape == (5, 24)
        assert not np.isnan(test_prediction).any()
    
    def test_different_architectures(self):
        """测试不同的网络架构"""
        architectures = ['feedforward', 'residual', 'ensemble']
        
        # 生成一小批测试数据
        data_generator = TrainingDataGenerator(
            config=DataGenerationConfig(
                n_samples=200,
                use_cache=False,
                random_seed=42
            ),
            simulation_engine=self.simulation_engine
        )
        dataset = data_generator.generate_training_data()
        
        for arch in architectures:
            # 创建网络配置
            config = NetworkConfig(
                input_dim=20,
                output_dim=24,
                architecture=arch,
                layers=[64, 32],  # 简化架构以加快测试
                learning_rate=0.01
            )
            
            # 构建和训练模型
            dnn = CalibrationDNN(config)
            model = dnn.build_model()
            
            # 简单训练
            history = model.fit(
                dataset.X_train, dataset.y_train,
                validation_data=(dataset.X_val, dataset.y_val),
                epochs=2,
                batch_size=16,
                verbose=0
            )
            
            # 验证训练成功
            assert len(history.history['loss']) == 2
            assert not np.isnan(history.history['loss']).any()
            
            # 验证预测
            predictions = model.predict(dataset.X_test[:10], verbose=0)
            assert predictions.shape == (10, 24)
            assert not np.isnan(predictions).any()
    
    def test_training_with_early_stopping(self):
        """测试早停机制"""
        # 生成训练数据
        data_generator = TrainingDataGenerator(
            config=DataGenerationConfig(
                n_samples=500,
                use_cache=False,
                random_seed=42
            ),
            simulation_engine=self.simulation_engine
        )
        dataset = data_generator.generate_training_data()
        
        # 构建模型
        dnn = CalibrationDNN(self.network_config)
        model = dnn.build_model()
        
        # 设置早停监控
        monitor = TrainingMonitor(
            patience=3,
            min_delta=0.001,
            monitor='val_loss',
            verbose=0
        )
        
        callbacks = monitor.create_callbacks(
            model_save_path=os.path.join(self.temp_dir, "early_stop_model.h5")
        )
        
        # 训练模型（设置较多epoch以触发早停）
        history = model.fit(
            dataset.X_train, dataset.y_train,
            validation_data=(dataset.X_val, dataset.y_val),
            epochs=50,  # 设置较多epoch
            batch_size=32,
            callbacks=callbacks,
            verbose=0
        )
        
        # 验证早停生效（实际训练的epoch数应该少于50）
        actual_epochs = len(history.history['loss'])
        assert actual_epochs < 50
        assert actual_epochs >= 3  # 至少训练了几个epoch
    
    def test_model_comparison(self):
        """测试模型比较"""
        # 生成训练数据
        data_generator = TrainingDataGenerator(
            config=DataGenerationConfig(
                n_samples=300,
                use_cache=False,
                random_seed=42
            ),
            simulation_engine=self.simulation_engine
        )
        dataset = data_generator.generate_training_data()
        
        # 创建两个不同的模型
        config1 = NetworkConfig(
            input_dim=20, output_dim=24,
            layers=[64, 32], learning_rate=0.01
        )
        config2 = NetworkConfig(
            input_dim=20, output_dim=24,
            layers=[128, 64], learning_rate=0.005
        )
        
        models = []
        for i, config in enumerate([config1, config2]):
            dnn = CalibrationDNN(config)
            model = dnn.build_model()
            
            # 简单训练
            model.fit(
                dataset.X_train, dataset.y_train,
                validation_data=(dataset.X_val, dataset.y_val),
                epochs=3,
                batch_size=16,
                verbose=0
            )
            
            models.append(model)
        
        # 比较模型性能
        evaluator1 = ModelEvaluator(models[0])
        evaluator2 = ModelEvaluator(models[1])
        
        comparison = evaluator1.compare_models(
            evaluator2, dataset.X_test, dataset.y_test
        )
        
        # 验证比较结果
        assert 'model1_metrics' in comparison
        assert 'model2_metrics' in comparison
        assert 'comparison_summary' in comparison
        
        # 验证指标存在
        assert 'mse' in comparison['model1_metrics']
        assert 'mse' in comparison['model2_metrics']
    
    def test_model_versioning(self):
        """测试模型版本管理"""
        # 生成训练数据
        data_generator = TrainingDataGenerator(
            config=DataGenerationConfig(
                n_samples=200,
                use_cache=False,
                random_seed=42
            ),
            simulation_engine=self.simulation_engine
        )
        dataset = data_generator.generate_training_data()
        
        # 创建模型管理器
        model_manager = ModelManager(self.manager_config)
        
        # 训练和保存多个版本的模型
        model_name = "versioning_test_model"
        versions = ["1.0", "1.1", "2.0"]
        
        for version in versions:
            # 构建模型
            dnn = CalibrationDNN(self.network_config)
            model = dnn.build_model()
            
            # 简单训练
            model.fit(
                dataset.X_train, dataset.y_train,
                epochs=2,
                batch_size=16,
                verbose=0
            )
            
            # 保存模型
            model_info = model_manager.save_model(
                model=model,
                model_name=model_name,
                version=version,
                metadata={'version_info': f'Version {version}'}
            )
            
            assert model_info is not None
        
        # 验证版本列表
        versions_list = model_manager.list_model_versions(model_name)
        assert len(versions_list) == 3
        
        version_numbers = [v['version'] for v in versions_list]
        for version in versions:
            assert version in version_numbers
        
        # 验证加载特定版本
        loaded_result = model_manager.load_model(model_name, "1.1")
        assert loaded_result is not None
        assert loaded_result['metadata'].version == "1.1"
        
        # 验证加载最新版本
        latest_result = model_manager.load_model(model_name)
        assert latest_result is not None
        assert latest_result['metadata'].version == "2.0"  # 应该是最新版本
    
    def test_training_with_custom_loss(self):
        """测试自定义损失函数训练"""
        # 生成训练数据
        data_generator = TrainingDataGenerator(
            config=DataGenerationConfig(
                n_samples=300,
                use_cache=False,
                random_seed=42
            ),
            simulation_engine=self.simulation_engine
        )
        dataset = data_generator.generate_training_data()
        
        # 创建带权重的网络配置
        config = NetworkConfig(
            input_dim=20,
            output_dim=24,
            layers=[64, 32],
            target_weights=[1.0] * 24  # 所有目标等权重
        )
        
        # 构建模型
        dnn = CalibrationDNN(config)
        model = dnn.build_model()
        
        # 训练模型
        history = model.fit(
            dataset.X_train, dataset.y_train,
            validation_data=(dataset.X_val, dataset.y_val),
            epochs=3,
            batch_size=16,
            verbose=0
        )
        
        # 验证训练成功
        assert len(history.history['loss']) == 3
        assert not np.isnan(history.history['loss']).any()
        
        # 验证预测
        predictions = model.predict(dataset.X_test[:5], verbose=0)
        assert predictions.shape == (5, 24)
        assert not np.isnan(predictions).any()


if __name__ == "__main__":
    pytest.main([__file__])
