"""
深度神经网络模块单元测试
"""

import pytest
import numpy as np
import tensorflow as tf
from unittest.mock import patch, MagicMock
import tempfile
import os

from src.calibration.neural_network import CalibrationDNN, NetworkConfig


class TestNetworkConfig:
    """NetworkConfig测试类"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = NetworkConfig(input_dim=10, output_dim=5)
        
        assert config.input_dim == 10
        assert config.output_dim == 5
        assert config.architecture == 'feedforward'
        assert config.layers == [256, 128, 64]
        assert config.activation == 'relu'
        assert config.dropout_rate == 0.2
        assert config.learning_rate == 0.001
        assert config.batch_normalization is True
        assert config.l2_regularization == 0.001
        assert config.target_weights is None
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = NetworkConfig(
            input_dim=20,
            output_dim=10,
            architecture='residual',
            layers=[512, 256, 128],
            activation='tanh',
            dropout_rate=0.3,
            learning_rate=0.01,
            batch_normalization=False,
            l2_regularization=0.01,
            target_weights=[1.0, 2.0, 1.5]
        )
        
        assert config.input_dim == 20
        assert config.output_dim == 10
        assert config.architecture == 'residual'
        assert config.layers == [512, 256, 128]
        assert config.activation == 'tanh'
        assert config.dropout_rate == 0.3
        assert config.learning_rate == 0.01
        assert config.batch_normalization is False
        assert config.l2_regularization == 0.01
        assert config.target_weights == [1.0, 2.0, 1.5]


class TestCalibrationDNN:
    """CalibrationDNN测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.config = NetworkConfig(input_dim=10, output_dim=5)
        self.dnn = CalibrationDNN(self.config)
    
    def test_initialization(self):
        """测试初始化"""
        assert self.dnn.config == self.config
        assert self.dnn.model is None
        assert self.dnn.history is None
        assert self.dnn.logger is not None
    
    def test_validate_config_valid(self):
        """测试有效配置验证"""
        # 不应该抛出异常
        self.dnn._validate_config()
    
    def test_validate_config_invalid_input_dim(self):
        """测试无效输入维度"""
        config = NetworkConfig(input_dim=0, output_dim=5)
        dnn = CalibrationDNN(config)
        
        with pytest.raises(ValueError, match="输入维度必须大于0"):
            dnn._validate_config()
    
    def test_validate_config_invalid_output_dim(self):
        """测试无效输出维度"""
        config = NetworkConfig(input_dim=10, output_dim=-1)
        dnn = CalibrationDNN(config)
        
        with pytest.raises(ValueError, match="输出维度必须大于0"):
            dnn._validate_config()
    
    def test_validate_config_empty_layers(self):
        """测试空层配置"""
        config = NetworkConfig(input_dim=10, output_dim=5, layers=[])
        dnn = CalibrationDNN(config)
        
        with pytest.raises(ValueError, match="网络层配置不能为空"):
            dnn._validate_config()
    
    def test_validate_config_invalid_dropout(self):
        """测试无效dropout率"""
        config = NetworkConfig(input_dim=10, output_dim=5, dropout_rate=1.5)
        dnn = CalibrationDNN(config)
        
        with pytest.raises(ValueError, match="Dropout率必须在"):
            dnn._validate_config()
    
    def test_build_feedforward_model(self):
        """测试构建前馈网络"""
        model = self.dnn.build_model()
        
        assert model is not None
        assert self.dnn.model is model
        assert model.name == 'calibration_dnn'
        
        # 检查输入输出形状
        assert model.input_shape == (None, 10)
        assert model.output_shape == (None, 5)
        
        # 检查层数（输入层 + 隐藏层 + 输出层 + BN + Dropout）
        assert len(model.layers) > 3
    
    def test_build_residual_model(self):
        """测试构建残差网络"""
        config = NetworkConfig(
            input_dim=10,
            output_dim=5,
            architecture='residual',
            layers=[64, 64, 32]
        )
        dnn = CalibrationDNN(config)
        model = dnn.build_model()
        
        assert model is not None
        assert model.name == 'residual_dnn'
        assert model.input_shape == (None, 10)
        assert model.output_shape == (None, 5)
    
    def test_build_ensemble_model(self):
        """测试构建集成网络"""
        config = NetworkConfig(
            input_dim=10,
            output_dim=5,
            architecture='ensemble',
            layers=[64, 32]
        )
        dnn = CalibrationDNN(config)
        model = dnn.build_model()
        
        assert model is not None
        assert model.name == 'ensemble_dnn'
        assert model.input_shape == (None, 10)
        assert model.output_shape == (None, 5)
    
    def test_build_invalid_architecture(self):
        """测试无效架构"""
        config = NetworkConfig(
            input_dim=10,
            output_dim=5,
            architecture='invalid'
        )
        dnn = CalibrationDNN(config)
        
        with pytest.raises(ValueError, match="不支持的网络架构"):
            dnn.build_model()
    
    def test_model_compilation(self):
        """测试模型编译"""
        model = self.dnn.build_model()
        
        # 检查优化器
        assert isinstance(model.optimizer, tf.keras.optimizers.Adam)
        assert model.optimizer.learning_rate.numpy() == 0.001
        
        # 检查损失函数
        assert model.loss == 'mse'
        
        # 检查指标
        assert 'mae' in [m.name for m in model.metrics]
        assert 'mse' in [m.name for m in model.metrics]
    
    def test_weighted_loss_compilation(self):
        """测试加权损失编译"""
        config = NetworkConfig(
            input_dim=10,
            output_dim=3,
            target_weights=[1.0, 2.0, 1.5]
        )
        dnn = CalibrationDNN(config)
        model = dnn.build_model()
        
        # 检查模型已编译
        assert model.optimizer is not None
        assert model.loss is not None
    
    def test_get_model_summary_no_model(self):
        """测试未构建模型时的摘要"""
        summary = self.dnn.get_model_summary()
        assert summary == "模型尚未构建"
    
    def test_get_model_summary_with_model(self):
        """测试已构建模型的摘要"""
        self.dnn.build_model()
        summary = self.dnn.get_model_summary()
        
        assert "Model:" in summary
        assert "Total params:" in summary
        assert "Trainable params:" in summary
    
    @patch('tensorflow.keras.utils.plot_model')
    def test_visualize_architecture_success(self, mock_plot_model):
        """测试架构可视化成功"""
        self.dnn.build_model()
        
        result = self.dnn.visualize_architecture("test_arch.png")
        
        mock_plot_model.assert_called_once()
        assert result == "test_arch.png"
    
    def test_visualize_architecture_no_model(self):
        """测试未构建模型时的可视化"""
        with pytest.raises(ValueError, match="模型尚未构建"):
            self.dnn.visualize_architecture()
    
    @patch('tensorflow.keras.utils.plot_model')
    def test_visualize_architecture_exception(self, mock_plot_model):
        """测试可视化异常处理"""
        mock_plot_model.side_effect = Exception("Test error")
        self.dnn.build_model()
        
        result = self.dnn.visualize_architecture()
        assert result == ""
    
    def test_create_calibration_config_small(self):
        """测试小参数集的配置创建"""
        config = CalibrationDNN.create_calibration_config(5, 3)
        
        assert config.input_dim == 5
        assert config.output_dim == 3
        assert config.layers == [256, 128, 64]
        assert config.architecture == 'feedforward'
    
    def test_create_calibration_config_medium(self):
        """测试中等参数集的配置创建"""
        config = CalibrationDNN.create_calibration_config(30, 10)
        
        assert config.input_dim == 30
        assert config.output_dim == 10
        assert config.layers == [512, 256, 128, 64]
    
    def test_create_calibration_config_large(self):
        """测试大参数集的配置创建"""
        config = CalibrationDNN.create_calibration_config(100, 50)
        
        assert config.input_dim == 100
        assert config.output_dim == 50
        assert config.layers == [1024, 512, 256, 128, 64]
    
    def test_create_calibration_config_custom_architecture(self):
        """测试自定义架构的配置创建"""
        config = CalibrationDNN.create_calibration_config(
            20, 10, architecture='residual'
        )
        
        assert config.architecture == 'residual'
        assert config.input_dim == 20
        assert config.output_dim == 10
    
    def test_model_prediction(self):
        """测试模型预测"""
        model = self.dnn.build_model()
        
        # 创建测试数据
        X_test = np.random.randn(10, 10)
        
        # 进行预测
        predictions = model.predict(X_test, verbose=0)
        
        assert predictions.shape == (10, 5)
        assert not np.isnan(predictions).any()
    
    def test_model_training(self):
        """测试模型训练"""
        model = self.dnn.build_model()
        
        # 创建训练数据
        X_train = np.random.randn(100, 10)
        y_train = np.random.randn(100, 5)
        
        # 训练模型
        history = model.fit(
            X_train, y_train,
            epochs=2,
            batch_size=32,
            verbose=0
        )
        
        assert 'loss' in history.history
        assert 'mae' in history.history
        assert len(history.history['loss']) == 2


if __name__ == "__main__":
    pytest.main([__file__])
