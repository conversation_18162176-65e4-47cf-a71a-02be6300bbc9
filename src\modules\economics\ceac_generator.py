"""
成本效益可接受曲线（CEAC）生成器

该模块实现了健康经济学分析中的CEAC生成功能，包括：
- 成本效益可接受曲线生成
- 概率敏感性分析
- 多策略CEAC比较
- CEAC数据导出功能
- CEAC图表生成和格式化
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Union
from enum import Enum
import numpy as np
import pandas as pd
from scipy import stats
import logging

logger = logging.getLogger(__name__)


class CEACAnalysisType(Enum):
    """CEAC分析类型枚举"""
    SINGLE_STRATEGY = "single_strategy"     # 单策略分析
    MULTI_STRATEGY = "multi_strategy"       # 多策略比较
    PAIRWISE = "pairwise"                   # 成对比较


@dataclass
class CEACPoint:
    """CEAC曲线上的单个数据点"""
    wtp_threshold: float
    probability_cost_effective: float
    net_monetary_benefit: float
    strategy_name: str


@dataclass
class CEACResult:
    """CEAC分析结果数据类"""
    strategy_name: str
    wtp_thresholds: List[float]
    probabilities: List[float]
    analysis_type: CEACAnalysisType
    sample_size: int
    confidence_intervals: Optional[List[Tuple[float, float]]] = None
    optimal_threshold: Optional[float] = None
    threshold_at_50_percent: Optional[float] = None


@dataclass
class MultiStrategyCEACResult:
    """多策略CEAC分析结果"""
    strategies: List[str]
    wtp_thresholds: List[float]
    strategy_probabilities: Dict[str, List[float]]
    dominant_strategy_by_threshold: List[str]
    analysis_summary: Dict[str, Union[str, float, int]]


class CEACGenerator:
    """成本效益可接受曲线生成器"""
    
    def __init__(self, wtp_range: Tuple[float, float] = (0, 500000), n_points: int = 100):
        """
        初始化CEAC生成器
        
        Args:
            wtp_range: 支付意愿阈值范围（元/QALY）
            n_points: 分析点数
        """
        self.wtp_range = wtp_range
        self.n_points = n_points
        self.wtp_thresholds = np.linspace(wtp_range[0], wtp_range[1], n_points)
        logger.info(f"CEAC生成器初始化完成，阈值范围: {wtp_range[0]:,.0f}-{wtp_range[1]:,.0f}元/QALY")
    
    def generate_single_strategy_ceac(
        self,
        cost_samples: np.ndarray,
        qaly_samples: np.ndarray,
        reference_cost: float,
        reference_qalys: float,
        strategy_name: str = "intervention",
        include_confidence_intervals: bool = True
    ) -> CEACResult:
        """
        生成单策略CEAC曲线
        
        Args:
            cost_samples: 成本样本数组
            qaly_samples: QALY样本数组
            reference_cost: 参考策略成本
            reference_qalys: 参考策略QALYs
            strategy_name: 策略名称
            include_confidence_intervals: 是否包含置信区间
            
        Returns:
            CEACResult: CEAC分析结果
        """
        logger.debug(f"生成{strategy_name}的单策略CEAC曲线")
        
        if len(cost_samples) != len(qaly_samples):
            raise ValueError("成本样本和QALY样本数量必须相等")
        
        # 计算增量值
        incremental_costs = cost_samples - reference_cost
        incremental_qalys = qaly_samples - reference_qalys
        
        probabilities = []
        confidence_intervals = [] if include_confidence_intervals else None
        
        for threshold in self.wtp_thresholds:
            # 计算净货币效益
            nmb_samples = incremental_qalys * threshold - incremental_costs
            
            # 计算成本效益概率
            prob_cost_effective = np.mean(nmb_samples > 0)
            probabilities.append(prob_cost_effective)
            
            # 计算置信区间
            if include_confidence_intervals:
                ci = self._calculate_probability_confidence_interval(nmb_samples > 0)
                confidence_intervals.append(ci)
        
        # 找到概率为50%时的阈值
        threshold_at_50_percent = self._find_threshold_at_probability(
            self.wtp_thresholds, probabilities, 0.5
        )
        
        # 找到最优阈值（概率变化最大的点）
        optimal_threshold = self._find_optimal_threshold(
            self.wtp_thresholds, probabilities
        )
        
        result = CEACResult(
            strategy_name=strategy_name,
            wtp_thresholds=self.wtp_thresholds.tolist(),
            probabilities=probabilities,
            analysis_type=CEACAnalysisType.SINGLE_STRATEGY,
            sample_size=len(cost_samples),
            confidence_intervals=confidence_intervals,
            optimal_threshold=optimal_threshold,
            threshold_at_50_percent=threshold_at_50_percent
        )
        
        logger.info(f"单策略CEAC生成完成，50%概率阈值: {threshold_at_50_percent:,.0f}元/QALY")
        return result
    
    def generate_multi_strategy_ceac(
        self,
        strategies_data: List[Dict[str, Union[str, np.ndarray]]],
        include_pairwise_analysis: bool = False
    ) -> MultiStrategyCEACResult:
        """
        生成多策略CEAC比较
        
        Args:
            strategies_data: 策略数据列表，包含name, cost_samples, qaly_samples
            include_pairwise_analysis: 是否包含成对比较分析
            
        Returns:
            MultiStrategyCEACResult: 多策略CEAC结果
        """
        logger.debug(f"生成多策略CEAC比较，共{len(strategies_data)}个策略")
        
        strategies = [s['name'] for s in strategies_data]
        strategy_probabilities = {strategy: [] for strategy in strategies}
        dominant_strategy_by_threshold = []
        
        # 验证样本数量一致性
        sample_sizes = [len(s['cost_samples']) for s in strategies_data]
        if len(set(sample_sizes)) > 1:
            raise ValueError("所有策略的样本数量必须相等")
        
        n_samples = sample_sizes[0]
        
        for threshold in self.wtp_thresholds:
            # 计算每个策略的净货币效益
            strategy_nmb = {}
            for strategy_data in strategies_data:
                nmb_samples = (strategy_data['qaly_samples'] * threshold - 
                              strategy_data['cost_samples'])
                strategy_nmb[strategy_data['name']] = nmb_samples
            
            # 计算每个策略成为最优选择的概率
            strategy_optimal_counts = {strategy: 0 for strategy in strategies}
            
            for i in range(n_samples):
                # 找到第i次模拟中NMB最高的策略
                sample_nmb = {name: strategy_nmb[name][i] for name in strategies}
                best_strategy = max(sample_nmb, key=sample_nmb.get)
                strategy_optimal_counts[best_strategy] += 1
            
            # 转换为概率并记录占优策略
            max_prob = 0
            dominant_strategy = strategies[0]
            
            for strategy in strategies:
                prob = strategy_optimal_counts[strategy] / n_samples
                strategy_probabilities[strategy].append(prob)
                
                if prob > max_prob:
                    max_prob = prob
                    dominant_strategy = strategy
            
            dominant_strategy_by_threshold.append(dominant_strategy)
        
        # 生成分析摘要
        analysis_summary = self._generate_multi_strategy_summary(
            strategies, strategy_probabilities, self.wtp_thresholds
        )
        
        result = MultiStrategyCEACResult(
            strategies=strategies,
            wtp_thresholds=self.wtp_thresholds.tolist(),
            strategy_probabilities=strategy_probabilities,
            dominant_strategy_by_threshold=dominant_strategy_by_threshold,
            analysis_summary=analysis_summary
        )
        
        logger.info(f"多策略CEAC生成完成，主要占优策略: {analysis_summary.get('most_dominant_strategy', 'N/A')}")
        return result
    
    def perform_pairwise_ceac_analysis(
        self,
        strategy1_data: Dict[str, Union[str, np.ndarray]],
        strategy2_data: Dict[str, Union[str, np.ndarray]]
    ) -> Dict[str, CEACResult]:
        """
        执行成对CEAC分析
        
        Args:
            strategy1_data: 策略1数据
            strategy2_data: 策略2数据
            
        Returns:
            Dict[str, CEACResult]: 成对CEAC分析结果
        """
        logger.debug(f"执行成对CEAC分析: {strategy1_data['name']} vs {strategy2_data['name']}")
        
        # 策略1相对于策略2的CEAC
        ceac1 = self.generate_single_strategy_ceac(
            strategy1_data['cost_samples'],
            strategy1_data['qaly_samples'],
            np.mean(strategy2_data['cost_samples']),
            np.mean(strategy2_data['qaly_samples']),
            f"{strategy1_data['name']}_vs_{strategy2_data['name']}"
        )
        ceac1.analysis_type = CEACAnalysisType.PAIRWISE
        
        # 策略2相对于策略1的CEAC
        ceac2 = self.generate_single_strategy_ceac(
            strategy2_data['cost_samples'],
            strategy2_data['qaly_samples'],
            np.mean(strategy1_data['cost_samples']),
            np.mean(strategy1_data['qaly_samples']),
            f"{strategy2_data['name']}_vs_{strategy1_data['name']}"
        )
        ceac2.analysis_type = CEACAnalysisType.PAIRWISE
        
        result = {
            f"{strategy1_data['name']}_vs_{strategy2_data['name']}": ceac1,
            f"{strategy2_data['name']}_vs_{strategy1_data['name']}": ceac2
        }
        
        logger.info("成对CEAC分析完成")
        return result
    
    def _calculate_probability_confidence_interval(
        self,
        binary_outcomes: np.ndarray,
        confidence_level: float = 0.95
    ) -> Tuple[float, float]:
        """
        计算概率的置信区间
        
        Args:
            binary_outcomes: 二元结果数组（True/False）
            confidence_level: 置信水平
            
        Returns:
            Tuple[float, float]: 置信区间下限和上限
        """
        n = len(binary_outcomes)
        p = np.mean(binary_outcomes)
        
        # 使用Wilson得分区间
        z = stats.norm.ppf(1 - (1 - confidence_level) / 2)
        
        denominator = 1 + z**2 / n
        center = (p + z**2 / (2 * n)) / denominator
        margin = z * np.sqrt(p * (1 - p) / n + z**2 / (4 * n**2)) / denominator
        
        ci_lower = max(0, center - margin)
        ci_upper = min(1, center + margin)
        
        return (ci_lower, ci_upper)
    
    def _find_threshold_at_probability(
        self,
        thresholds: np.ndarray,
        probabilities: List[float],
        target_probability: float
    ) -> Optional[float]:
        """
        找到特定概率对应的阈值
        
        Args:
            thresholds: 阈值数组
            probabilities: 概率列表
            target_probability: 目标概率
            
        Returns:
            Optional[float]: 对应的阈值
        """
        prob_array = np.array(probabilities)
        
        # 找到最接近目标概率的索引
        closest_idx = np.argmin(np.abs(prob_array - target_probability))
        
        # 如果找到精确匹配或接近匹配
        if abs(prob_array[closest_idx] - target_probability) < 0.01:
            return thresholds[closest_idx]
        
        # 线性插值
        if closest_idx > 0 and closest_idx < len(prob_array) - 1:
            if prob_array[closest_idx] < target_probability:
                # 在当前点和下一个点之间插值
                x1, y1 = thresholds[closest_idx], prob_array[closest_idx]
                x2, y2 = thresholds[closest_idx + 1], prob_array[closest_idx + 1]
            else:
                # 在前一个点和当前点之间插值
                x1, y1 = thresholds[closest_idx - 1], prob_array[closest_idx - 1]
                x2, y2 = thresholds[closest_idx], prob_array[closest_idx]
            
            if y2 != y1:
                interpolated_threshold = x1 + (target_probability - y1) * (x2 - x1) / (y2 - y1)
                return interpolated_threshold
        
        return thresholds[closest_idx]
    
    def _find_optimal_threshold(
        self,
        thresholds: np.ndarray,
        probabilities: List[float]
    ) -> Optional[float]:
        """
        找到最优阈值（概率变化最大的点）
        
        Args:
            thresholds: 阈值数组
            probabilities: 概率列表
            
        Returns:
            Optional[float]: 最优阈值
        """
        if len(probabilities) < 3:
            return None
        
        # 计算概率的一阶导数（差分）
        prob_diff = np.diff(probabilities)
        
        # 找到变化最大的点
        max_change_idx = np.argmax(np.abs(prob_diff))
        
        # 返回变化最大点的中点阈值
        return (thresholds[max_change_idx] + thresholds[max_change_idx + 1]) / 2
    
    def _generate_multi_strategy_summary(
        self,
        strategies: List[str],
        strategy_probabilities: Dict[str, List[float]],
        thresholds: np.ndarray
    ) -> Dict[str, Union[str, float, int]]:
        """
        生成多策略分析摘要
        
        Args:
            strategies: 策略名称列表
            strategy_probabilities: 策略概率字典
            thresholds: 阈值数组
            
        Returns:
            Dict: 分析摘要
        """
        # 计算每个策略的平均概率
        avg_probabilities = {
            strategy: np.mean(probs) 
            for strategy, probs in strategy_probabilities.items()
        }
        
        # 找到平均概率最高的策略
        most_dominant_strategy = max(avg_probabilities, key=avg_probabilities.get)
        
        # 计算每个策略在不同阈值范围内的占优情况
        threshold_ranges = {
            'low': (0, 100000),
            'medium': (100000, 300000),
            'high': (300000, float('inf'))
        }
        
        range_dominance = {}
        for range_name, (low, high) in threshold_ranges.items():
            range_indices = np.where((thresholds >= low) & (thresholds < high))[0]
            if len(range_indices) > 0:
                range_avg_probs = {
                    strategy: np.mean([strategy_probabilities[strategy][i] for i in range_indices])
                    for strategy in strategies
                }
                range_dominance[range_name] = max(range_avg_probs, key=range_avg_probs.get)
        
        # 计算决策不确定性（概率在0.2-0.8之间的阈值比例）
        uncertain_points = 0
        total_points = len(thresholds)
        
        for i in range(total_points):
            max_prob_at_threshold = max(strategy_probabilities[s][i] for s in strategies)
            if 0.2 <= max_prob_at_threshold <= 0.8:
                uncertain_points += 1
        
        decision_uncertainty = uncertain_points / total_points
        
        summary = {
            'most_dominant_strategy': most_dominant_strategy,
            'highest_avg_probability': avg_probabilities[most_dominant_strategy],
            'decision_uncertainty': decision_uncertainty,
            'low_threshold_dominant': range_dominance.get('low', 'N/A'),
            'medium_threshold_dominant': range_dominance.get('medium', 'N/A'),
            'high_threshold_dominant': range_dominance.get('high', 'N/A'),
            'total_strategies': len(strategies),
            'threshold_points': total_points
        }
        
        return summary
    
    def generate_ceac_visualization_data(
        self,
        ceac_result: Union[CEACResult, MultiStrategyCEACResult]
    ) -> Dict[str, Union[List, Dict]]:
        """
        生成CEAC可视化数据
        
        Args:
            ceac_result: CEAC分析结果
            
        Returns:
            Dict: 可视化数据
        """
        logger.debug("生成CEAC可视化数据")
        
        if isinstance(ceac_result, CEACResult):
            # 单策略CEAC可视化数据
            viz_data = {
                'type': 'single_strategy',
                'strategy_name': ceac_result.strategy_name,
                'x_thresholds': ceac_result.wtp_thresholds,
                'y_probabilities': ceac_result.probabilities,
                'confidence_intervals': ceac_result.confidence_intervals,
                'key_points': {
                    'threshold_at_50_percent': ceac_result.threshold_at_50_percent,
                    'optimal_threshold': ceac_result.optimal_threshold
                }
            }
            
        else:
            # 多策略CEAC可视化数据
            viz_data = {
                'type': 'multi_strategy',
                'strategies': ceac_result.strategies,
                'x_thresholds': ceac_result.wtp_thresholds,
                'strategy_curves': ceac_result.strategy_probabilities,
                'dominant_strategy_by_threshold': ceac_result.dominant_strategy_by_threshold,
                'summary': ceac_result.analysis_summary
            }
        
        logger.info("CEAC可视化数据生成完成")
        return viz_data
    
    def export_ceac_data(
        self,
        ceac_result: Union[CEACResult, MultiStrategyCEACResult],
        file_format: str = "csv"
    ) -> Dict[str, pd.DataFrame]:
        """
        导出CEAC数据
        
        Args:
            ceac_result: CEAC分析结果
            file_format: 导出格式
            
        Returns:
            Dict[str, pd.DataFrame]: 导出数据
        """
        logger.debug(f"导出CEAC数据，格式: {file_format}")
        
        export_data = {}
        
        if isinstance(ceac_result, CEACResult):
            # 单策略数据导出
            main_data = pd.DataFrame({
                '支付意愿阈值': ceac_result.wtp_thresholds,
                '成本效益概率': ceac_result.probabilities
            })
            
            if ceac_result.confidence_intervals:
                main_data['置信区间下限'] = [ci[0] for ci in ceac_result.confidence_intervals]
                main_data['置信区间上限'] = [ci[1] for ci in ceac_result.confidence_intervals]
            
            export_data['ceac_curve'] = main_data
            
            # 关键点数据
            key_points = pd.DataFrame([
                {
                    '指标': '50%概率阈值',
                    '数值': ceac_result.threshold_at_50_percent,
                    '单位': '元/QALY'
                },
                {
                    '指标': '最优阈值',
                    '数值': ceac_result.optimal_threshold,
                    '单位': '元/QALY'
                },
                {
                    '指标': '样本数量',
                    '数值': ceac_result.sample_size,
                    '单位': '个'
                }
            ])
            export_data['key_metrics'] = key_points
            
        else:
            # 多策略数据导出
            main_data = pd.DataFrame({
                '支付意愿阈值': ceac_result.wtp_thresholds,
                **ceac_result.strategy_probabilities,
                '占优策略': ceac_result.dominant_strategy_by_threshold
            })
            export_data['multi_strategy_ceac'] = main_data
            
            # 摘要数据
            summary_data = pd.DataFrame([
                {
                    '指标': key,
                    '数值': value
                }
                for key, value in ceac_result.analysis_summary.items()
            ])
            export_data['analysis_summary'] = summary_data
        
        logger.info(f"CEAC数据导出完成，包含{len(export_data)}个数据表")
        return export_data
    
    def update_wtp_range(self, new_range: Tuple[float, float], new_n_points: Optional[int] = None) -> None:
        """
        更新支付意愿阈值范围
        
        Args:
            new_range: 新的阈值范围
            new_n_points: 新的分析点数
        """
        old_range = self.wtp_range
        self.wtp_range = new_range
        
        if new_n_points:
            self.n_points = new_n_points
        
        self.wtp_thresholds = np.linspace(new_range[0], new_range[1], self.n_points)
        
        logger.info(f"阈值范围已更新: {old_range} -> {new_range}, 分析点数: {self.n_points}")
    
    def find_optimal_strategy_at_threshold(
        self,
        multi_ceac_result: MultiStrategyCEACResult,
        target_threshold: float
    ) -> Dict[str, Union[str, float]]:
        """
        找到特定阈值下的最优策略
        
        Args:
            multi_ceac_result: 多策略CEAC结果
            target_threshold: 目标阈值
            
        Returns:
            Dict: 最优策略信息
        """
        # 找到最接近目标阈值的索引
        thresholds = np.array(multi_ceac_result.wtp_thresholds)
        closest_idx = np.argmin(np.abs(thresholds - target_threshold))
        
        # 找到该阈值下概率最高的策略
        max_prob = 0
        optimal_strategy = ""
        strategy_probs = {}
        
        for strategy in multi_ceac_result.strategies:
            prob = multi_ceac_result.strategy_probabilities[strategy][closest_idx]
            strategy_probs[strategy] = prob
            
            if prob > max_prob:
                max_prob = prob
                optimal_strategy = strategy
        
        result = {
            'target_threshold': target_threshold,
            'actual_threshold': thresholds[closest_idx],
            'optimal_strategy': optimal_strategy,
            'optimal_probability': max_prob,
            'all_probabilities': strategy_probs
        }
        
        logger.info(f"阈值{target_threshold:,.0f}元/QALY下的最优策略: {optimal_strategy} (概率: {max_prob:.3f})")
        return result