"""
性能监控模块
实现抽样性能监控和瓶颈分析工具
"""

import time
import psutil
import threading
import logging
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
import numpy as np
import json
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    timestamp: float
    metric_name: str
    value: float
    unit: str
    category: str
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class BottleneckInfo:
    """瓶颈信息数据类"""
    component: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    description: str
    impact_score: float
    recommendations: List[str]
    metrics: Dict[str, float]

@dataclass
class PerformanceReport:
    """性能报告数据类"""
    timestamp: float
    duration_seconds: float
    total_samples: int
    bottlenecks: List[BottleneckInfo]
    performance_summary: Dict[str, Any]
    recommendations: List[str]

class PerformanceCollector:
    """性能数据收集器"""
    
    def __init__(self, collection_interval: float = 0.1):
        """
        初始化性能收集器
        
        Args:
            collection_interval: 数据收集间隔（秒）
        """
        self.collection_interval = collection_interval
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self.collecting = False
        self.collection_thread = None
        self.start_time = None
        
        # 系统监控
        self.process = psutil.Process()
        
        # 自定义指标
        self.custom_metrics = {}
        
    def start_collection(self):
        """开始收集性能数据"""
        if self.collecting:
            return
        
        self.collecting = True
        self.start_time = time.time()
        self.collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        self.collection_thread.start()
        
        logger.info("性能数据收集已启动")
    
    def stop_collection(self):
        """停止收集性能数据"""
        self.collecting = False
        if self.collection_thread:
            self.collection_thread.join(timeout=2.0)
        
        logger.info("性能数据收集已停止")
    
    def _collection_loop(self):
        """数据收集循环"""
        while self.collecting:
            try:
                current_time = time.time()
                
                # 收集系统指标
                self._collect_system_metrics(current_time)
                
                # 收集自定义指标
                self._collect_custom_metrics(current_time)
                
                time.sleep(self.collection_interval)
                
            except Exception as e:
                logger.error(f"性能数据收集错误: {e}")
                time.sleep(self.collection_interval)
    
    def _collect_system_metrics(self, timestamp: float):
        """收集系统性能指标"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent()
        self.add_metric("cpu_usage", cpu_percent, "percent", "system", timestamp)
        
        # 内存使用
        memory_info = psutil.virtual_memory()
        self.add_metric("memory_usage", memory_info.percent, "percent", "system", timestamp)
        self.add_metric("memory_available", memory_info.available / (1024**3), "GB", "system", timestamp)
        
        # 进程内存使用
        process_memory = self.process.memory_info()
        self.add_metric("process_memory", process_memory.rss / (1024**2), "MB", "process", timestamp)
        
        # 进程CPU使用
        process_cpu = self.process.cpu_percent()
        self.add_metric("process_cpu", process_cpu, "percent", "process", timestamp)
        
        # 磁盘I/O
        disk_io = psutil.disk_io_counters()
        if disk_io:
            self.add_metric("disk_read_mb", disk_io.read_bytes / (1024**2), "MB", "io", timestamp)
            self.add_metric("disk_write_mb", disk_io.write_bytes / (1024**2), "MB", "io", timestamp)
    
    def _collect_custom_metrics(self, timestamp: float):
        """收集自定义指标"""
        for metric_name, metric_func in self.custom_metrics.items():
            try:
                value = metric_func()
                if isinstance(value, (int, float)):
                    self.add_metric(metric_name, value, "custom", "custom", timestamp)
                elif isinstance(value, dict):
                    for sub_name, sub_value in value.items():
                        if isinstance(sub_value, (int, float)):
                            self.add_metric(f"{metric_name}_{sub_name}", sub_value, "custom", "custom", timestamp)
            except Exception as e:
                logger.error(f"收集自定义指标 {metric_name} 失败: {e}")
    
    def add_metric(self, name: str, value: float, unit: str, category: str, 
                  timestamp: Optional[float] = None, metadata: Optional[Dict] = None):
        """添加性能指标"""
        if timestamp is None:
            timestamp = time.time()
        
        metric = PerformanceMetric(
            timestamp=timestamp,
            metric_name=name,
            value=value,
            unit=unit,
            category=category,
            metadata=metadata or {}
        )
        
        self.metrics[name].append(metric)
    
    def register_custom_metric(self, name: str, metric_func: Callable[[], Any]):
        """注册自定义指标函数"""
        self.custom_metrics[name] = metric_func
    
    def get_metric_history(self, metric_name: str, 
                          duration_seconds: Optional[float] = None) -> List[PerformanceMetric]:
        """获取指标历史数据"""
        if metric_name not in self.metrics:
            return []
        
        metrics = list(self.metrics[metric_name])
        
        if duration_seconds is not None:
            cutoff_time = time.time() - duration_seconds
            metrics = [m for m in metrics if m.timestamp >= cutoff_time]
        
        return metrics
    
    def get_metric_statistics(self, metric_name: str,
                            duration_seconds: Optional[float] = None) -> Dict[str, float]:
        """获取指标统计信息"""
        history = self.get_metric_history(metric_name, duration_seconds)
        
        if not history:
            return {}
        
        values = [m.value for m in history]
        
        return {
            'count': len(values),
            'mean': np.mean(values),
            'std': np.std(values),
            'min': np.min(values),
            'max': np.max(values),
            'median': np.median(values),
            'p95': np.percentile(values, 95),
            'p99': np.percentile(values, 99)
        }
    
    def clear_metrics(self):
        """清空所有指标数据"""
        self.metrics.clear()
        logger.info("性能指标数据已清空")

class BottleneckAnalyzer:
    """瓶颈分析器"""
    
    def __init__(self, collector: PerformanceCollector):
        """
        初始化瓶颈分析器
        
        Args:
            collector: 性能数据收集器
        """
        self.collector = collector
        
        # 瓶颈检测阈值
        self.thresholds = {
            'cpu_usage': {'warning': 70, 'critical': 90},
            'memory_usage': {'warning': 75, 'critical': 90},
            'process_memory': {'warning': 1000, 'critical': 2000},  # MB
            'disk_io_rate': {'warning': 100, 'critical': 500},  # MB/s
            'execution_time': {'warning': 30, 'critical': 120},  # seconds
            'samples_per_second': {'warning': 100, 'critical': 10}  # 低于此值为瓶颈
        }
    
    def analyze_bottlenecks(self, duration_seconds: float = 300) -> List[BottleneckInfo]:
        """
        分析性能瓶颈
        
        Args:
            duration_seconds: 分析时间窗口（秒）
            
        Returns:
            List[BottleneckInfo]: 瓶颈信息列表
        """
        bottlenecks = []
        
        # 分析CPU瓶颈
        cpu_bottleneck = self._analyze_cpu_bottleneck(duration_seconds)
        if cpu_bottleneck:
            bottlenecks.append(cpu_bottleneck)
        
        # 分析内存瓶颈
        memory_bottleneck = self._analyze_memory_bottleneck(duration_seconds)
        if memory_bottleneck:
            bottlenecks.append(memory_bottleneck)
        
        # 分析I/O瓶颈
        io_bottleneck = self._analyze_io_bottleneck(duration_seconds)
        if io_bottleneck:
            bottlenecks.append(io_bottleneck)
        
        # 分析算法效率瓶颈
        algorithm_bottleneck = self._analyze_algorithm_bottleneck(duration_seconds)
        if algorithm_bottleneck:
            bottlenecks.append(algorithm_bottleneck)
        
        # 按影响分数排序
        bottlenecks.sort(key=lambda x: x.impact_score, reverse=True)
        
        return bottlenecks
    
    def _analyze_cpu_bottleneck(self, duration_seconds: float) -> Optional[BottleneckInfo]:
        """分析CPU瓶颈"""
        cpu_stats = self.collector.get_metric_statistics('cpu_usage', duration_seconds)
        process_cpu_stats = self.collector.get_metric_statistics('process_cpu', duration_seconds)
        
        if not cpu_stats or not process_cpu_stats:
            return None
        
        avg_cpu = cpu_stats.get('mean', 0)
        max_cpu = cpu_stats.get('max', 0)
        avg_process_cpu = process_cpu_stats.get('mean', 0)
        
        severity = 'low'
        impact_score = 0.0
        recommendations = []
        
        if max_cpu >= self.thresholds['cpu_usage']['critical']:
            severity = 'critical'
            impact_score = 0.9
            recommendations.extend([
                "CPU使用率过高，考虑减少并行度或优化算法",
                "检查是否有CPU密集型操作可以优化",
                "考虑使用更高性能的硬件"
            ])
        elif avg_cpu >= self.thresholds['cpu_usage']['warning']:
            severity = 'medium'
            impact_score = 0.6
            recommendations.extend([
                "CPU使用率较高，监控性能影响",
                "考虑优化计算密集型操作"
            ])
        
        if avg_process_cpu > 50:  # 进程占用超过50%
            impact_score += 0.2
            recommendations.append("当前进程CPU占用较高，检查算法效率")
        
        if impact_score > 0.3:
            return BottleneckInfo(
                component="CPU",
                severity=severity,
                description=f"CPU使用率: 平均{avg_cpu:.1f}%, 最大{max_cpu:.1f}%, 进程平均{avg_process_cpu:.1f}%",
                impact_score=impact_score,
                recommendations=recommendations,
                metrics={
                    'avg_cpu_usage': avg_cpu,
                    'max_cpu_usage': max_cpu,
                    'avg_process_cpu': avg_process_cpu
                }
            )
        
        return None
    
    def _analyze_memory_bottleneck(self, duration_seconds: float) -> Optional[BottleneckInfo]:
        """分析内存瓶颈"""
        memory_stats = self.collector.get_metric_statistics('memory_usage', duration_seconds)
        process_memory_stats = self.collector.get_metric_statistics('process_memory', duration_seconds)
        
        if not memory_stats or not process_memory_stats:
            return None
        
        avg_memory = memory_stats.get('mean', 0)
        max_memory = memory_stats.get('max', 0)
        avg_process_memory = process_memory_stats.get('mean', 0)
        max_process_memory = process_memory_stats.get('max', 0)
        
        severity = 'low'
        impact_score = 0.0
        recommendations = []
        
        if max_memory >= self.thresholds['memory_usage']['critical']:
            severity = 'critical'
            impact_score = 0.9
            recommendations.extend([
                "系统内存使用率过高，可能导致性能严重下降",
                "考虑增加系统内存或优化内存使用",
                "启用内存压缩或使用内存映射文件"
            ])
        elif avg_memory >= self.thresholds['memory_usage']['warning']:
            severity = 'medium'
            impact_score = 0.6
            recommendations.extend([
                "系统内存使用率较高，监控内存泄漏",
                "考虑批量处理以减少内存峰值"
            ])
        
        if max_process_memory >= self.thresholds['process_memory']['critical']:
            severity = max(severity, 'high')
            impact_score += 0.3
            recommendations.extend([
                "进程内存使用过高，检查内存泄漏",
                "考虑使用内存优化技术"
            ])
        elif avg_process_memory >= self.thresholds['process_memory']['warning']:
            impact_score += 0.2
            recommendations.append("进程内存使用较高，监控内存增长趋势")
        
        if impact_score > 0.3:
            return BottleneckInfo(
                component="Memory",
                severity=severity,
                description=f"内存使用: 系统平均{avg_memory:.1f}%, 进程平均{avg_process_memory:.1f}MB, 进程峰值{max_process_memory:.1f}MB",
                impact_score=impact_score,
                recommendations=recommendations,
                metrics={
                    'avg_memory_usage': avg_memory,
                    'max_memory_usage': max_memory,
                    'avg_process_memory': avg_process_memory,
                    'max_process_memory': max_process_memory
                }
            )
        
        return None
    
    def _analyze_io_bottleneck(self, duration_seconds: float) -> Optional[BottleneckInfo]:
        """分析I/O瓶颈"""
        disk_read_stats = self.collector.get_metric_statistics('disk_read_mb', duration_seconds)
        disk_write_stats = self.collector.get_metric_statistics('disk_write_mb', duration_seconds)
        
        if not disk_read_stats or not disk_write_stats:
            return None
        
        # 计算I/O速率
        read_history = self.collector.get_metric_history('disk_read_mb', duration_seconds)
        write_history = self.collector.get_metric_history('disk_write_mb', duration_seconds)
        
        if len(read_history) < 2 or len(write_history) < 2:
            return None
        
        # 计算平均I/O速率
        read_rate = self._calculate_rate(read_history)
        write_rate = self._calculate_rate(write_history)
        total_io_rate = read_rate + write_rate
        
        severity = 'low'
        impact_score = 0.0
        recommendations = []
        
        if total_io_rate >= self.thresholds['disk_io_rate']['critical']:
            severity = 'critical'
            impact_score = 0.8
            recommendations.extend([
                "磁盘I/O速率过高，可能成为性能瓶颈",
                "考虑使用SSD或优化数据访问模式",
                "启用数据压缩以减少I/O量"
            ])
        elif total_io_rate >= self.thresholds['disk_io_rate']['warning']:
            severity = 'medium'
            impact_score = 0.5
            recommendations.extend([
                "磁盘I/O速率较高，监控性能影响",
                "考虑优化数据存储策略"
            ])
        
        if impact_score > 0.3:
            return BottleneckInfo(
                component="Disk I/O",
                severity=severity,
                description=f"磁盘I/O: 读取{read_rate:.1f}MB/s, 写入{write_rate:.1f}MB/s, 总计{total_io_rate:.1f}MB/s",
                impact_score=impact_score,
                recommendations=recommendations,
                metrics={
                    'read_rate_mbs': read_rate,
                    'write_rate_mbs': write_rate,
                    'total_io_rate_mbs': total_io_rate
                }
            )
        
        return None
    
    def _analyze_algorithm_bottleneck(self, duration_seconds: float) -> Optional[BottleneckInfo]:
        """分析算法效率瓶颈"""
        # 这里需要从自定义指标中获取算法性能数据
        execution_time_stats = self.collector.get_metric_statistics('execution_time', duration_seconds)
        samples_per_second_stats = self.collector.get_metric_statistics('samples_per_second', duration_seconds)
        
        severity = 'low'
        impact_score = 0.0
        recommendations = []
        
        if execution_time_stats:
            avg_execution_time = execution_time_stats.get('mean', 0)
            max_execution_time = execution_time_stats.get('max', 0)
            
            if max_execution_time >= self.thresholds['execution_time']['critical']:
                severity = 'critical'
                impact_score = 0.9
                recommendations.extend([
                    "算法执行时间过长，严重影响性能",
                    "考虑算法优化或并行化处理",
                    "检查是否存在低效的循环或计算"
                ])
            elif avg_execution_time >= self.thresholds['execution_time']['warning']:
                severity = 'medium'
                impact_score = 0.6
                recommendations.extend([
                    "算法执行时间较长，考虑优化",
                    "分析算法复杂度和瓶颈点"
                ])
        
        if samples_per_second_stats:
            avg_samples_per_second = samples_per_second_stats.get('mean', 0)
            min_samples_per_second = samples_per_second_stats.get('min', 0)
            
            if min_samples_per_second <= self.thresholds['samples_per_second']['critical']:
                severity = max(severity, 'high')
                impact_score += 0.4
                recommendations.extend([
                    "抽样效率极低，检查算法实现",
                    "考虑使用向量化或并行处理"
                ])
            elif avg_samples_per_second <= self.thresholds['samples_per_second']['warning']:
                impact_score += 0.3
                recommendations.append("抽样效率较低，考虑性能优化")
        
        if impact_score > 0.3:
            description_parts = []
            if execution_time_stats:
                avg_time = execution_time_stats.get('mean', 0)
                description_parts.append(f"平均执行时间{avg_time:.2f}秒")
            if samples_per_second_stats:
                avg_rate = samples_per_second_stats.get('mean', 0)
                description_parts.append(f"平均处理速度{avg_rate:.1f}样本/秒")
            
            return BottleneckInfo(
                component="Algorithm",
                severity=severity,
                description="算法效率: " + ", ".join(description_parts),
                impact_score=impact_score,
                recommendations=recommendations,
                metrics={
                    'avg_execution_time': execution_time_stats.get('mean', 0) if execution_time_stats else 0,
                    'avg_samples_per_second': samples_per_second_stats.get('mean', 0) if samples_per_second_stats else 0
                }
            )
        
        return None
    
    def _calculate_rate(self, metric_history: List[PerformanceMetric]) -> float:
        """计算指标变化率"""
        if len(metric_history) < 2:
            return 0.0
        
        # 计算总变化量和时间差
        total_change = metric_history[-1].value - metric_history[0].value
        time_diff = metric_history[-1].timestamp - metric_history[0].timestamp
        
        if time_diff <= 0:
            return 0.0
        
        return max(0, total_change / time_diff)  # 确保非负
    
    def get_performance_score(self, duration_seconds: float = 300) -> float:
        """
        计算整体性能分数（0-100）
        
        Args:
            duration_seconds: 评估时间窗口
            
        Returns:
            float: 性能分数
        """
        bottlenecks = self.analyze_bottlenecks(duration_seconds)
        
        if not bottlenecks:
            return 100.0
        
        # 计算性能损失
        total_impact = sum(bottleneck.impact_score for bottleneck in bottlenecks)
        
        # 转换为性能分数
        performance_score = max(0, 100 - total_impact * 100)
        
        return performance_score

class PerformanceMonitor:
    """性能监控主类"""
    
    def __init__(self, collection_interval: float = 0.1):
        """
        初始化性能监控器
        
        Args:
            collection_interval: 数据收集间隔
        """
        self.collector = PerformanceCollector(collection_interval)
        self.analyzer = BottleneckAnalyzer(self.collector)
        self.monitoring_active = False
        self.reports = []
    
    def start_monitoring(self):
        """开始性能监控"""
        self.collector.start_collection()
        self.monitoring_active = True
        logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.collector.stop_collection()
        self.monitoring_active = False
        logger.info("性能监控已停止")
    
    def register_sampling_metrics(self, sampler_instance):
        """注册抽样相关的性能指标"""
        def get_execution_time():
            if hasattr(sampler_instance, 'last_execution_time'):
                return sampler_instance.last_execution_time
            return 0.0
        
        def get_samples_per_second():
            if hasattr(sampler_instance, 'samples_per_second'):
                return sampler_instance.samples_per_second
            return 0.0
        
        def get_memory_efficiency():
            if hasattr(sampler_instance, 'memory_efficiency'):
                return sampler_instance.memory_efficiency
            return 0.0
        
        self.collector.register_custom_metric('execution_time', get_execution_time)
        self.collector.register_custom_metric('samples_per_second', get_samples_per_second)
        self.collector.register_custom_metric('memory_efficiency', get_memory_efficiency)
    
    def generate_report(self, duration_seconds: float = 300) -> PerformanceReport:
        """
        生成性能报告
        
        Args:
            duration_seconds: 报告时间窗口
            
        Returns:
            PerformanceReport: 性能报告
        """
        start_time = time.time()
        
        # 分析瓶颈
        bottlenecks = self.analyzer.analyze_bottlenecks(duration_seconds)
        
        # 收集性能摘要
        performance_summary = self._generate_performance_summary(duration_seconds)
        
        # 生成建议
        recommendations = self._generate_recommendations(bottlenecks, performance_summary)
        
        # 计算总样本数（如果有的话）
        total_samples = performance_summary.get('total_samples_processed', 0)
        
        report = PerformanceReport(
            timestamp=start_time,
            duration_seconds=duration_seconds,
            total_samples=total_samples,
            bottlenecks=bottlenecks,
            performance_summary=performance_summary,
            recommendations=recommendations
        )
        
        self.reports.append(report)
        
        # 保持最近50个报告
        if len(self.reports) > 50:
            self.reports = self.reports[-50:]
        
        return report
    
    def _generate_performance_summary(self, duration_seconds: float) -> Dict[str, Any]:
        """生成性能摘要"""
        summary = {}
        
        # 系统资源使用
        cpu_stats = self.collector.get_metric_statistics('cpu_usage', duration_seconds)
        memory_stats = self.collector.get_metric_statistics('memory_usage', duration_seconds)
        process_memory_stats = self.collector.get_metric_statistics('process_memory', duration_seconds)
        
        if cpu_stats:
            summary['cpu'] = {
                'avg_usage': cpu_stats.get('mean', 0),
                'max_usage': cpu_stats.get('max', 0),
                'p95_usage': cpu_stats.get('p95', 0)
            }
        
        if memory_stats:
            summary['system_memory'] = {
                'avg_usage': memory_stats.get('mean', 0),
                'max_usage': memory_stats.get('max', 0),
                'p95_usage': memory_stats.get('p95', 0)
            }
        
        if process_memory_stats:
            summary['process_memory'] = {
                'avg_usage_mb': process_memory_stats.get('mean', 0),
                'max_usage_mb': process_memory_stats.get('max', 0),
                'p95_usage_mb': process_memory_stats.get('p95', 0)
            }
        
        # 算法性能
        execution_time_stats = self.collector.get_metric_statistics('execution_time', duration_seconds)
        samples_per_second_stats = self.collector.get_metric_statistics('samples_per_second', duration_seconds)
        
        if execution_time_stats:
            summary['algorithm_performance'] = {
                'avg_execution_time': execution_time_stats.get('mean', 0),
                'max_execution_time': execution_time_stats.get('max', 0),
                'total_execution_time': execution_time_stats.get('mean', 0) * execution_time_stats.get('count', 0)
            }
        
        if samples_per_second_stats:
            summary['throughput'] = {
                'avg_samples_per_second': samples_per_second_stats.get('mean', 0),
                'min_samples_per_second': samples_per_second_stats.get('min', 0),
                'max_samples_per_second': samples_per_second_stats.get('max', 0)
            }
            
            # 估算总处理样本数
            if execution_time_stats:
                total_time = execution_time_stats.get('mean', 0) * execution_time_stats.get('count', 0)
                avg_rate = samples_per_second_stats.get('mean', 0)
                summary['total_samples_processed'] = int(total_time * avg_rate)
        
        # 整体性能分数
        summary['performance_score'] = self.analyzer.get_performance_score(duration_seconds)
        
        return summary
    
    def _generate_recommendations(self, bottlenecks: List[BottleneckInfo], 
                                performance_summary: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 从瓶颈中提取建议
        for bottleneck in bottlenecks:
            recommendations.extend(bottleneck.recommendations)
        
        # 基于性能摘要的额外建议
        performance_score = performance_summary.get('performance_score', 100)
        
        if performance_score < 60:
            recommendations.append("整体性能较差，建议优先解决关键瓶颈")
        elif performance_score < 80:
            recommendations.append("性能有改进空间，建议逐步优化")
        else:
            recommendations.append("性能良好，保持当前配置")
        
        # 去重并限制数量
        unique_recommendations = list(dict.fromkeys(recommendations))
        return unique_recommendations[:10]  # 最多10条建议
    
    def visualize_performance(self, duration_seconds: float = 300, 
                            output_path: Optional[str] = None):
        """
        可视化性能数据
        
        Args:
            duration_seconds: 可视化时间窗口
            output_path: 输出文件路径
        """
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('性能监控仪表板', fontsize=16)
        
        # CPU使用率
        cpu_history = self.collector.get_metric_history('cpu_usage', duration_seconds)
        if cpu_history:
            times = [(m.timestamp - cpu_history[0].timestamp) / 60 for m in cpu_history]  # 转换为分钟
            values = [m.value for m in cpu_history]
            
            axes[0, 0].plot(times, values, 'b-', linewidth=2)
            axes[0, 0].set_title('CPU使用率')
            axes[0, 0].set_xlabel('时间 (分钟)')
            axes[0, 0].set_ylabel('使用率 (%)')
            axes[0, 0].grid(True, alpha=0.3)
            axes[0, 0].axhline(y=70, color='orange', linestyle='--', alpha=0.7, label='警告线')
            axes[0, 0].axhline(y=90, color='red', linestyle='--', alpha=0.7, label='危险线')
            axes[0, 0].legend()
        
        # 内存使用
        memory_history = self.collector.get_metric_history('process_memory', duration_seconds)
        if memory_history:
            times = [(m.timestamp - memory_history[0].timestamp) / 60 for m in memory_history]
            values = [m.value for m in memory_history]
            
            axes[0, 1].plot(times, values, 'g-', linewidth=2)
            axes[0, 1].set_title('进程内存使用')
            axes[0, 1].set_xlabel('时间 (分钟)')
            axes[0, 1].set_ylabel('内存 (MB)')
            axes[0, 1].grid(True, alpha=0.3)
            axes[0, 1].axhline(y=1000, color='orange', linestyle='--', alpha=0.7, label='警告线')
            axes[0, 1].axhline(y=2000, color='red', linestyle='--', alpha=0.7, label='危险线')
            axes[0, 1].legend()
        
        # 执行时间
        exec_time_history = self.collector.get_metric_history('execution_time', duration_seconds)
        if exec_time_history:
            times = [(m.timestamp - exec_time_history[0].timestamp) / 60 for m in exec_time_history]
            values = [m.value for m in exec_time_history]
            
            axes[1, 0].plot(times, values, 'r-', linewidth=2)
            axes[1, 0].set_title('执行时间')
            axes[1, 0].set_xlabel('时间 (分钟)')
            axes[1, 0].set_ylabel('时间 (秒)')
            axes[1, 0].grid(True, alpha=0.3)
        
        # 处理速度
        throughput_history = self.collector.get_metric_history('samples_per_second', duration_seconds)
        if throughput_history:
            times = [(m.timestamp - throughput_history[0].timestamp) / 60 for m in throughput_history]
            values = [m.value for m in throughput_history]
            
            axes[1, 1].plot(times, values, 'm-', linewidth=2)
            axes[1, 1].set_title('处理速度')
            axes[1, 1].set_xlabel('时间 (分钟)')
            axes[1, 1].set_ylabel('样本/秒')
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            logger.info(f"性能图表已保存到: {output_path}")
        else:
            plt.show()
        
        plt.close()
    
    def export_report(self, report: PerformanceReport, output_path: str):
        """导出性能报告"""
        report_data = {
            'timestamp': report.timestamp,
            'duration_seconds': report.duration_seconds,
            'total_samples': report.total_samples,
            'performance_summary': report.performance_summary,
            'bottlenecks': [
                {
                    'component': b.component,
                    'severity': b.severity,
                    'description': b.description,
                    'impact_score': b.impact_score,
                    'recommendations': b.recommendations,
                    'metrics': b.metrics
                } for b in report.bottlenecks
            ],
            'recommendations': report.recommendations
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"性能报告已导出到: {output_path}")
    
    def get_historical_trends(self, metric_name: str, 
                            hours: int = 24) -> Dict[str, Any]:
        """获取历史趋势分析"""
        duration_seconds = hours * 3600
        history = self.collector.get_metric_history(metric_name, duration_seconds)
        
        if len(history) < 10:
            return {'trend': 'insufficient_data'}
        
        # 计算趋势
        times = np.array([m.timestamp for m in history])
        values = np.array([m.value for m in history])
        
        # 线性回归计算趋势
        time_normalized = times - times[0]
        coeffs = np.polyfit(time_normalized, values, 1)
        trend_slope = coeffs[0]
        
        # 分类趋势
        if abs(trend_slope) < 0.01:
            trend_direction = 'stable'
        elif trend_slope > 0:
            trend_direction = 'increasing'
        else:
            trend_direction = 'decreasing'
        
        # 计算变化率
        total_change = values[-1] - values[0]
        time_span = times[-1] - times[0]
        change_rate = total_change / (time_span / 3600) if time_span > 0 else 0  # 每小时变化率
        
        return {
            'trend': trend_direction,
            'slope': trend_slope,
            'change_rate_per_hour': change_rate,
            'total_change': total_change,
            'volatility': np.std(values),
            'data_points': len(history)
        }
    
    def cleanup(self):
        """清理资源"""
        self.stop_monitoring()
        self.collector.clear_metrics()
        self.reports.clear()
        logger.info("性能监控器已清理")

# 便捷函数
def monitor_sampling_performance(sampler_func: Callable, 
                               *args, 
                               monitor_duration: float = 300,
                               **kwargs) -> Tuple[Any, PerformanceReport]:
    """
    监控抽样函数的性能
    
    Args:
        sampler_func: 抽样函数
        monitor_duration: 监控持续时间
        *args, **kwargs: 传递给抽样函数的参数
        
    Returns:
        Tuple[Any, PerformanceReport]: (抽样结果, 性能报告)
    """
    monitor = PerformanceMonitor(collection_interval=0.1)
    
    try:
        monitor.start_monitoring()
        
        # 执行抽样函数
        result = sampler_func(*args, **kwargs)
        
        # 等待一段时间收集数据
        time.sleep(min(2.0, monitor_duration * 0.1))
        
        # 生成报告
        report = monitor.generate_report(monitor_duration)
        
        return result, report
        
    finally:
        monitor.cleanup()
"""
性能监控模块
实现抽样性能监控和瓶颈分析工具
"""

import time
import psutil
import threading
import logging
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
import numpy as np
import json
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    timestamp: float
    metric_name: str
    value: float
    unit: str
    category: str
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class BottleneckInfo:
    """瓶颈信息数据类"""
    component: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    description: str
    impact_score: float
    recommendations: List[str]
    metrics: Dict[str, float]

@dataclass
class PerformanceReport:
    """性能报告数据类"""
    timestamp: float
    duration_seconds: float
    total_samples: int
    bottlenecks: List[BottleneckInfo]
    performance_summary: Dict[str, Any]
    recommendations: List[str]

class PerformanceCollector:
    """性能数据收集器"""
    
    def __init__(self, collection_interval: float = 0.1):
        """
        初始化性能收集器
        
        Args:
            collection_interval: 数据收集间隔（秒）
        """
        self.collection_interval = collection_interval
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self.collecting = False
        self.collection_thread = None
        self.start_time = None
        
        # 系统监控
        self.process = psutil.Process()
        
        # 自定义指标
        self.custom_metrics = {}
        
    def start_collection(self):
        """开始收集性能数据"""
        if self.collecting:
            return
        
        self.collecting = True
        self.start_time = time.time()
        self.collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        self.collection_thread.start()
        
        logger.info("性能数据收集已启动")
    
    def stop_collection(self):
        """停止收集性能数据"""
        self.collecting = False
        if self.collection_thread:
            self.collection_thread.join(timeout=2.0)
        
        logger.info("性能数据收集已停止")
    
    def _collection_loop(self):
        """数据收集循环"""
        while self.collecting:
            try:
                current_time = time.time()
                
                # 收集系统指标
                self._collect_system_metrics(current_time)
                
                # 收集自定义指标
                self._collect_custom_metrics(current_time)
                
                time.sleep(self.collection_interval)
                
            except Exception as e:
                logger.error(f"性能数据收集错误: {e}")
                time.sleep(self.collection_interval)
    
    def _collect_system_metrics(self, timestamp: float):
        """收集系统性能指标"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent()
        self.add_metric("cpu_usage", cpu_percent, "percent", "system", timestamp)
        
        # 内存使用
        memory_info = psutil.virtual_memory()
        self.add_metric("memory_usage", memory_info.percent, "percent", "system", timestamp)
        self.add_metric("memory_available", memory_info.available / (1024**3), "GB", "system", timestamp)
        
        # 进程内存使用
        process_memory = self.process.memory_info()
        self.add_metric("process_memory", process_memory.rss / (1024**2), "MB", "process", timestamp)
        
        # 进程CPU使用
        process_cpu = self.process.cpu_percent()
        self.add_metric("process_cpu", process_cpu, "percent", "process", timestamp)
        
        # 磁盘I/O
        disk_io = psutil.disk_io_counters()
        if disk_io:
            self.add_metric("disk_read_mb", disk_io.read_bytes / (1024**2), "MB", "io", timestamp)
            self.add_metric("disk_write_mb", disk_io.write_bytes / (1024**2), "MB", "io", timestamp)
    
    def _collect_custom_metrics(self, timestamp: float):
        """收集自定义指标"""
        for metric_name, metric_func in self.custom_metrics.items():
            try:
                value = metric_func()
                if isinstance(value, (int, float)):
                    self.add_metric(metric_name, value, "custom", "custom", timestamp)
                elif isinstance(value, dict):
                    for sub_name, sub_value in value.items():
                        if isinstance(sub_value, (int, float)):
                            self.add_metric(f"{metric_name}_{sub_name}", sub_value, "custom", "custom", timestamp)
            except Exception as e:
                logger.error(f"收集自定义指标 {metric_name} 失败: {e}")
    
    def add_metric(self, name: str, value: float, unit: str, category: str, 
                  timestamp: Optional[float] = None, metadata: Optional[Dict] = None):
        """添加性能指标"""
        if timestamp is None:
            timestamp = time.time()
        
        metric = PerformanceMetric(
            timestamp=timestamp,
            metric_name=name,
            value=value,
            unit=unit,
            category=category,
            metadata=metadata or {}
        )
        
        self.metrics[name].append(metric)
    
    def register_custom_metric(self, name: str, metric_func: Callable[[], Any]):
        """注册自定义指标函数"""
        self.custom_metrics[name] = metric_func
    
    def get_metric_history(self, metric_name: str, 
                          duration_seconds: Optional[float] = None) -> List[PerformanceMetric]:
        """获取指标历史数据"""
        if metric_name not in self.metrics:
            return []
        
        metrics = list(self.metrics[metric_name])
        
        if duration_seconds is not None:
            cutoff_time = time.time() - duration_seconds
            metrics = [m for m in metrics if m.timestamp >= cutoff_time]
        
        return metrics
    
    def get_metric_statistics(self, metric_name: str, 
                            duration_seconds: Optional[float] = None) -> Dict[str, float]:
        """获取指标统计信息"""
        history = self.get_metric_history(metric_name, duration_seconds)
        
        if not history:
            return {}
        
        values = [m.value for m in history]
        
        return {
            'count': len(values),
            'mean': np.mean(values),
            'std': np.std(values),
            'min': np.min(values),
            'max': np.max(values),
            'median': np.median(values),
            'p95': np.percentile(values, 95),
            'p99': np.percentile(values, 99)
        }
    
    def clear_metrics(self):
        """清空所有指标数据"""
        self.metrics.clear()
        logger.info("性能指标数据已清空")

class BottleneckAnalyzer:
    """瓶颈分析器"""
    
    def __init__(self, collector: PerformanceCollector):
        """
        初始化瓶颈分析器
        
        Args:
            collector: 性能数据收集器
        """
        self.collector = collector
        
        # 瓶颈检测阈值
        self.thresholds = {
            'cpu_usage': {'warning': 70, 'critical': 90},
            'memory_usage': {'warning': 75, 'critical': 90},
            'process_memory': {'warning': 1000, 'critical': 2000},  # MB
            'disk_io_rate': {'warning': 100, 'critical': 500},  # MB/s
            'execution_time': {'warning': 30, 'critical': 120},  # seconds
            'samples_per_second': {'warning': 100, 'critical': 10}  # 低于此值为瓶颈
        }
    
    def analyze_bottlenecks(self, duration_seconds: float = 300) -> List[BottleneckInfo]:
        """
        分析性能瓶颈
        
        Args:
            duration_seconds: 分析时间窗口（秒）
            
        Returns:
            List[BottleneckInfo]: 瓶颈信息列表
        """
        bottlenecks = []
        
        # 分析CPU瓶颈
        cpu_bottleneck = self._analyze_cpu_bottleneck(duration_seconds)
        if cpu_bottleneck:
            bottlenecks.append(cpu_bottleneck)
        
        # 分析内存瓶颈
        memory_bottleneck = self._analyze_memory_bottleneck(duration_seconds)
        if memory_bottleneck:
            bottlenecks.append(memory_bottleneck)
        
        # 分析I/O瓶颈
        io_bottleneck = self._analyze_io_bottleneck(duration_seconds)
        if io_bottleneck:
            bottlenecks.append(io_bottleneck)
        
        # 分析算法效率瓶颈
        algorithm_bottleneck = self._analyze_algorithm_bottleneck(duration_seconds)
        if algorithm_bottleneck:
            bottlenecks.append(algorithm_bottleneck)
        
        # 按影响分数排序
        bottlenecks.sort(key=lambda x: x.impact_score, reverse=True)
        
        return bottlenecks
    
    def _analyze_cpu_bottleneck(self, duration_seconds: float) -> Optional[BottleneckInfo]:
        """分析CPU瓶颈"""
        cpu_stats = self.collector.get_metric_statistics('cpu_usage', duration_seconds)
        process_cpu_stats = self.collector.get_metric_statistics('process_cpu', duration_seconds)
        
        if not cpu_stats or not process_cpu_stats:
            return None
        
        avg_cpu = cpu_stats.get('mean', 0)
        max_cpu = cpu_stats.get('max', 0)
        avg_process_cpu = process_cpu_stats.get('mean', 0)
        
        severity = 'low'
        impact_score = 0.0
        recommendations = []
        
        if max_cpu >= self.thresholds['cpu_usage']['critical']:
            severity = 'critical'
            impact_score = 0.9
            recommendations.extend([
                "CPU使用率过高，考虑减少并行度或优化算法",
                "检查是否有CPU密集型操作可以优化",
                "考虑使用更高性能的硬件"
            ])
        elif avg_cpu >= self.thresholds['cpu_usage']['warning']:
            severity = 'medium'
            impact_score = 0.6
            recommendations.extend([
                "CPU使用率较高，监控性能影响",
                "考虑优化计算密集型操作"
            ])
        
        if avg_process_cpu > 50:  # 进程占用超过50%
            impact_score += 0.2
            recommendations.append("当前进程CPU占用较高，检查算法效率")
        
        if impact_score > 0.3:
            return BottleneckInfo(
                component="CPU",
                severity=severity,
                description=f"CPU使用率: 平均{avg_cpu:.1f}%, 最大{max_cpu:.1f}%, 进程平均{avg_process_cpu:.1f}%",
                impact_score=impact_score,
                recommendations=recommendations,
                metrics={
                    'avg_cpu_usage': avg_cpu,
                    'max_cpu_usage': max_cpu,
                    'avg_process_cpu': avg_process_cpu
                }
            )
        
        return None
    
    def _analyze_memory_bottleneck(self, duration_seconds: float) -> Optional[BottleneckInfo]:
        """分析内存瓶颈"""
        memory_stats = self.collector.get_metric_statistics('memory_usage', duration_seconds)
        process_memory_stats = self.collector.get_metric_statistics('process_memory', duration_seconds)
        
        if not memory_stats or not process_memory_stats:
            return None
        
        avg_memory = memory_stats.get('mean', 0)
        max_memory = memory_stats.get('max', 0)
        avg_process_memory = process_memory_stats.get('mean', 0)
        max_process_memory = process_memory_stats.get('max', 0)
        
        severity = 'low'
        impact_score = 0.0
        recommendations = []
        
        if max_memory >= self.thresholds['memory_usage']['critical']:
            severity = 'critical'
            impact_score = 0.9
            recommendations.extend([
                "系统内存使用率过高，可能导致性能严重下降",
                "考虑增加系统内存或优化内存使用",
                "启用内存压缩或使用内存映射文件"
            ])
        elif avg_memory >= self.thresholds['memory_usage']['warning']:
            severity = 'medium'
            impact_score = 0.6
            recommendations.extend([
                "系统内存使用率较高，监控内存泄漏",
                "考虑批量处理以减少内存峰值"
            ])
        
        if max_process_memory >= self.thresholds['process_memory']['critical']:
            severity = max(severity, 'high')
            impact_score += 0.3
            recommendations.extend([
                "进程内存使用过高，检查内存泄漏",
                "考虑使用内存优化技术"
            ])
        elif avg_process_memory >= self.thresholds['process_memory']['warning']:
            impact_score += 0.2
            recommendations.append("进程内存使用较高，监控内存增长趋势")
        
        if impact_score > 0.3:
            return BottleneckInfo(
                component="Memory",
                severity=severity,
                description=f"内存使用: 系统平均{avg_memory:.1f}%, 进程平均{avg_process_memory:.1f}MB, 进程峰值{max_process_memory:.1f}MB",
                impact_score=impact_score,
                recommendations=recommendations,
                metrics={
                    'avg_memory_usage': avg_memory,
                    'max_memory_usage': max_memory,
                    'avg_process_memory': avg_process_memory,
                    'max_process_memory': max_process_memory
                }
            )
        
        return None
    
    def _analyze_io_bottleneck(self, duration_seconds: float) -> Optional[BottleneckInfo]:
        """分析I/O瓶颈"""
        disk_read_stats = self.collector.get_metric_statistics('disk_read_mb', duration_seconds)
        disk_write_stats = self.collector.get_metric_statistics('disk_write_mb', duration_seconds)
        
        if not disk_read_stats or not disk_write_stats:
            return None
        
        # 计算I/O速率
        read_history = self.collector.get_metric_history('disk_read_mb', duration_seconds)
        write_history = self.collector.get_metric_history('disk_write_mb', duration_seconds)
        
        if len(read_history) < 2 or len(write_history) < 2:
            return None
        
        # 计算平均I/O速率
        read_rate = self._calculate_rate(read_history)
        write_rate = self._calculate_rate(write_history)
        total_io_rate = read_rate + write_rate
        
        severity = 'low'
        impact_score = 0.0
        recommendations = []
        
        if total_io_rate >= self.thresholds['disk_io_rate']['critical']:
            severity = 'critical'
            impact_score = 0.8
            recommendations.extend([
                "磁盘I/O速率过高，可能成为性能瓶颈",
                "考虑使用SSD或优化数据访问模式",
                "启用数据压缩以减少I/O量"
            ])
        elif total_io_rate >= self.thresholds['disk_io_rate']['warning']:
            severity = 'medium'
            impact_score = 0.5
            recommendations.extend([
                "磁盘I/O速率较高，监控性能影响",
                "考虑优化数据存储策略"
            ])
        
        if impact_score > 0.3:
            return BottleneckInfo(
                component="Disk I/O",
                severity=severity,
                description=f"磁盘I/O: 读取{read_rate:.1f}MB/s, 写入{write_rate:.1f}MB/s, 总计{total_io_rate:.1f}MB/s",
                impact_score=impact_score,
                recommendations=recommendations,
                metrics={
                    'read_rate_mbs': read_rate,
                    'write_rate_mbs': write_rate,
                    'total_io_rate_mbs': total_io_rate
                }
            )
        
        return None
    
    def _analyze_algorithm_bottleneck(self, duration_seconds: float) -> Optional[BottleneckInfo]:
        """分析算法效率瓶颈"""
        # 这里需要从自定义指标中获取算法性能数据
        execution_time_stats = self.collector.get_metric_statistics('execution_time', duration_seconds)
        samples_per_second_stats = self.collector.get_metric_statistics('samples_per_second', duration_seconds)
        
        severity = 'low'
        impact_score = 0.0
        recommendations = []
        
        if execution_time_stats:
            avg_execution_time = execution_time_stats.get('mean', 0)
            max_execution_time = execution_time_stats.get('max', 0)
            
            if max_execution_time >= self.thresholds['execution_time']['critical']:
                severity = 'critical'
                impact_score = 0.9
                recommendations.extend([
                    "算法执行时间过长，严重影响性能",
                    "考虑算法优化或并行化处理",
                    "检查是否存在低效的循环或计算"
                ])
            elif avg_execution_time >= self.thresholds['execution_time']['warning']:
                severity = 'medium'
                impact_score = 0.6
                recommendations.extend([
                    "算法执行时间较长，考虑优化",
                    "分析算法复杂度和瓶颈点"
                ])
        
        if samples_per_second_stats:
            avg_samples_per_second = samples_per_second_stats.get('mean', 0)
            min_samples_per_second = samples_per_second_stats.get('min', 0)
            
            if min_samples_per_second <= self.thresholds['samples_per_second']['critical']:
                severity = max(severity, 'high')
                impact_score += 0.4
                recommendations.extend([
                    "抽样效率极低，检查算法实现",
                    "考虑使用向量化或并行处理"
                ])
            elif avg_samples_per_second <= self.thresholds['samples_per_second']['warning']:
                impact_score += 0.3
                recommendations.append("抽样效率较低，考虑性能优化")
        
        if impact_score > 0.3:
            description_parts = []
            if execution_time_stats:
                avg_time = execution_time_stats.get('mean', 0)
                description_parts.append(f"平均执行时间{avg_time:.2f}秒")
            if samples_per_second_stats:
                avg_rate = samples_per_second_stats.get('mean', 0)
                description_parts.append(f"平均处理速度{avg_rate:.1f}样本/秒")
            
            return BottleneckInfo(
                component="Algorithm",
                severity=severity,
                description="算法效率: " + ", ".join(description_parts),
                impact_score=impact_score,
                recommendations=recommendations,
                metrics={
                    'avg_execution_time': execution_time_stats.get('mean', 0) if execution_time_stats else 0,
                    'avg_samples_per_second': samples_per_second_stats.get('mean', 0) if samples_per_second_stats else 0
                }
            )
        
        return None
    
    def _calculate_rate(self, metric_history: List[PerformanceMetric]) -> float:
        """计算指标变化率"""
        if len(metric_history) < 2:
            return 0.0
        
        # 计算总变化量和时间差
        total_change = metric_history[-1].value - metric_history[0].value
        time_diff = metric_history[-1].timestamp - metric_history[0].timestamp
        
        if time_diff <= 0:
            return 0.0
        
        return max(0, total_change / time_diff)  # 确保非负
    
    def get_performance_score(self, duration_seconds: float = 300) -> float:
        """
        计算整体性能分数（0-100）
        
        Args:
            duration_seconds: 评估时间窗口
            
        Returns:
            float: 性能分数
        """
        bottlenecks = self.analyze_bottlenecks(duration_seconds)
        
        if not bottlenecks:
            return 100.0
        
        # 计算性能损失
        total_impact = sum(bottleneck.impact_score for bottleneck in bottlenecks)
        
        # 转换为性能分数
        performance_score = max(0, 100 - total_impact * 100)
        
        return performance_score

class PerformanceMonitor:
    """性能监控主类"""
    
    def __init__(self, collection_interval: float = 0.1):
        """
        初始化性能监控器
        
        Args:
            collection_interval: 数据收集间隔
        """
        self.collector = PerformanceCollector(collection_interval)
        self.analyzer = BottleneckAnalyzer(self.collector)
        self.monitoring_active = False
        self.reports = []
    
    def start_monitoring(self):
        """开始性能监控"""
        self.collector.start_collection()
        self.monitoring_active = True
        logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.collector.stop_collection()
        self.monitoring_active = False
        logger.info("性能监控已停止")
    
    def register_sampling_metrics(self, sampler_instance):
        """注册抽样相关的性能指标"""
        def get_execution_time():
            if hasattr(sampler_instance, 'last_execution_time'):
                return sampler_instance.last_execution_time
            return 0.0
        
        def get_samples_per_second():
            if hasattr(sampler_instance, 'samples_per_second'):
                return sampler_instance.samples_per_second
            return 0.0
        
        def get_memory_efficiency():
            if hasattr(sampler_instance, 'memory_efficiency'):
                return sampler_instance.memory_efficiency
            return 0.0
        
        self.collector.register_custom_metric('execution_time', get_execution_time)
        self.collector.register_custom_metric('samples_per_second', get_samples_per_second)
        self.collector.register_custom_metric('memory_efficiency', get_memory_efficiency)
    
    def generate_report(self, duration_seconds: float = 300) -> PerformanceReport:
        """
        生成性能报告
        
        Args:
            duration_seconds: 报告时间窗口
            
        Returns:
            PerformanceReport: 性能报告
        """
        start_time = time.time()
        
        # 分析瓶颈
        bottlenecks = self.analyzer.analyze_bottlenecks(duration_seconds)
        
        # 收集性能摘要
        performance_summary = self._generate_performance_summary(duration_seconds)
        
        # 生成建议
        recommendations = self._generate_recommendations(bottlenecks, performance_summary)
        
        # 计算总样本数（如果有的话）
        total_samples = performance_summary.get('total_samples_processed', 0)
        
        report = PerformanceReport(
            timestamp=start_time,
            duration_seconds=duration_seconds,
            total_samples=total_samples,
            bottlenecks=bottlenecks,
            performance_summary=performance_summary,
            recommendations=recommendations
        )
        
        self.reports.append(report)
        
        # 保持最近50个报告
        if len(self.reports) > 50:
            self.reports = self.reports[-50:]
        
        return report
    
    def _generate_performance_summary(self, duration_seconds: float) -> Dict[str, Any]:
        """生成性能摘要"""
        summary = {}
        
        # 系统资源使用
        cpu_stats = self.collector.get_metric_statistics('cpu_usage', duration_seconds)
        memory_stats = self.collector.get_metric_statistics('memory_usage', duration_seconds)
        process_memory_stats = self.collector.get_metric_statistics('process_memory', duration_seconds)
        
        if cpu_stats:
            summary['cpu'] = {
                'avg_usage': cpu_stats.get('mean', 0),
                'max_usage': cpu_stats.get('max', 0),
                'p95_usage': cpu_stats.get('p95', 0)
            }
        
        if memory_stats:
            summary['system_memory'] = {
                'avg_usage': memory_stats.get('mean', 0),
                'max_usage': memory_stats.get('max', 0),
                'p95_usage': memory_stats.get('p95', 0)
            }
        
        if process_memory_stats:
            summary['process_memory'] = {
                'avg_usage_mb': process_memory_stats.get('mean', 0),
                'max_usage_mb': process_memory_stats.get('max', 0),
                'p95_usage_mb': process_memory_stats.get('p95', 0)
            }
        
        # 算法性能
        execution_time_stats = self.collector.get_metric_statistics('execution_time', duration_seconds)
        samples_per_second_stats = self.collector.get_metric_statistics('samples_per_second', duration_seconds)
        
        if execution_time_stats:
            summary['algorithm_performance'] = {
                'avg_execution_time': execution_time_stats.get('mean', 0),
                'max_execution_time': execution_time_stats.get('max', 0),
                'total_execution_time': execution_time_stats.get('mean', 0) * execution_time_stats.get('count', 0)
            }
        
        if samples_per_second_stats:
            summary['throughput'] = {
                'avg_samples_per_second': samples_per_second_stats.get('mean', 0),
                'min_samples_per_second': samples_per_second_stats.get('min', 0),
                'max_samples_per_second': samples_per_second_stats.get('max', 0)
            }
            
            # 估算总处理样本数
            if execution_time_stats:
                total_time = execution_time_stats.get('mean', 0) * execution_time_stats.get('count', 0)
                avg_rate = samples_per_second_stats.get('mean', 0)
                summary['total_samples_processed'] = int(total_time * avg_rate)
        
        # 整体性能分数
        summary['performance_score'] = self.analyzer.get_performance_score(duration_seconds)
        
        return summary
    
    def _generate_recommendations(self, bottlenecks: List[BottleneckInfo], 
                                performance_summary: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 从瓶颈中提取建议
        for bottleneck in bottlenecks:
            recommendations.extend(bottleneck.recommendations)
        
        # 基于性能摘要的额外建议
        performance_score = performance_summary.get('performance_score', 100)
        
        if performance_score < 60:
            recommendations.append("整体性能较差，建议优先解决关键瓶颈")
        elif performance_score < 80:
            recommendations.append("性能有改进空间，建议逐步优化")
        else:
            recommendations.append("性能良好，保持当前配置")
        
        # 去重并限制数量
        unique_recommendations = list(dict.fromkeys(recommendations))
        return unique_recommendations[:10]  # 最多10条建议
    
    def visualize_performance(self, duration_seconds: float = 300, 
                            output_path: Optional[str] = None):
        """
        可视化性能数据
        
        Args:
            duration_seconds: 可视化时间窗口
            output_path: 输出文件路径
        """
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('性能监控仪表板', fontsize=16)
        
        # CPU使用率
        cpu_history = self.collector.get_metric_history('cpu_usage', duration_seconds)
        if cpu_history:
            times = [(m.timestamp - cpu_history[0].timestamp) / 60 for m in cpu_history]  # 转换为分钟
            values = [m.value for m in cpu_history]
            
            axes[0, 0].plot(times, values, 'b-', linewidth=2)
            axes[0, 0].set_title('CPU使用率')
            axes[0, 0].set_xlabel('时间 (分钟)')
            axes[0, 0].set_ylabel('使用率 (%)')
            axes[0, 0].grid(True, alpha=0.3)
            axes[0, 0].axhline(y=70, color='orange', linestyle='--', alpha=0.7, label='警告线')
            axes[0, 0].axhline(y=90, color='red', linestyle='--', alpha=0.7, label='危险线')
            axes[0, 0].legend()
        
        # 内存使用
        memory_history = self.collector.get_metric_history('process_memory', duration_seconds)
        if memory_history:
            times = [(m.timestamp - memory_history[0].timestamp) / 60 for m in memory_history]
            values = [m.value for m in memory_history]
            
            axes[0, 1].plot(times, values, 'g-', linewidth=2)
            axes[0, 1].set_title('进程内存使用')
            axes[0, 1].set_xlabel('时间 (分钟)')
            axes[0, 1].set_ylabel('内存 (MB)')
            axes[0, 1].grid(True, alpha=0.3)
            axes[0, 1].axhline(y=1000, color='orange', linestyle='--', alpha=0.7, label='警告线')
            axes[0, 1].axhline(y=2000, color='red', linestyle='--', alpha=0.7, label='危险线')
            axes[0, 1].legend()
        
        # 执行时间
        exec_time_history = self.collector.get_metric_history('execution_time', duration_seconds)
        if exec_time_history:
            times = [(m.timestamp - exec_time_history[0].timestamp) / 60 for m in exec_time_history]
            values = [m.value for m in exec_time_history]
            
            axes[1, 0].plot(times, values, 'r-', linewidth=2)
            axes[1, 0].set_title('执行时间')
            axes[1, 0].set_xlabel('时间 (分钟)')
            axes[1, 0].set_ylabel('时间 (秒)')
            axes[1, 0].grid(True, alpha=0.3)
        
        # 处理速度
        throughput_history = self.collector.get_metric_history('samples_per_second', duration_seconds)
        if throughput_history:
            times = [(m.timestamp - throughput_history[0].timestamp) / 60 for m in throughput_history]
            values = [m.value for m in throughput_history]
            
            axes[1, 1].plot(times, values, 'm-', linewidth=2)
            axes[1, 1].set_title('处理速度')
            axes[1, 1].set_xlabel('时间 (分钟)')
            axes[1, 1].set_ylabel('样本/秒')
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            logger.info(f"性能图表已保存到: {output_path}")
        else:
            plt.show()
        
        plt.close()
    
    def export_report(self, report: PerformanceReport, output_path: str):
        """导出性能报告"""
        report_data = {
            'timestamp': report.timestamp,
            'duration_seconds': report.duration_seconds,
            'total_samples': report.total_samples,
            'performance_summary': report.performance_summary,
            'bottlenecks': [
                {
                    'component': b.component,
                    'severity': b.severity,
                    'description': b.description,
                    'impact_score': b.impact_score,
                    'recommendations': b.recommendations,
                    'metrics': b.metrics
                } for b in report.bottlenecks
            ],
            'recommendations': report.recommendations
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"性能报告已导出到: {output_path}")
    
    def get_historical_trends(self, metric_name: str, 
                            hours: int = 24) -> Dict[str, Any]:
        """获取历史趋势分析"""
        duration_seconds = hours * 3600
        history = self.collector.get_metric_history(metric_name, duration_seconds)
        
        if len(history) < 10:
            return {'trend': 'insufficient_data'}
        
        # 计算趋势
        times = np.array([m.timestamp for m in history])
        values = np.array([m.value for m in history])
        
        # 线性回归计算趋势
        time_normalized = times - times[0]
        coeffs = np.polyfit(time_normalized, values, 1)
        trend_slope = coeffs[0]
        
        # 分类趋势
        if abs(trend_slope) < 0.01:
            trend_direction = 'stable'
        elif trend_slope > 0:
            trend_direction = 'increasing'
        else:
            trend_direction = 'decreasing'
        
        # 计算变化率
        total_change = values[-1] - values[0]
        time_span = times[-1] - times[0]
        change_rate = total_change / (time_span / 3600) if time_span > 0 else 0  # 每小时变化率
        
        return {
            'trend': trend_direction,
            'slope': trend_slope,
            'change_rate_per_hour': change_rate,
            'total_change': total_change,
            'volatility': np.std(values),
            'data_points': len(history)
        }
    
    def cleanup(self):
        """清理资源"""
        self.stop_monitoring()
        self.collector.clear_metrics()
        self.reports.clear()
        logger.info("性能监控器已清理")

# 便捷函数
def monitor_sampling_performance(sampler_func: Callable, 
                               *args, 
                               monitor_duration: float = 300,
                               **kwargs) -> Tuple[Any, PerformanceReport]:
    """
    监控抽样函数的性能
    
    Args:
        sampler_func: 抽样函数
        monitor_duration: 监控持续时间
        *args, **kwargs: 传递给抽样函数的参数
        
    Returns:
        Tuple[Any, PerformanceReport]: (抽样结果, 性能报告)
    """
    monitor = PerformanceMonitor(collection_interval=0.1)
    
    try:
        monitor.start_monitoring()
        
        # 执行抽样函数
        result = sampler_func(*args, **kwargs)
        
        # 等待一段时间收集数据
        time.sleep(min(2.0, monitor_duration * 0.1))
        
        # 生成报告
        report = monitor.generate_report(monitor_duration)
        
        return result, report
        
    finally:
        monitor.cleanup
"""
性能监控模块
实现抽样性能监控和瓶颈分析工具
"""

import time
import psutil
import threading
import logging
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
import numpy as np
import json
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    timestamp: float
    metric_name: str
    value: float
    unit: str
    category: str
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class BottleneckInfo:
    """瓶颈信息数据类"""
    component: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    description: str
    impact_score: float
    recommendations: List[str]
    metrics: Dict[str, float]

@dataclass
class PerformanceReport:
    """性能报告数据类"""
    timestamp: float
    duration_seconds: float
    total_samples: int
    bottlenecks: List[BottleneckInfo]
    performance_summary: Dict[str, Any]
    recommendations: List[str]

class PerformanceCollector:
    """性能数据收集器"""
    
    def __init__(self, collection_interval: float = 0.1):
        """
        初始化性能收集器
        
        Args:
            collection_interval: 数据收集间隔（秒）
        """
        self.collection_interval = collection_interval
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self.collecting = False
        self.collection_thread = None
        self.start_time = None
        
        # 系统监控
        self.process = psutil.Process()
        
        # 自定义指标
        self.custom_metrics = {}
        
    def start_collection(self):
        """开始收集性能数据"""
        if self.collecting:
            return
        
        self.collecting = True
        self.start_time = time.time()
        self.collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        self.collection_thread.start()
        
        logger.info("性能数据收集已启动")
    
    def stop_collection(self):
        """停止收集性能数据"""
        self.collecting = False
        if self.collection_thread:
            self.collection_thread.join(timeout=2.0)
        
        logger.info("性能数据收集已停止")
    
    def _collection_loop(self):
        """数据收集循环"""
        while self.collecting:
            try:
                current_time = time.time()
                
                # 收集系统指标
                self._collect_system_metrics(current_time)
                
                # 收集自定义指标
                self._collect_custom_metrics(current_time)
                
                time.sleep(self.collection_interval)
                
            except Exception as e:
                logger.error(f"性能数据收集错误: {e}")
                time.sleep(self.collection_interval)
    
    def _collect_system_metrics(self, timestamp: float):
        """收集系统性能指标"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent()
        self.add_metric("cpu_usage", cpu_percent, "percent", "system", timestamp)
        
        # 内存使用
        memory_info = psutil.virtual_memory()
        self.add_metric("memory_usage", memory_info.percent, "percent", "system", timestamp)
        self.add_metric("memory_available", memory_info.available / (1024**3), "GB", "system", timestamp)
        
        # 进程内存使用
        process_memory = self.process.memory_info()
        self.add_metric("process_memory", process_memory.rss / (1024**2), "MB", "process", timestamp)
        
        # 进程CPU使用
        process_cpu = self.process.cpu_percent()
        self.add_metric("process_cpu", process_cpu, "percent", "process", timestamp)
        
        # 磁盘I/O
        disk_io = psutil.disk_io_counters()
        if disk_io:
            self.add_metric("disk_read_mb", disk_io.read_bytes / (1024**2), "MB", "io", timestamp)
            self.add_metric("disk_write_mb", disk_io.write_bytes / (1024**2), "MB", "io", timestamp)
    
    def _collect_custom_metrics(self, timestamp: float):
        """收集自定义指标"""
        for metric_name, metric_func in self.custom_metrics.items():
            try:
                value = metric_func()
                if isinstance(value, (int, float)):
                    self.add_metric(metric_name, value, "custom", "custom", timestamp)
                elif isinstance(value, dict):
                    for sub_name, sub_value in value.items():
                        if isinstance(sub_value, (int, float)):
                            self.add_metric(f"{metric_name}_{sub_name}", sub_value, "custom", "custom", timestamp)
            except Exception as e:
                logger.error(f"收集自定义指标 {metric_name} 失败: {e}")
    
    def add_metric(self, name: str, value: float, unit: str, category: str, 
                  timestamp: Optional[float] = None, metadata: Optional[Dict] = None):
        """添加性能指标"""
        if timestamp is None:
            timestamp = time.time()
        
        metric = PerformanceMetric(
            timestamp=timestamp,
            metric_name=name,
            value=value,
            unit=unit,
            category=category,
            metadata=metadata or {}
        )
        
        self.metrics[name].append(metric)
    
    def register_custom_metric(self, name: str, metric_func: Callable[[], Any]):
        """注册自定义指标函数"""
        self.custom_metrics[name] = metric_func
    
    def get_metric_history(self, metric_name: str, 
                          duration_seconds: Optional[float] = None) -> List[PerformanceMetric]:
        """获取指标历史数据"""
        if metric_name not in self.metrics:
            return []
        
        metrics = list(self.metrics[metric_name])
        
        if duration_seconds is not None:
            cutoff_time = time.time() - duration_seconds
            metrics = [m for m in metrics if m.timestamp >= cutoff_time]
        
        return metrics
    
    def get_metric_statistics(self, metric_name: str, 
                            duration_seconds: Optional[float] = None) -> Dict[str, float]:
        """获取指标统计信息"""
        history = self.get_metric_history(metric_name, duration_seconds)
        
        if not history:
            return {}
        
        values = [m.value for m in history]
        
        return {
            'count': len(values),
            'mean': np.mean(values),
            'std': np.std(values),
            'min': np.min(values),
            'max': np.max(values),
            'median': np.median(values),
            'p95': np.percentile(values, 95),
            'p99': np.percentile(values, 99)
        }
    
    def clear_metrics(self):
        """清空所有指标数据"""
        self.metrics.clear()
        logger.info("性能指标数据已清空")

class BottleneckAnalyzer:
    """瓶颈分析器"""
    
    def __init__(self, collector: PerformanceCollector):
