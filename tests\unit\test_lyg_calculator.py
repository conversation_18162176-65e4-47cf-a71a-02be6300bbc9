"""
Unit tests for LYG calculator module.
"""
import pytest
import numpy as np
from src.modules.economics.lyg_calculator import LYGCalculator
from src.core.population import Population
from src.core.individual import Individual


class TestLYGCalculator:
    """Test suite for LYGCalculator class."""
    
    def test_init(self):
        """Test LYGCalculator initialization."""
        calculator = LYGCalculator()
        assert calculator.life_table is None
        
        # Test with life table (mock)
        mock_life_table = object()
        calculator_with_lt = LYGCalculator(life_table=mock_life_table)
        assert calculator_with_lt.life_table == mock_life_table
    
    def test_calculate_lyg(self):
        """Test LYG calculation."""
        calculator = LYGCalculator()
        
        # Create sample data
        ages = [50, 55, 60, 65, 70]
        intervention_survival = [0.95, 0.90, 0.80, 0.65, 0.45]
        control_survival = [0.90, 0.80, 0.65, 0.50, 0.30]
        
        result = calculator.calculate_lyg(intervention_survival, control_survival, ages)
        
        assert isinstance(result, dict)
        assert 'total_lyg' in result
        assert 'lyg_by_age' in result
        assert 'mean_lyg_per_person' in result
        assert len(result['lyg_by_age']) == len(ages)
        
        # Check that LYG values are positive (intervention is better)
        for item in result['lyg_by_age']:
            assert item['lyg'] >= 0
            assert item['intervention_ly'] >= item['control_ly']
    
    def test_calculate_lyg_invalid_input(self):
        """Test LYG calculation with invalid input."""
        calculator = LYGCalculator()
        
        # Test with mismatched list lengths
        ages = [50, 55, 60]
        intervention_survival = [0.95, 0.90]
        control_survival = [0.90, 0.80, 0.65]
        
        with pytest.raises(ValueError):
            calculator.calculate_lyg(intervention_survival, control_survival, ages)
    
    def test_calculate_discounted_lyg(self):
        """Test discounted LYG calculation."""
        calculator = LYGCalculator()
        
        # Create sample LYG result
        ages = [50, 55, 60, 65, 70]
        intervention_survival = [0.95, 0.90, 0.80, 0.65, 0.45]
        control_survival = [0.90, 0.80, 0.65, 0.50, 0.30]
        
        lyg_result = calculator.calculate_lyg(intervention_survival, control_survival, ages)
        discounted_result = calculator.calculate_discounted_lyg(lyg_result)
        
        assert 'total_discounted_lyg' in discounted_result
        assert 'lyg_by_age' in discounted_result
        
        # Check that discounted values are added
        for item in discounted_result['lyg_by_age']:
            assert 'discounted_lyg' in item
    
    def test_calculate_discounted_lyg_invalid_input(self):
        """Test discounted LYG calculation with invalid input."""
        calculator = LYGCalculator()
        
        # Test with invalid result format
        invalid_result = {'invalid_key': 'invalid_value'}
        
        with pytest.raises(ValueError):
            calculator.calculate_discounted_lyg(invalid_result)
    
    def test_calculate_population_lyg(self):
        """Test population-level LYG calculation."""
        calculator = LYGCalculator()
        
        # Create mock populations
        intervention_population = Population()
        control_population = Population()
        
        intervention_population.individuals = []
        control_population.individuals = []
        
        # Add mock individuals
        from src.core.enums import Gender
        for i in range(5):
            ind1 = Individual(birth_year=1980, gender=Gender.FEMALE)
            ind1.age = 50 + i * 2
            intervention_population.individuals.append(ind1)

            ind2 = Individual(birth_year=1980, gender=Gender.MALE)
            ind2.age = 48 + i * 2
            control_population.individuals.append(ind2)
        
        result = calculator.calculate_population_lyg(intervention_population, control_population)
        
        assert isinstance(result, dict)
        assert 'intervention_population_size' in result
        assert 'control_population_size' in result
        assert 'lyg_analysis' in result
        assert isinstance(result['lyg_analysis'], dict)


if __name__ == "__main__":
    pytest.main([__file__])