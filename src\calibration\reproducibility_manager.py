"""
抽样可重现性管理模块
实现随机种子管理、版本控制和抽样结果验证
"""

import hashlib
import json
import time
import uuid
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import pickle
import gzip
import numpy as np
from datetime import datetime
import platform
import sys

from .parameter_sampler import SamplingConfig, SamplingResult, ParameterDefinition


@dataclass
class SamplingMetadata:
    """抽样元数据"""
    experiment_id: str
    timestamp: str
    version: str
    description: str
    author: str
    environment_info: Dict[str, Any]
    config_hash: str
    result_hash: str


@dataclass
class ReproducibilityRecord:
    """可重现性记录"""
    metadata: SamplingMetadata
    config: SamplingConfig
    quality_metrics: Dict[str, float]
    generation_time: float
    verification_status: str
    verification_timestamp: Optional[str] = None
    verification_details: Optional[Dict[str, Any]] = None


class SeedManager:
    """随机种子管理器"""
    
    def __init__(self, base_seed: int = 42):
        """
        初始化种子管理器
        
        Args:
            base_seed: 基础随机种子
        """
        self.base_seed = base_seed
        self.seed_history = []
        self.current_seed = base_seed
    
    def generate_seed(self, experiment_id: str, iteration: int = 0) -> int:
        """
        生成确定性种子
        
        Args:
            experiment_id: 实验ID
            iteration: 迭代次数
            
        Returns:
            int: 生成的种子
        """
        # 使用实验ID和迭代次数生成确定性种子
        seed_string = f"{self.base_seed}_{experiment_id}_{iteration}"
        seed_hash = hashlib.md5(seed_string.encode()).hexdigest()
        
        # 将哈希转换为整数种子
        seed = int(seed_hash[:8], 16) % (2**31 - 1)
        
        # 记录种子历史
        self.seed_history.append({
            'experiment_id': experiment_id,
            'iteration': iteration,
            'seed': seed,
            'timestamp': datetime.now().isoformat()
        })
        
        self.current_seed = seed
        return seed
    
    def get_seed_sequence(self, experiment_id: str, count: int) -> List[int]:
        """
        生成种子序列
        
        Args:
            experiment_id: 实验ID
            count: 种子数量
            
        Returns:
            List[int]: 种子序列
        """
        seeds = []
        for i in range(count):
            seed = self.generate_seed(experiment_id, i)
            seeds.append(seed)
        return seeds
    
    def reset_to_seed(self, seed: int):
        """
        重置到指定种子
        
        Args:
            seed: 目标种子
        """
        self.current_seed = seed
        np.random.seed(seed)
    
    def get_seed_history(self) -> List[Dict[str, Any]]:
        """
        获取种子历史
        
        Returns:
            List[Dict[str, Any]]: 种子历史记录
        """
        return self.seed_history.copy()
    
    def save_seed_history(self, filepath: str):
        """
        保存种子历史
        
        Args:
            filepath: 保存路径
        """
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump({
                'base_seed': self.base_seed,
                'current_seed': self.current_seed,
                'seed_history': self.seed_history
            }, f, indent=2, ensure_ascii=False)
    
    def load_seed_history(self, filepath: str):
        """
        加载种子历史
        
        Args:
            filepath: 文件路径
        """
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
            self.base_seed = data['base_seed']
            self.current_seed = data['current_seed']
            self.seed_history = data['seed_history']


class VersionManager:
    """版本管理器"""
    
    def __init__(self, version_file: Optional[str] = None):
        """
        初始化版本管理器
        
        Args:
            version_file: 版本文件路径
        """
        self.version_file = version_file
        self.current_version = "1.0.0"
        self.version_history = []
        
        if version_file and Path(version_file).exists():
            self.load_version_info()
    
    def get_current_version(self) -> str:
        """
        获取当前版本
        
        Returns:
            str: 当前版本号
        """
        return self.current_version
    
    def increment_version(self, version_type: str = "patch") -> str:
        """
        递增版本号
        
        Args:
            version_type: 版本类型 (major, minor, patch)
            
        Returns:
            str: 新版本号
        """
        major, minor, patch = map(int, self.current_version.split('.'))
        
        if version_type == "major":
            major += 1
            minor = 0
            patch = 0
        elif version_type == "minor":
            minor += 1
            patch = 0
        elif version_type == "patch":
            patch += 1
        else:
            raise ValueError(f"无效的版本类型: {version_type}")
        
        new_version = f"{major}.{minor}.{patch}"
        
        # 记录版本历史
        self.version_history.append({
            'old_version': self.current_version,
            'new_version': new_version,
            'version_type': version_type,
            'timestamp': datetime.now().isoformat()
        })
        
        self.current_version = new_version
        
        if self.version_file:
            self.save_version_info()
        
        return new_version
    
    def save_version_info(self):
        """保存版本信息"""
        if not self.version_file:
            return
        
        version_data = {
            'current_version': self.current_version,
            'version_history': self.version_history,
            'last_updated': datetime.now().isoformat()
        }
        
        with open(self.version_file, 'w', encoding='utf-8') as f:
            json.dump(version_data, f, indent=2, ensure_ascii=False)
    
    def load_version_info(self):
        """加载版本信息"""
        if not self.version_file or not Path(self.version_file).exists():
            return
        
        with open(self.version_file, 'r', encoding='utf-8') as f:
            version_data = json.load(f)
            self.current_version = version_data.get('current_version', '1.0.0')
            self.version_history = version_data.get('version_history', [])


class HashValidator:
    """哈希验证器"""
    
    @staticmethod
    def calculate_config_hash(config: SamplingConfig) -> str:
        """
        计算配置哈希
        
        Args:
            config: 抽样配置
            
        Returns:
            str: 配置哈希值
        """
        # 创建配置的标准化表示
        config_dict = {
            'n_samples': config.n_samples,
            'random_seed': config.random_seed,
            'sampling_method': config.sampling_method,
            'optimization_criterion': config.optimization_criterion,
            'parameters': [
                {
                    'name': p.name,
                    'min_value': p.min_value,
                    'max_value': p.max_value,
                    'distribution': p.distribution,
                    'constraints': p.constraints,
                    'description': p.description
                } for p in config.parameters
            ]
        }
        
        # 生成JSON字符串并计算哈希
        config_json = json.dumps(config_dict, sort_keys=True, ensure_ascii=False)
        return hashlib.sha256(config_json.encode('utf-8')).hexdigest()
    
    @staticmethod
    def calculate_result_hash(result: SamplingResult) -> str:
        """
        计算结果哈希
        
        Args:
            result: 抽样结果
            
        Returns:
            str: 结果哈希值
        """
        # 使用样本数据和质量指标计算哈希
        samples_hash = hashlib.md5(result.samples.tobytes()).hexdigest()
        
        metrics_dict = {
            'quality_metrics': result.quality_metrics,
            'parameter_names': result.parameter_names,
            'generation_time': result.generation_time
        }
        
        metrics_json = json.dumps(metrics_dict, sort_keys=True, ensure_ascii=False)
        metrics_hash = hashlib.md5(metrics_json.encode('utf-8')).hexdigest()
        
        # 组合哈希
        combined_hash = samples_hash + metrics_hash
        return hashlib.sha256(combined_hash.encode('utf-8')).hexdigest()
    
    @staticmethod
    def verify_result_integrity(result: SamplingResult, expected_hash: str) -> bool:
        """
        验证结果完整性
        
        Args:
            result: 抽样结果
            expected_hash: 期望的哈希值
            
        Returns:
            bool: 验证是否通过
        """
        actual_hash = HashValidator.calculate_result_hash(result)
        return actual_hash == expected_hash


class ReproducibilityManager:
    """可重现性管理器"""
    
    def __init__(self, storage_path: str, author: str = "Unknown"):
        """
        初始化可重现性管理器
        
        Args:
            storage_path: 存储路径
            author: 作者名称
        """
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        self.author = author
        
        # 初始化子组件
        self.seed_manager = SeedManager()
        self.version_manager = VersionManager(str(self.storage_path / "version.json"))
        self.hash_validator = HashValidator()
        
        # 记录存储
        self.records_file = self.storage_path / "reproducibility_records.json"
        self.records = self._load_records()
    
    def _load_records(self) -> List[ReproducibilityRecord]:
        """加载可重现性记录"""
        if not self.records_file.exists():
            return []
        
        try:
            with open(self.records_file, 'r', encoding='utf-8') as f:
                records_data = json.load(f)
            
            records = []
            for record_data in records_data:
                # 重构配置对象
                config_data = record_data['config']
                parameters = [
                    ParameterDefinition(**param_data) 
                    for param_data in config_data['parameters']
                ]
                config = SamplingConfig(
                    parameters=parameters,
                    n_samples=config_data['n_samples'],
                    random_seed=config_data['random_seed'],
                    sampling_method=config_data['sampling_method'],
                    optimization_criterion=config_data['optimization_criterion']
                )
                
                # 重构元数据对象
                metadata = SamplingMetadata(**record_data['metadata'])
                
                # 创建记录对象
                record = ReproducibilityRecord(
                    metadata=metadata,
                    config=config,
                    quality_metrics=record_data['quality_metrics'],
                    generation_time=record_data['generation_time'],
                    verification_status=record_data['verification_status'],
                    verification_timestamp=record_data.get('verification_timestamp'),
                    verification_details=record_data.get('verification_details')
                )
                
                records.append(record)
            
            return records
            
        except Exception as e:
            print(f"加载可重现性记录失败: {e}")
            return []
    
    def _save_records(self):
        """保存可重现性记录"""
        try:
            records_data = []
            for record in self.records:
                # 转换为可序列化的格式
                record_data = {
                    'metadata': asdict(record.metadata),
                    'config': {
                        'parameters': [asdict(p) for p in record.config.parameters],
                        'n_samples': record.config.n_samples,
                        'random_seed': record.config.random_seed,
                        'sampling_method': record.config.sampling_method,
                        'optimization_criterion': record.config.optimization_criterion
                    },
                    'quality_metrics': record.quality_metrics,
                    'generation_time': record.generation_time,
                    'verification_status': record.verification_status,
                    'verification_timestamp': record.verification_timestamp,
                    'verification_details': record.verification_details
                }
                records_data.append(record_data)
            
            with open(self.records_file, 'w', encoding='utf-8') as f:
                json.dump(records_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"保存可重现性记录失败: {e}")
    
    def create_experiment(self, config: SamplingConfig, description: str = "") -> str:
        """
        创建新实验
        
        Args:
            config: 抽样配置
            description: 实验描述
            
        Returns:
            str: 实验ID
        """
        experiment_id = str(uuid.uuid4())
        
        # 生成确定性种子
        seed = self.seed_manager.generate_seed(experiment_id)
        
        # 更新配置中的种子
        config.random_seed = seed
        
        return experiment_id
    
    def record_sampling_result(self, experiment_id: str, config: SamplingConfig, 
                             result: SamplingResult, description: str = "") -> ReproducibilityRecord:
        """
        记录抽样结果
        
        Args:
            experiment_id: 实验ID
            config: 抽样配置
            result: 抽样结果
            description: 描述
            
        Returns:
            ReproducibilityRecord: 可重现性记录
        """
        # 计算哈希值
        config_hash = self.hash_validator.calculate_config_hash(config)
        result_hash = self.hash_validator.calculate_result_hash(result)
        
        # 收集环境信息
        environment_info = {
            'python_version': sys.version,
            'platform': platform.platform(),
            'numpy_version': np.__version__,
            'timestamp': datetime.now().isoformat()
        }
        
        # 创建元数据
        metadata = SamplingMetadata(
            experiment_id=experiment_id,
            timestamp=datetime.now().isoformat(),
            version=self.version_manager.get_current_version(),
            description=description,
            author=self.author,
            environment_info=environment_info,
            config_hash=config_hash,
            result_hash=result_hash
        )
        
        # 创建记录
        record = ReproducibilityRecord(
            metadata=metadata,
            config=config,
            quality_metrics=result.quality_metrics,
            generation_time=result.generation_time,
            verification_status="unverified"
        )
        
        # 保存样本数据
        self._save_samples(experiment_id, result)
        
        # 添加到记录列表
        self.records.append(record)
        self._save_records()
        
        return record
    
    def _save_samples(self, experiment_id: str, result: SamplingResult):
        """
        保存样本数据
        
        Args:
            experiment_id: 实验ID
            result: 抽样结果
        """
        samples_file = self.storage_path / f"samples_{experiment_id}.pkl.gz"
        
        with gzip.open(samples_file, 'wb') as f:
            pickle.dump({
                'samples': result.samples,
                'parameter_names': result.parameter_names,
                'hash_signature': result.hash_signature
            }, f)
    
    def _load_samples(self, experiment_id: str) -> Optional[Dict[str, Any]]:
        """
        加载样本数据
        
        Args:
            experiment_id: 实验ID
            
        Returns:
            Optional[Dict[str, Any]]: 样本数据
        """
        samples_file = self.storage_path / f"samples_{experiment_id}.pkl.gz"
        
        if not samples_file.exists():
            return None
        
        try:
            with gzip.open(samples_file, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            print(f"加载样本数据失败: {e}")
            return None
    
    def reproduce_experiment(self, experiment_id: str) -> Optional[SamplingResult]:
        """
        重现实验
        
        Args:
            experiment_id: 实验ID
            
        Returns:
            Optional[SamplingResult]: 重现的抽样结果
        """
        # 查找记录
        record = self.find_record_by_id(experiment_id)
        if not record:
            print(f"未找到实验记录: {experiment_id}")
            return None
        
        # 重置种子
        self.seed_manager.reset_to_seed(record.config.random_seed)
        
        # 重新执行抽样
        from .parameter_sampler import LatinHypercubeSampler
        sampler = LatinHypercubeSampler(record.config)
        reproduced_result = sampler.generate_samples()
        
        return reproduced_result
    
    def verify_experiment(self, experiment_id: str) -> Dict[str, Any]:
        """
        验证实验可重现性
        
        Args:
            experiment_id: 实验ID
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        # 查找记录
        record = self.find_record_by_id(experiment_id)
        if not record:
            return {
                'success': False,
                'error': f'未找到实验记录: {experiment_id}'
            }
        
        # 加载原始样本
        original_samples = self._load_samples(experiment_id)
        if not original_samples:
            return {
                'success': False,
                'error': '无法加载原始样本数据'
            }
        
        # 重现实验
        reproduced_result = self.reproduce_experiment(experiment_id)
        if not reproduced_result:
            return {
                'success': False,
                'error': '无法重现实验'
            }
        
        # 比较结果
        verification_details = {
            'samples_match': np.array_equal(
                original_samples['samples'], 
                reproduced_result.samples
            ),
            'hash_match': (
                original_samples['hash_signature'] == 
                reproduced_result.hash_signature
            ),
            'config_hash_match': (
                record.metadata.config_hash == 
                self.hash_validator.calculate_config_hash(record.config)
            ),
            'quality_metrics_comparison': self._compare_quality_metrics(
                record.quality_metrics,
                reproduced_result.quality_metrics
            )
        }
        
        # 判断验证是否成功
        verification_success = all([
            verification_details['samples_match'],
            verification_details['hash_match'],
            verification_details['config_hash_match']
        ])
        
        # 更新记录
        record.verification_status = "verified" if verification_success else "failed"
        record.verification_timestamp = datetime.now().isoformat()
        record.verification_details = verification_details
        
        self._save_records()
        
        return {
            'success': verification_success,
            'details': verification_details,
            'experiment_id': experiment_id,
            'verification_timestamp': record.verification_timestamp
        }
    
    def _compare_quality_metrics(self, original: Dict[str, float], 
                                reproduced: Dict[str, float], 
                                tolerance: float = 1e-10) -> Dict[str, Any]:
        """
        比较质量指标
        
        Args:
            original: 原始指标
            reproduced: 重现指标
            tolerance: 容差
            
        Returns:
            Dict[str, Any]: 比较结果
        """
        comparison = {
            'metrics_match': True,
            'differences': {},
            'max_difference': 0.0
        }
        
        for key in original:
            if key in reproduced:
                diff = abs(original[key] - reproduced[key])
                comparison['differences'][key] = diff
                comparison['max_difference'] = max(comparison['max_difference'], diff)
                
                if diff > tolerance:
                    comparison['metrics_match'] = False
            else:
                comparison['metrics_match'] = False
                comparison['differences'][key] = f"缺失指标: {key}"
        
        return comparison
    
    def find_record_by_id(self, experiment_id: str) -> Optional[ReproducibilityRecord]:
        """
        根据ID查找记录
        
        Args:
            experiment_id: 实验ID
            
        Returns:
            Optional[ReproducibilityRecord]: 找到的记录
        """
        for record in self.records:
            if record.metadata.experiment_id == experiment_id:
                return record
        return None
    
    def list_experiments(self, verified_only: bool = False) -> List[Dict[str, Any]]:
        """
        列出实验
        
        Args:
            verified_only: 是否只显示已验证的实验
            
        Returns:
            List[Dict[str, Any]]: 实验列表
        """
        experiments = []
        
        for record in self.records:
            if verified_only and record.verification_status != "verified":
                continue
            
            experiment_info = {
                'experiment_id': record.metadata.experiment_id,
                'timestamp': record.metadata.timestamp,
                'version': record.metadata.version,
                'description': record.metadata.description,
                'author': record.metadata.author,
                'n_samples': record.config.n_samples,
                'n_parameters': len(record.config.parameters),
                'verification_status': record.verification_status,
                'generation_time': record.generation_time
            }
            
            experiments.append(experiment_info)
        
        return experiments
    
    def generate_reproducibility_report(self) -> str:
        """
        生成可重现性报告
        
        Returns:
            str: 报告文本
        """
        total_experiments = len(self.records)
        verified_experiments = len([r for r in self.records if r.verification_status == "verified"])
        failed_experiments = len([r for r in self.records if r.verification_status == "failed"])
        unverified_experiments = total_experiments - verified_experiments - failed_experiments
        
        report_lines = [
            "=" * 60,
            "抽样可重现性报告",
            "=" * 60,
            "",
            f"总实验数: {total_experiments}",
            f"已验证实验: {verified_experiments}",
            f"验证失败实验: {failed_experiments}",
            f"未验证实验: {unverified_experiments}",
            "",
            f"验证成功率: {verified_experiments/total_experiments*100:.1f}%" if total_experiments > 0 else "验证成功率: N/A",
            "",
            "实验详情:",
            "-" * 40
        ]
        
        for record in self.records:
            report_lines.extend([
                f"实验ID: {record.metadata.experiment_id[:8]}...",
                f"  时间: {record.metadata.timestamp}",
                f"  版本: {record.metadata.version}",
                f"  描述: {record.metadata.description}",
                f"  作者: {record.metadata.author}",
                f"  样本数: {record.config.n_samples}",
                f"  验证状态: {record.verification_status}",
                ""
            ])
        
        report_lines.extend([
            "=" * 60
        ])
        
        return "\n".join(report_lines)
    
    def cleanup_old_experiments(self, days_old: int = 30):
        """
        清理旧实验
        
        Args:
            days_old: 保留天数
        """
        cutoff_time = datetime.now().timestamp() - (days_old * 24 * 3600)
        
        records_to_keep = []
        for record in self.records:
            record_time = datetime.fromisoformat(record.metadata.timestamp).timestamp()
            
            if record_time >= cutoff_time:
                records_to_keep.append(record)
            else:
                # 删除样本文件
                samples_file = self.storage_path / f"samples_{record.metadata.experiment_id}.pkl.gz"
                if samples_file.exists():
                    samples_file.unlink()
        
        self.records = records_to_keep
        self._save_records()
        
        print(f"清理完成，保留了 {len(records_to_keep)} 个实验记录")