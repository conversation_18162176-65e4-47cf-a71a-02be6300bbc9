"""
Health Outcomes Calculation Demo

This script demonstrates the functionality of the newly implemented health outcomes calculation modules:
- QALY calculation
- LYG calculation
- Utility value management
- Uncertainty analysis
"""

import numpy as np
from src.modules.economics.qaly_calculator import QALYCalculator, HealthState
from src.modules.economics.lyg_calculator import LYGCalculator
from src.modules.economics.utility_values import UtilityValueManager
from src.modules.economics.uncertainty_analysis import UncertaintyAnalyzer
from src.core.individual import Individual
from src.core.population import Population
from src.core.enums import Gender, DiseaseState


def demo_qaly_calculation():
    """Demonstrate QALY calculation functionality."""
    print("=== QALY Calculation Demo ===")
    
    # Initialize QALY calculator
    qaly_calc = QALYCalculator(discount_rate=0.03)
    print(f"QALY calculator initialized with {qaly_calc.discount_rate} discount rate")
    
    # Create sample health states
    health_states = [
        HealthState(
            state_name="normal",
            utility_value=0.95,
            duration_years=5.0,
            age_at_start=50
        ),
        HealthState(
            state_name="clinical_cancer_stage_ii",
            utility_value=0.78,
            duration_years=3.0,
            age_at_start=55
        )
    ]
    
    # Create a sample individual
    individual = Individual(birth_year=1970, gender=Gender.MALE)
    individual.age = 58
    individual.baseline_age = 50
    
    # Calculate QALYs for the individual
    qaly_result = qaly_calc.calculate_individual_qalys(individual, health_states)
    
    print(f"Individual QALY Results:")
    print(f"  Total QALYs: {qaly_result.total_qalys:.2f}")
    print(f"  Undiscounted QALYs: {qaly_result.undiscounted_qalys:.2f}")
    print(f"  Discounted QALYs: {qaly_result.discounted_qalys:.2f}")
    print(f"  QALYs by age group: {qaly_result.qaly_by_age_group}")
    print()


def demo_lyg_calculation():
    """Demonstrate LYG calculation functionality."""
    print("=== LYG Calculation Demo ===")
    
    # Initialize LYG calculator
    lyg_calc = LYGCalculator()
    print("LYG calculator initialized")
    
    # Create sample survival data
    ages = [50, 55, 60, 65, 70]
    intervention_survival = [0.95, 0.90, 0.80, 0.65, 0.45]
    control_survival = [0.90, 0.80, 0.65, 0.50, 0.30]
    
    # Calculate LYG
    lyg_result = lyg_calc.calculate_lyg(intervention_survival, control_survival, ages)
    print(f"Basic LYG Results:")
    print(f"  Total LYG: {lyg_result['total_lyg']:.2f}")
    print(f"  Mean LYG per person: {lyg_result['mean_lyg_per_person']:.2f}")
    
    # Calculate discounted LYG
    discounted_lyg_result = lyg_calc.calculate_discounted_lyg(lyg_result)
    print(f"  Total Discounted LYG: {discounted_lyg_result['total_discounted_lyg']:.2f}")
    print()


def demo_utility_management():
    """Demonstrate utility value management functionality."""
    print("=== Utility Value Management Demo ===")
    
    # Initialize utility value manager
    utility_manager = UtilityValueManager()
    print("Utility value manager initialized")
    
    # Get utility values for different health states
    normal_utility = utility_manager.get_utility_value('normal')
    cancer_utility = utility_manager.get_utility_value('clinical_cancer_stage_ii')
    
    print(f"Utility values:")
    print(f"  Normal health: {normal_utility}")
    print(f"  Cancer stage II: {cancer_utility}")
    
    # Get age-adjusted utilities
    age_30_utility = utility_manager.get_age_adjusted_utility(cancer_utility, 30)
    age_70_utility = utility_manager.get_age_adjusted_utility(cancer_utility, 70)
    
    print(f"Age-adjusted cancer stage II utility:")
    print(f"  Age 30: {age_30_utility:.3f}")
    print(f"  Age 70: {age_70_utility:.3f}")
    
    # Get treatment disutilities
    colonoscopy_disutility = utility_manager.get_treatment_disutility('colonoscopy')
    chemotherapy_disutility = utility_manager.get_treatment_disutility('chemotherapy')
    
    print(f"Treatment disutilities:")
    print(f"  Colonoscopy: {colonoscopy_disutility}")
    print(f"  Chemotherapy: {chemotherapy_disutility}")
    
    # Apply treatment disutility
    adjusted_utility = utility_manager.apply_treatment_disutility(normal_utility, 'colonoscopy')
    print(f"Normal utility adjusted for colonoscopy: {adjusted_utility:.3f}")
    print()


def demo_uncertainty_analysis():
    """Demonstrate uncertainty analysis functionality."""
    print("=== Uncertainty Analysis Demo ===")
    
    # Initialize uncertainty analyzer
    uncertainty_analyzer = UncertaintyAnalyzer(n_simulations=100)
    print("Uncertainty analyzer initialized with 100 simulations")
    
    # Create sample data for confidence interval calculation
    sample_data = [10.0, 11.0, 9.5, 10.5, 12.0, 9.0, 11.5, 10.2, 9.8, 10.8]
    ci_lower, ci_upper = uncertainty_analyzer.calculate_confidence_interval(sample_data)
    
    print(f"Confidence interval for sample data:")
    print(f"  Sample mean: {np.mean(sample_data):.2f}")
    print(f"  95% CI: [{ci_lower:.2f}, {ci_upper:.2f}]")
    
    # Demonstrate bootstrap method
    from src.modules.economics.qaly_calculator import QALYResult
    
    # Create sample QALY results
    qaly_results = [
        QALYResult(
            total_qalys=10.0 + np.random.normal(0, 1),
            undiscounted_qalys=12.0,
            discounted_qalys=10.0,
            qaly_by_age_group={'50_59': 5.0, '60_69': 5.0}
        ) for _ in range(20)
    ]
    
    bootstrap_ci = uncertainty_analyzer.bootstrap_qaly_ci(qaly_results)
    mean_qaly = np.mean([r.total_qalys for r in qaly_results])
    
    print(f"Bootstrap confidence interval for QALY results:")
    print(f"  Sample mean QALY: {mean_qaly:.2f}")
    print(f"  95% Bootstrap CI: [{bootstrap_ci[0]:.2f}, {bootstrap_ci[1]:.2f}]")
    print()


def main():
    """Main function to run all demos."""
    print("Health Outcomes Calculation Modules Demo")
    print("=" * 50)
    print()
    
    demo_qaly_calculation()
    demo_lyg_calculation()
    demo_utility_management()
    demo_uncertainty_analysis()
    
    print("=== Demo Completed Successfully ===")


if __name__ == "__main__":
    main()