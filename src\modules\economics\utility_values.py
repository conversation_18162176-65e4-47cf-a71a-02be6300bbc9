"""
Utility Values Management for health outcomes assessment.
"""
from typing import Dict, List, Optional, Union
from enum import Enum
import numpy as np
import yaml
import json
import csv
from src.core.individual import Individual


class UtilityScale(Enum):
    """Standard utility scales for measuring health-related quality of life."""
    EQ5D = "eq5d"           # EuroQol-5D
    SF6D = "sf6d"           # SF-6D
    HUI3 = "hui3"           # Health Utilities Index Mark 3
    CUSTOM = "custom"       # Custom scale


class UtilityValueManager:
    """Manager for health utility values used in QALY and other health outcome calculations."""
    
    def __init__(self):
        """Initialize utility value manager with default values."""
        self.utility_values = self._load_default_utilities()
        self.age_adjustments = self._load_default_age_adjustments()
        self.treatment_disutilities = self._load_default_treatment_disutilities()
        self.scale = UtilityScale.EQ5D
    
    def _load_default_utilities(self) -> Dict:
        """
        Load default utility values for health states.
        
        Returns:
            Dictionary of utility values by health state
        """
        return {
            # Normal/baseline health states
            'normal': 1.0,
            'healthy_population': 0.95,
            
            # Disease states - colorectal cancer specific
            'low_risk_adenoma': 0.98,
            'high_risk_adenoma': 0.96,
            'preclinical_cancer': 1.0,  # May be asymptomatic
            'clinical_cancer_stage_i': 0.85,
            'clinical_cancer_stage_ii': 0.78,
            'clinical_cancer_stage_iii': 0.70,
            'clinical_cancer_stage_iv': 0.55,
            
            # Treatment-related states
            'post_surgery_recovery': 0.80,
            'undergoing_chemotherapy': 0.70,
            'undergoing_radiation': 0.75,
            'palliative_care': 0.60
        }
    
    def _load_default_age_adjustments(self) -> Dict:
        """
        Load default age-specific adjustments to utility values.
        
        Returns:
            Dictionary of age adjustment factors
        """
        return {
            '18_29': 0.95,
            '30_39': 0.93,
            '40_49': 0.91,
            '50_59': 0.89,
            '60_69': 0.86,
            '70_79': 0.82,
            '80_plus': 0.78
        }
    
    def _load_default_treatment_disutilities(self) -> Dict:
        """
        Load default disutility values for medical procedures.
        
        Returns:
            Dictionary of disutility values by procedure
        """
        return {
            # Screening disutilities
            'fit_test': -0.001,
            'colonoscopy': -0.01,
            'sigmoidoscopy': -0.005,
            
            # Treatment disutilities
            'surgery_acute': -0.15,      # Surgery acute phase (3 months)
            'chemotherapy': -0.20,       # Chemotherapy period
            'radiation_therapy': -0.10,  # Radiation therapy period
            'palliative_care': -0.25     # Palliative care
        }
    
    def load_utilities_from_yaml(self, file_path: str) -> None:
        """
        Load utility values from a YAML configuration file.
        
        Args:
            file_path: Path to YAML file with utility values
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            if 'utility_weights' in config:
                utility_data = config['utility_weights']
                
                # Set scale if provided
                if 'scale' in utility_data:
                    try:
                        self.scale = UtilityScale(utility_data['scale'].lower())
                    except ValueError:
                        self.scale = UtilityScale.CUSTOM
                
                # Load baseline utilities
                if 'baseline_utilities' in utility_data:
                    for category, values in utility_data['baseline_utilities'].items():
                        if isinstance(values, dict):
                            for key, value in values.items():
                                self.utility_values[f"{category}_{key}"] = value
                        else:
                            self.utility_values[category] = values
                
                # Load disease state utilities
                if 'disease_state_utilities' in utility_data:
                    self.utility_values.update(utility_data['disease_state_utilities'])
                
                # Load treatment-related utilities
                if 'treatment_related_utilities' in utility_data:
                    treatment_data = utility_data['treatment_related_utilities']
                    if 'screening_disutility' in treatment_data:
                        self.treatment_disutilities.update(treatment_data['screening_disutility'])
                    if 'treatment_disutility' in treatment_data:
                        self.treatment_disutilities.update(treatment_data['treatment_disutility'])
        
        except FileNotFoundError:
            raise FileNotFoundError(f"Utility configuration file not found: {file_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing YAML file: {e}")
    
    def load_utilities_from_json(self, file_path: str) -> None:
        """
        Load utility values from a JSON configuration file.
        
        Args:
            file_path: Path to JSON file with utility values
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if 'utility_weights' in config:
                utility_data = config['utility_weights']
                
                # Set scale if provided
                if 'scale' in utility_data:
                    try:
                        self.scale = UtilityScale(utility_data['scale'].lower())
                    except ValueError:
                        self.scale = UtilityScale.CUSTOM
                
                # Load baseline utilities
                if 'baseline_utilities' in utility_data:
                    self.utility_values.update(utility_data['baseline_utilities'])
                
                # Load disease state utilities
                if 'disease_state_utilities' in utility_data:
                    self.utility_values.update(utility_data['disease_state_utilities'])
                
                # Load treatment-related utilities
                if 'treatment_related_utilities' in utility_data:
                    treatment_data = utility_data['treatment_related_utilities']
                    if 'screening_disutility' in treatment_data:
                        self.treatment_disutilities.update(treatment_data['screening_disutility'])
                    if 'treatment_disutility' in treatment_data:
                        self.treatment_disutilities.update(treatment_data['treatment_disutility'])
        
        except FileNotFoundError:
            raise FileNotFoundError(f"Utility configuration file not found: {file_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Error parsing JSON file: {e}")
    
    def get_utility_value(self, health_state: str) -> float:
        """
        Get utility value for a specific health state.
        
        Args:
            health_state: Name of health state
            
        Returns:
            Utility value for the health state
        """
        return self.utility_values.get(health_state, 1.0)  # Default to 1.0 (perfect health)
    
    def get_age_adjusted_utility(self, base_utility: float, age: Union[int, float]) -> float:
        """
        Get age-adjusted utility value.
        
        Args:
            base_utility: Base utility value for health state
            age: Age of individual
            
        Returns:
            Age-adjusted utility value
        """
        age = int(age)
        if 18 <= age <= 29:
            age_group = '18_29'
        elif 30 <= age <= 39:
            age_group = '30_39'
        elif 40 <= age <= 49:
            age_group = '40_49'
        elif 50 <= age <= 59:
            age_group = '50_59'
        elif 60 <= age <= 69:
            age_group = '60_69'
        elif 70 <= age <= 79:
            age_group = '70_79'
        else:
            age_group = '80_plus'
        
        age_adjustment = self.age_adjustments.get(age_group, 1.0)
        return base_utility * age_adjustment
    
    def get_disease_state_utility(self, disease_state: str, age: Union[int, float] = None) -> float:
        """
        Get utility value for a disease state, with optional age adjustment.
        
        Args:
            disease_state: Name of disease state
            age: Age of individual (optional)
            
        Returns:
            Utility value for the disease state, age-adjusted if age provided
        """
        base_utility = self.get_utility_value(disease_state)
        if age is not None:
            return self.get_age_adjusted_utility(base_utility, age)
        return base_utility
    
    def get_treatment_disutility(self, treatment: str) -> float:
        """
        Get disutility value for a treatment or procedure.
        
        Args:
            treatment: Name of treatment or procedure
            
        Returns:
            Disutility value (negative utility impact)
        """
        return self.treatment_disutilities.get(treatment, 0.0)
    
    def apply_treatment_disutility(self, base_utility: float, treatment: str) -> float:
        """
        Apply treatment disutility to a base utility value.
        
        Args:
            base_utility: Base utility value
            treatment: Name of treatment or procedure
            
        Returns:
            Utility value adjusted for treatment disutility
        """
        disutility = self.get_treatment_disutility(treatment)
        return base_utility + disutility  # Disutilities are negative, so we add them
    
    def interpolate_utility(self, age: float, utility_values: Dict[str, float]) -> float:
        """
        Interpolate utility value for a specific age based on age brackets.
        
        Args:
            age: Age for which to interpolate utility
            utility_values: Dictionary with age brackets as keys and utilities as values
            
        Returns:
            Interpolated utility value
        """
        # Convert string keys to numeric age ranges
        age_brackets = []
        for key in utility_values.keys():
            # Extract numeric values from keys like "age_18_29"
            if 'age_' in key:
                parts = key.replace('age_', '').split('_')
                if len(parts) >= 2 and parts[-1] == 'plus':
                    # Handle "80_plus" case
                    age_brackets.append((int(parts[0]), float('inf'), utility_values[key]))
                elif len(parts) == 2:
                    age_brackets.append((int(parts[0]), int(parts[1]), utility_values[key]))
            elif '_' in key:
                # Handle direct range format like "18_29"
                parts = key.split('_')
                if len(parts) == 2:
                    try:
                        age_brackets.append((int(parts[0]), int(parts[1]), utility_values[key]))
                    except ValueError:
                        continue
        
        # Sort by age range start
        age_brackets.sort(key=lambda x: x[0])
        
        # Find appropriate bracket or interpolate
        for i, (start, end, utility) in enumerate(age_brackets):
            if start <= age <= end:
                # Exact match or within range
                return utility

        # Check if age is between brackets for interpolation
        for i in range(len(age_brackets) - 1):
            current_start, current_end, current_utility = age_brackets[i]
            next_start, next_end, next_utility = age_brackets[i + 1]

            if current_end < age < next_start:
                # Linear interpolation between brackets
                proportion = (age - current_end) / (next_start - current_end)
                return current_utility + proportion * (next_utility - current_utility)

        # If age is outside all ranges, use the closest value
        if age < age_brackets[0][0]:
            return age_brackets[0][2]
        else:
            return age_brackets[-1][2]
    
    def get_utility_for_individual(self, individual: Individual) -> float:
        """
        Get utility value for an individual based on their health state and age.
        
        Args:
            individual: Individual for which to calculate utility
            
        Returns:
            Age-adjusted utility value for the individual
        """
        # Get disease state from individual
        disease_state = getattr(individual, 'disease_state', 'normal')
        
        # Get age from individual
        age = getattr(individual, 'age', 50)
        
        # Get base utility for disease state
        base_utility = self.get_disease_state_utility(disease_state)
        
        # Apply age adjustment
        age_adjusted_utility = self.get_age_adjusted_utility(base_utility, age)
        
        # Check for recent treatments that might affect utility
        # This is a simplified implementation - in practice, this would check for
        # recent procedures or treatments in the individual's history
        if hasattr(individual, 'recent_treatments'):
            for treatment in individual.recent_treatments:
                age_adjusted_utility = self.apply_treatment_disutility(age_adjusted_utility, treatment)
        
        return age_adjusted_utility
