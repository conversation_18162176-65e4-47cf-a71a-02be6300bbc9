"""
训练数据生成器模块
用于生成深度神经网络训练所需的数据集
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass
import time
import logging
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
import joblib
import os
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class DataGenerationConfig:
    """数据生成配置"""
    n_samples: int = 10000
    batch_size: int = 100
    test_size: float = 0.2
    validation_size: float = 0.2
    random_seed: int = 42
    use_cache: bool = True
    cache_dir: str = "data/cache"
    n_workers: int = 4
    preprocessing_method: str = "robust"  # standard, robust, minmax
    add_noise: bool = False
    noise_level: float = 0.01
    augmentation_factor: float = 1.0


@dataclass
class TrainingDataset:
    """训练数据集"""
    X_train: np.ndarray
    X_val: np.ndarray
    X_test: np.ndarray
    y_train: np.ndarray
    y_val: np.ndarray
    y_test: np.ndarray
    feature_names: List[str]
    target_names: List[str]
    input_scaler: Any
    output_scaler: Any
    metadata: Dict[str, Any]


class TrainingDataGenerator:
    """训练数据生成器类"""
    
    def __init__(self, 
                 simulation_engine: Any,
                 parameter_sampler: Any,
                 config: DataGenerationConfig):
        """
        初始化训练数据生成器
        
        Args:
            simulation_engine: 微观模拟引擎
            parameter_sampler: 参数采样器
            config: 数据生成配置
        """
        self.simulation_engine = simulation_engine
        self.parameter_sampler = parameter_sampler
        self.config = config
        self.data_cache = {}
        
        # 创建缓存目录
        os.makedirs(config.cache_dir, exist_ok=True)
        
        # 初始化预处理器
        self.input_scaler = None
        self.output_scaler = None
        
        logger.info(f"训练数据生成器初始化完成，配置: {config}")
    
    def generate_training_data(self, 
                             progress_callback: Optional[Callable] = None) -> TrainingDataset:
        """
        生成完整的训练数据集
        
        Args:
            progress_callback: 进度回调函数
            
        Returns:
            TrainingDataset: 完整的训练数据集
        """
        logger.info(f"开始生成 {self.config.n_samples} 个训练样本")
        start_time = time.time()
        
        # 检查缓存
        cache_key = self._generate_cache_key()
        if self.config.use_cache and self._check_cache(cache_key):
            logger.info("从缓存加载训练数据")
            return self._load_from_cache(cache_key)
        
        # 生成原始数据
        X_raw, y_raw = self._generate_raw_data(progress_callback)
        
        # 数据预处理
        X_processed, y_processed = self._preprocess_data(X_raw, y_raw)
        
        # 数据增强
        if self.config.augmentation_factor > 1.0:
            X_processed, y_processed = self._augment_data(X_processed, y_processed)
        
        # 分割数据集
        dataset = self._split_dataset(X_processed, y_processed)
        
        # 保存到缓存
        if self.config.use_cache:
            self._save_to_cache(cache_key, dataset)
        
        generation_time = time.time() - start_time
        logger.info(f"训练数据生成完成，耗时: {generation_time:.2f}秒")
        
        return dataset
    
    def _generate_raw_data(self, 
                          progress_callback: Optional[Callable] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        生成原始训练数据
        
        Args:
            progress_callback: 进度回调函数
            
        Returns:
            Tuple[np.ndarray, np.ndarray]: 输入参数和输出目标
        """
        # 生成参数样本
        logger.info("生成参数样本...")
        sampling_result = self.parameter_sampler.generate_samples()
        parameter_samples = sampling_result.samples
        
        logger.info(f"参数样本形状: {parameter_samples.shape}")
        
        # 批量运行模拟
        simulation_outputs = []
        n_batches = (self.config.n_samples + self.config.batch_size - 1) // self.config.batch_size
        
        logger.info(f"开始批量模拟，共 {n_batches} 个批次")
        
        for batch_idx in range(n_batches):
            start_idx = batch_idx * self.config.batch_size
            end_idx = min(start_idx + self.config.batch_size, self.config.n_samples)
            batch_params = parameter_samples[start_idx:end_idx]
            
            logger.info(f"处理批次 {batch_idx + 1}/{n_batches} ({len(batch_params)} 个样本)")
            
            # 并行运行模拟
            batch_outputs = self._run_simulation_batch_parallel(batch_params)
            simulation_outputs.extend(batch_outputs)
            
            # 进度回调
            if progress_callback:
                progress = (batch_idx + 1) / n_batches
                progress_callback(progress, batch_idx + 1, n_batches)
            
            # 每10个批次报告进度
            if (batch_idx + 1) % 10 == 0:
                logger.info(f"已完成 {end_idx}/{self.config.n_samples} 个模拟")
        
        X = parameter_samples[:len(simulation_outputs)]
        y = np.array(simulation_outputs)
        
        logger.info(f"原始数据形状: X={X.shape}, y={y.shape}")
        
        return X, y
    
    def _run_simulation_batch_parallel(self, parameter_batch: np.ndarray) -> List[np.ndarray]:
        """
        并行批量运行模拟
        
        Args:
            parameter_batch: 参数批次
            
        Returns:
            List[np.ndarray]: 模拟输出列表
        """
        if self.config.n_workers == 1:
            # 单线程执行
            return self._run_simulation_batch(parameter_batch)
        
        # 多线程执行
        with ThreadPoolExecutor(max_workers=self.config.n_workers) as executor:
            futures = []
            for params in parameter_batch:
                future = executor.submit(self._run_single_simulation, params)
                futures.append(future)
            
            outputs = []
            for future in futures:
                try:
                    output = future.result(timeout=300)  # 5分钟超时
                    outputs.append(output)
                except Exception as e:
                    logger.error(f"模拟执行失败: {e}")
                    # 使用默认值或跳过
                    outputs.append(np.zeros(self._get_output_dimension()))
        
        return outputs
    
    def _run_simulation_batch(self, parameter_batch: np.ndarray) -> List[np.ndarray]:
        """
        批量运行模拟（单线程）
        
        Args:
            parameter_batch: 参数批次
            
        Returns:
            List[np.ndarray]: 模拟输出列表
        """
        outputs = []
        
        for params in parameter_batch:
            try:
                output = self._run_single_simulation(params)
                outputs.append(output)
            except Exception as e:
                logger.error(f"模拟执行失败: {e}")
                # 使用默认值
                outputs.append(np.zeros(self._get_output_dimension()))
        
        return outputs
    
    def _run_single_simulation(self, params: np.ndarray) -> np.ndarray:
        """
        运行单个模拟
        
        Args:
            params: 模拟参数
            
        Returns:
            np.ndarray: 目标指标
        """
        # 设置模拟参数
        self.simulation_engine.set_parameters(params)
        
        # 运行模拟
        result = self.simulation_engine.run_simulation()
        
        # 提取目标指标
        target_metrics = self._extract_target_metrics(result)
        
        return target_metrics
    
    def _extract_target_metrics(self, simulation_result: Any) -> np.ndarray:
        """
        从模拟结果中提取目标指标
        
        Args:
            simulation_result: 模拟结果
            
        Returns:
            np.ndarray: 目标指标数组
        """
        metrics = []
        
        try:
            # 低风险腺瘤患病率（按年龄组）
            low_risk_adenoma_prevalence = simulation_result.get_low_risk_adenoma_prevalence_by_age()
            if isinstance(low_risk_adenoma_prevalence, (list, np.ndarray)):
                metrics.extend(low_risk_adenoma_prevalence)
            else:
                metrics.append(low_risk_adenoma_prevalence)
            
            # 高风险腺瘤患病率（按年龄组）
            high_risk_adenoma_prevalence = simulation_result.get_high_risk_adenoma_prevalence_by_age()
            if isinstance(high_risk_adenoma_prevalence, (list, np.ndarray)):
                metrics.extend(high_risk_adenoma_prevalence)
            else:
                metrics.append(high_risk_adenoma_prevalence)

            # 癌症发病率（按年龄组）
            cancer_incidence = simulation_result.get_cancer_incidence_by_age()
            if isinstance(cancer_incidence, (list, np.ndarray)):
                metrics.extend(cancer_incidence)
            else:
                metrics.append(cancer_incidence)
            
            # 癌症死亡率
            cancer_mortality = simulation_result.get_cancer_mortality_rate()
            metrics.append(cancer_mortality)
            
            # 筛查检出率
            screening_detection_rate = simulation_result.get_screening_detection_rate()
            metrics.append(screening_detection_rate)
            
            # 生存率（5年）
            survival_rate_5y = simulation_result.get_survival_rate(years=5)
            metrics.append(survival_rate_5y)
            
            # 筛查成本效果比
            cost_effectiveness = simulation_result.get_cost_effectiveness_ratio()
            metrics.append(cost_effectiveness)
            
        except Exception as e:
            logger.warning(f"提取目标指标时出错: {e}")
            # 返回默认值
            metrics = [0.0] * self._get_output_dimension()
        
        return np.array(metrics, dtype=np.float32)
    
    def _get_output_dimension(self) -> int:
        """获取输出维度"""
        # 根据目标指标定义输出维度
        # 腺瘤患病率(10个年龄组) + 癌症发病率(10个年龄组) + 癌症死亡率 + 筛查检出率 + 5年生存率 + 成本效果比
        return 10 + 10 + 1 + 1 + 1 + 1  # 24维
    
    def _preprocess_data(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        数据预处理和标准化
        
        Args:
            X: 输入特征
            y: 输出目标
            
        Returns:
            Tuple[np.ndarray, np.ndarray]: 预处理后的数据
        """
        logger.info("开始数据预处理...")
        
        # 处理缺失值和异常值
        X = self._handle_missing_values(X)
        y = self._handle_missing_values(y)
        
        # 移除异常值
        X, y = self._remove_outliers(X, y)
        
        # 特征标准化
        if self.config.preprocessing_method == "standard":
            self.input_scaler = StandardScaler()
        elif self.config.preprocessing_method == "robust":
            self.input_scaler = RobustScaler()
        elif self.config.preprocessing_method == "minmax":
            self.input_scaler = MinMaxScaler()
        else:
            raise ValueError(f"不支持的预处理方法: {self.config.preprocessing_method}")
        
        X_scaled = self.input_scaler.fit_transform(X)
        
        # 目标标准化（使用RobustScaler以处理异常值）
        self.output_scaler = RobustScaler()
        y_scaled = self.output_scaler.fit_transform(y)
        
        # 添加噪声（如果启用）
        if self.config.add_noise:
            X_scaled = self._add_noise(X_scaled, self.config.noise_level)
        
        logger.info(f"预处理完成，数据形状: X={X_scaled.shape}, y={y_scaled.shape}")
        
        return X_scaled, y_scaled
    
    def _handle_missing_values(self, data: np.ndarray) -> np.ndarray:
        """处理缺失值"""
        # 检查NaN值
        nan_mask = np.isnan(data)
        if np.any(nan_mask):
            logger.warning(f"发现 {np.sum(nan_mask)} 个缺失值，使用均值填充")
            # 使用列均值填充
            col_means = np.nanmean(data, axis=0)
            data = np.where(nan_mask, col_means, data)
        
        # 检查无穷值
        inf_mask = np.isinf(data)
        if np.any(inf_mask):
            logger.warning(f"发现 {np.sum(inf_mask)} 个无穷值，使用有限值的最大/最小值替换")
            finite_data = data[np.isfinite(data)]
            if len(finite_data) > 0:
                data = np.where(np.isposinf(data), np.max(finite_data), data)
                data = np.where(np.isneginf(data), np.min(finite_data), data)
        
        return data
    
    def _remove_outliers(self, X: np.ndarray, y: np.ndarray, 
                        method: str = "iqr", threshold: float = 3.0) -> Tuple[np.ndarray, np.ndarray]:
        """
        移除异常值
        
        Args:
            X: 输入特征
            y: 输出目标
            method: 异常值检测方法 ("iqr", "zscore")
            threshold: 阈值
            
        Returns:
            Tuple[np.ndarray, np.ndarray]: 清理后的数据
        """
        if method == "iqr":
            # 使用四分位距方法
            Q1 = np.percentile(y, 25, axis=0)
            Q3 = np.percentile(y, 75, axis=0)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            # 检查每个样本是否在所有维度上都正常
            outlier_mask = np.any((y < lower_bound) | (y > upper_bound), axis=1)
            
        elif method == "zscore":
            # 使用Z分数方法
            z_scores = np.abs((y - np.mean(y, axis=0)) / np.std(y, axis=0))
            outlier_mask = np.any(z_scores > threshold, axis=1)
        
        else:
            raise ValueError(f"不支持的异常值检测方法: {method}")
        
        # 移除异常值
        normal_indices = ~outlier_mask
        X_clean = X[normal_indices]
        y_clean = y[normal_indices]
        
        n_outliers = np.sum(outlier_mask)
        if n_outliers > 0:
            logger.info(f"移除了 {n_outliers} 个异常值样本")
        
        return X_clean, y_clean
    
    def _add_noise(self, data: np.ndarray, noise_level: float) -> np.ndarray:
        """添加高斯噪声"""
        noise = np.random.normal(0, noise_level, data.shape)
        return data + noise
    
    def _augment_data(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        数据增强
        
        Args:
            X: 输入特征
            y: 输出目标
            
        Returns:
            Tuple[np.ndarray, np.ndarray]: 增强后的数据
        """
        if self.config.augmentation_factor <= 1.0:
            return X, y
        
        logger.info(f"开始数据增强，增强因子: {self.config.augmentation_factor}")
        
        n_original = len(X)
        n_augmented = int(n_original * (self.config.augmentation_factor - 1))
        
        # 随机选择样本进行增强
        indices = np.random.choice(n_original, n_augmented, replace=True)
        X_aug = X[indices].copy()
        y_aug = y[indices].copy()
        
        # 添加小幅度的随机扰动
        noise_std = 0.01
        X_aug += np.random.normal(0, noise_std, X_aug.shape)
        y_aug += np.random.normal(0, noise_std * 0.1, y_aug.shape)  # 输出噪声更小
        
        # 合并原始数据和增强数据
        X_combined = np.vstack([X, X_aug])
        y_combined = np.vstack([y, y_aug])
        
        logger.info(f"数据增强完成，从 {n_original} 增加到 {len(X_combined)} 个样本")
        
        return X_combined, y_combined
    
    def _split_dataset(self, X: np.ndarray, y: np.ndarray) -> TrainingDataset:
        """
        分割数据集
        
        Args:
            X: 输入特征
            y: 输出目标
            
        Returns:
            TrainingDataset: 分割后的数据集
        """
        logger.info("分割数据集...")
        
        # 首先分离测试集
        X_temp, X_test, y_temp, y_test = train_test_split(
            X, y, 
            test_size=self.config.test_size,
            random_state=self.config.random_seed,
            stratify=None  # 回归任务不需要分层
        )
        
        # 从剩余数据中分离训练集和验证集
        val_size_adjusted = self.config.validation_size / (1 - self.config.test_size)
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp,
            test_size=val_size_adjusted,
            random_state=self.config.random_seed
        )
        
        # 生成特征名称和目标名称
        feature_names = [f"param_{i}" for i in range(X.shape[1])]
        target_names = [
            # 腺瘤患病率（10个年龄组）
            *[f"adenoma_prev_age_{i}" for i in range(10)],
            # 癌症发病率（10个年龄组）
            *[f"cancer_inc_age_{i}" for i in range(10)],
            # 其他指标
            "cancer_mortality", "screening_detection", "survival_5y", "cost_effectiveness"
        ]
        
        # 创建元数据
        metadata = {
            "n_total_samples": len(X),
            "n_train_samples": len(X_train),
            "n_val_samples": len(X_val),
            "n_test_samples": len(X_test),
            "n_features": X.shape[1],
            "n_targets": y.shape[1],
            "preprocessing_method": self.config.preprocessing_method,
            "generation_config": self.config,
            "data_statistics": {
                "X_mean": np.mean(X, axis=0).tolist(),
                "X_std": np.std(X, axis=0).tolist(),
                "y_mean": np.mean(y, axis=0).tolist(),
                "y_std": np.std(y, axis=0).tolist()
            }
        }
        
        logger.info(f"数据集分割完成:")
        logger.info(f"  训练集: {len(X_train)} 样本")
        logger.info(f"  验证集: {len(X_val)} 样本")
        logger.info(f"  测试集: {len(X_test)} 样本")
        
        return TrainingDataset(
            X_train=X_train,
            X_val=X_val,
            X_test=X_test,
            y_train=y_train,
            y_val=y_val,
            y_test=y_test,
            feature_names=feature_names,
            target_names=target_names,
            input_scaler=self.input_scaler,
            output_scaler=self.output_scaler,
            metadata=metadata
        )
    
    def _generate_cache_key(self) -> str:
        """生成缓存键"""
        import hashlib
        import json
        
        # 创建配置字符串
        config_dict = {
            "n_samples": self.config.n_samples,
            "batch_size": self.config.batch_size,
            "random_seed": self.config.random_seed,
            "preprocessing_method": self.config.preprocessing_method,
            "add_noise": self.config.add_noise,
            "noise_level": self.config.noise_level,
            "augmentation_factor": self.config.augmentation_factor
        }
        
        config_str = json.dumps(config_dict, sort_keys=True)
        cache_key = hashlib.md5(config_str.encode()).hexdigest()
        
        return f"training_data_{cache_key}"
    
    def _check_cache(self, cache_key: str) -> bool:
        """检查缓存是否存在"""
        cache_path = Path(self.config.cache_dir) / f"{cache_key}.pkl"
        return cache_path.exists()
    
    def _save_to_cache(self, cache_key: str, dataset: TrainingDataset):
        """保存到缓存"""
        cache_path = Path(self.config.cache_dir) / f"{cache_key}.pkl"
        
        try:
            joblib.dump(dataset, cache_path)
            logger.info(f"训练数据已保存到缓存: {cache_path}")
        except Exception as e:
            logger.error(f"保存缓存失败: {e}")
    
    def _load_from_cache(self, cache_key: str) -> TrainingDataset:
        """从缓存加载"""
        cache_path = Path(self.config.cache_dir) / f"{cache_key}.pkl"
        
        try:
            dataset = joblib.load(cache_path)
            logger.info(f"从缓存加载训练数据: {cache_path}")
            return dataset
        except Exception as e:
            logger.error(f"加载缓存失败: {e}")
            raise
    
    def get_data_summary(self, dataset: TrainingDataset) -> Dict[str, Any]:
        """
        获取数据集摘要信息
        
        Args:
            dataset: 训练数据集
            
        Returns:
            Dict[str, Any]: 摘要信息
        """
        summary = {
            "dataset_info": {
                "total_samples": dataset.metadata["n_total_samples"],
                "train_samples": dataset.metadata["n_train_samples"],
                "val_samples": dataset.metadata["n_val_samples"],
                "test_samples": dataset.metadata["n_test_samples"],
                "n_features": dataset.metadata["n_features"],
                "n_targets": dataset.metadata["n_targets"]
            },
            "feature_statistics": {
                "train": {
                    "mean": np.mean(dataset.X_train, axis=0),
                    "std": np.std(dataset.X_train, axis=0),
                    "min": np.min(dataset.X_train, axis=0),
                    "max": np.max(dataset.X_train, axis=0)
                }
            },
            "target_statistics": {
                "train": {
                    "mean": np.mean(dataset.y_train, axis=0),
                    "std": np.std(dataset.y_train, axis=0),
                    "min": np.min(dataset.y_train, axis=0),
                    "max": np.max(dataset.y_train, axis=0)
                }
            },
            "data_quality": {
                "missing_values": {
                    "X_train": np.sum(np.isnan(dataset.X_train)),
                    "y_train": np.sum(np.isnan(dataset.y_train))
                },
                "infinite_values": {
                    "X_train": np.sum(np.isinf(dataset.X_train)),
                    "y_train": np.sum(np.isinf(dataset.y_train))
                }
            }
        }
        
        return summary


class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_dataset(dataset: TrainingDataset) -> Dict[str, Any]:
        """
        验证数据集质量
        
        Args:
            dataset: 训练数据集
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        validation_results = {
            "is_valid": True,
            "warnings": [],
            "errors": [],
            "statistics": {}
        }
        
        # 检查数据形状一致性
        if dataset.X_train.shape[0] != dataset.y_train.shape[0]:
            validation_results["errors"].append("训练集X和y的样本数不一致")
            validation_results["is_valid"] = False
        
        if dataset.X_val.shape[0] != dataset.y_val.shape[0]:
            validation_results["errors"].append("验证集X和y的样本数不一致")
            validation_results["is_valid"] = False
        
        if dataset.X_test.shape[0] != dataset.y_test.shape[0]:
            validation_results["errors"].append("测试集X和y的样本数不一致")
            validation_results["is_valid"] = False
        
        # 检查特征维度一致性
        if not (dataset.X_train.shape[1] == dataset.X_val.shape[1] == dataset.X_test.shape[1]):
            validation_results["errors"].append("训练集、验证集、测试集的特征维度不一致")
            validation_results["is_valid"] = False
        
        # 检查目标维度一致性
        if not (dataset.y_train.shape[1] == dataset.y_val.shape[1] == dataset.y_test.shape[1]):
            validation_results["errors"].append("训练集、验证集、测试集的目标维度不一致")
            validation_results["is_valid"] = False
        
        # 检查缺失值
        for name, data in [("X_train", dataset.X_train), ("y_train", dataset.y_train),
                          ("X_val", dataset.X_val), ("y_val", dataset.y_val),
                          ("X_test", dataset.X_test), ("y_test", dataset.y_test)]:
            nan_count = np.sum(np.isnan(data))
            if nan_count > 0:
                validation_results["warnings"].append(f"{name} 包含 {nan_count} 个缺失值")
        
        # 检查无穷值
        for name, data in [("X_train", dataset.X_train), ("y_train", dataset.y_train),
                          ("X_val", dataset.X_val), ("y_val", dataset.y_val),
                          ("X_test", dataset.X_test), ("y_test", dataset.y_test)]:
            inf_count = np.sum(np.isinf(data))
            if inf_count > 0:
                validation_results["warnings"].append(f"{name} 包含 {inf_count} 个无穷值")
        
        # 检查数据分布
        train_std = np.std(dataset.X_train, axis=0)
        if np.any(train_std < 1e-6):
            validation_results["warnings"].append("某些特征的标准差过小，可能存在常数特征")
        
        # 计算统计信息
        validation_results["statistics"] = {
            "train_samples": len(dataset.X_train),
            "val_samples": len(dataset.X_val),
            "test_samples": len(dataset.X_test),
            "n_features": dataset.X_train.shape[1],
            "n_targets": dataset.y_train.shape[1],
            "feature_std_min": np.min(train_std),
            "feature_std_max": np.max(train_std),
            "target_range": {
                "min": np.min(dataset.y_train, axis=0).tolist(),
                "max": np.max(dataset.y_train, axis=0).tolist()
            }
        }
        
        return validation_results