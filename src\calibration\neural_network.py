"""
深度神经网络架构模块
实现校准用的深度神经网络架构设计和配置
"""

import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers, Model
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
import logging
import json

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class NetworkConfig:
    """网络配置数据类"""
    input_dim: int
    output_dim: int
    architecture: str = 'feedforward'  # feedforward, residual, ensemble
    layers: List[int] = field(default_factory=lambda: [256, 128, 64])
    activation: str = 'relu'
    dropout_rate: float = 0.2
    learning_rate: float = 0.001
    batch_normalization: bool = True
    l2_regularization: float = 0.001
    target_weights: Optional[List[float]] = None


class CalibrationDNN:
    """校准深度神经网络类"""

    def __init__(self, config: NetworkConfig):
        """
        初始化深度神经网络

        Args:
            config: 网络配置
        """
        self.config = config
        self.model = None
        self.history = None
        self.logger = logging.getLogger(__name__)

        # 验证配置
        self._validate_config()

        self.logger.info(f"初始化 {config.architecture} 网络架构")

    def _validate_config(self):
        """验证网络配置"""
        if self.config.input_dim <= 0:
            raise ValueError("输入维度必须大于0")
        if self.config.output_dim <= 0:
            raise ValueError("输出维度必须大于0")
        if not self.config.layers:
            raise ValueError("网络层配置不能为空")
        if self.config.dropout_rate < 0 or self.config.dropout_rate >= 1:
            raise ValueError("Dropout率必须在[0, 1)范围内")

    def build_model(self) -> keras.Model:
        """
        构建深度神经网络模型

        Returns:
            keras.Model: 构建的模型
        """
        if self.config.architecture == 'feedforward':
            model = self._build_feedforward_network()
        elif self.config.architecture == 'residual':
            model = self._build_residual_network()
        elif self.config.architecture == 'ensemble':
            model = self._build_ensemble_network()
        else:
            raise ValueError(f"不支持的网络架构: {self.config.architecture}")

        self.model = model
        self.logger.info(f"成功构建 {self.config.architecture} 网络架构")
        return model

    def _build_feedforward_network(self) -> keras.Model:
        """构建前馈神经网络"""
        inputs = keras.Input(shape=(self.config.input_dim,), name='input')
        x = inputs

        # 隐藏层
        for i, units in enumerate(self.config.layers):
            x = layers.Dense(
                units,
                activation=self.config.activation,
                kernel_regularizer=keras.regularizers.l2(
                    self.config.l2_regularization
                ),
                name=f'dense_{i + 1}'
            )(x)

            if self.config.batch_normalization:
                x = layers.BatchNormalization(name=f'bn_{i + 1}')(x)

            if self.config.dropout_rate > 0:
                x = layers.Dropout(
                    self.config.dropout_rate,
                    name=f'dropout_{i + 1}'
                )(x)

        # 输出层
        outputs = layers.Dense(
            self.config.output_dim,
            activation='linear',
            name='output'
        )(x)

        model = keras.Model(inputs=inputs, outputs=outputs, name='calibration_dnn')

        # 编译模型
        self._compile_model(model)

        return model

    def _build_residual_network(self) -> keras.Model:
        """构建残差神经网络"""
        inputs = keras.Input(shape=(self.config.input_dim,), name='input')
        x = inputs

        # 初始层
        x = layers.Dense(
            self.config.layers[0],
            activation=self.config.activation,
            name='initial_dense'
        )(x)

        if self.config.batch_normalization:
            x = layers.BatchNormalization(name='initial_bn')(x)

        # 残差块
        for i, units in enumerate(self.config.layers[1:], 1):
            residual = x

            # 第一个Dense层
            x = layers.Dense(
                units,
                activation=self.config.activation,
                kernel_regularizer=keras.regularizers.l2(
                    self.config.l2_regularization
                ),
                name=f'res_dense1_{i}'
            )(x)

            if self.config.batch_normalization:
                x = layers.BatchNormalization(name=f'res_bn1_{i}')(x)

            # 第二个Dense层
            x = layers.Dense(
                units,
                activation=None,
                kernel_regularizer=keras.regularizers.l2(
                    self.config.l2_regularization
                ),
                name=f'res_dense2_{i}'
            )(x)

            if self.config.batch_normalization:
                x = layers.BatchNormalization(name=f'res_bn2_{i}')(x)

            # 残差连接（如果维度匹配）
            if residual.shape[-1] == units:
                x = layers.Add(name=f'res_add_{i}')([x, residual])
            else:
                # 维度不匹配时使用投影
                residual_proj = layers.Dense(
                    units,
                    activation=None,
                    name=f'res_proj_{i}'
                )(residual)
                x = layers.Add(name=f'res_add_{i}')([x, residual_proj])

            x = layers.Activation(
                self.config.activation,
                name=f'res_activation_{i}'
            )(x)

            if self.config.dropout_rate > 0:
                x = layers.Dropout(
                    self.config.dropout_rate,
                    name=f'res_dropout_{i}'
                )(x)

        # 输出层
        outputs = layers.Dense(
            self.config.output_dim,
            activation='linear',
            name='output'
        )(x)

        model = keras.Model(inputs=inputs, outputs=outputs, name='residual_dnn')

        # 编译模型
        self._compile_model(model)

        return model

    def _build_ensemble_network(self) -> keras.Model:
        """构建集成神经网络"""
        inputs = keras.Input(shape=(self.config.input_dim,), name='input')

        # 创建多个子网络
        ensemble_outputs = []
        n_models = 3  # 集成3个模型

        for i in range(n_models):
            x = inputs

            # 每个子网络使用稍微不同的架构
            sub_layers = [
                int(units * (0.8 + 0.4 * i / (n_models - 1)))
                for units in self.config.layers
            ]

            for j, units in enumerate(sub_layers):
                x = layers.Dense(
                    units,
                    activation=self.config.activation,
                    kernel_regularizer=keras.regularizers.l2(
                        self.config.l2_regularization
                    ),
                    name=f'ensemble_{i}_dense_{j}'
                )(x)

                if self.config.batch_normalization:
                    x = layers.BatchNormalization(
                        name=f'ensemble_{i}_bn_{j}'
                    )(x)

                if self.config.dropout_rate > 0:
                    x = layers.Dropout(
                        self.config.dropout_rate,
                        name=f'ensemble_{i}_dropout_{j}'
                    )(x)

            # 子网络输出
            sub_output = layers.Dense(
                self.config.output_dim,
                activation='linear',
                name=f'ensemble_{i}_output'
            )(x)

            ensemble_outputs.append(sub_output)

        # 集成输出（平均）
        if len(ensemble_outputs) > 1:
            outputs = layers.Average(name='ensemble_average')(ensemble_outputs)
        else:
            outputs = ensemble_outputs[0]

        model = keras.Model(inputs=inputs, outputs=outputs, name='ensemble_dnn')

        # 编译模型
        self._compile_model(model)

        return model

    def _compile_model(self, model: keras.Model):
        """编译模型"""
        # 选择损失函数
        if self.config.target_weights is not None:
            # 加权MSE损失
            def weighted_mse(y_true, y_pred):
                weights = tf.constant(self.config.target_weights, dtype=tf.float32)
                squared_diff = tf.square(y_true - y_pred)
                weighted_squared_diff = squared_diff * weights
                return tf.reduce_mean(weighted_squared_diff)

            loss = weighted_mse
        else:
            loss = 'mse'

        # 编译模型
        model.compile(
            optimizer=keras.optimizers.Adam(
                learning_rate=self.config.learning_rate
            ),
            loss=loss,
            metrics=['mae', 'mse']
        )

    def get_model_summary(self) -> str:
        """获取模型摘要"""
        if self.model is None:
            return "模型尚未构建"

        import io
        import sys

        # 捕获模型摘要输出
        old_stdout = sys.stdout
        sys.stdout = buffer = io.StringIO()

        self.model.summary()

        sys.stdout = old_stdout
        summary = buffer.getvalue()

        return summary

    def visualize_architecture(self, save_path: Optional[str] = None) -> str:
        """可视化网络架构"""
        if self.model is None:
            raise ValueError("模型尚未构建，请先调用build_model()")

        try:
            if save_path is None:
                save_path = f"model_architecture_{self.config.architecture}.png"

            keras.utils.plot_model(
                self.model,
                to_file=save_path,
                show_shapes=True,
                show_layer_names=True,
                rankdir='TB',
                expand_nested=True,
                dpi=96
            )

            self.logger.info(f"网络架构图已保存到: {save_path}")
            return save_path

        except Exception as e:
            self.logger.warning(f"无法生成架构图: {e}")
            return ""

    def optimize_for_memory(self, target_memory_gb: float = 4.0) -> NetworkConfig:
        """
        为指定内存限制优化网络配置

        Args:
            target_memory_gb: 目标内存使用量（GB）

        Returns:
            NetworkConfig: 优化后的配置
        """
        # 估算当前配置的内存使用
        current_memory = self._estimate_memory_usage()

        if current_memory <= target_memory_gb:
            return self.config

        # 创建优化后的配置
        optimized_config = NetworkConfig(
            input_dim=self.config.input_dim,
            output_dim=self.config.output_dim,
            architecture=self.config.architecture,
            layers=self.config.layers.copy(),
            activation=self.config.activation,
            dropout_rate=self.config.dropout_rate,
            learning_rate=self.config.learning_rate,
            batch_normalization=self.config.batch_normalization,
            l2_regularization=self.config.l2_regularization,
            target_weights=self.config.target_weights
        )

        # 逐步减少网络复杂度
        reduction_factor = target_memory_gb / current_memory

        # 减少层数和神经元数量
        optimized_config.layers = [
            max(32, int(units * reduction_factor))
            for units in optimized_config.layers
        ]

        # 如果仍然超出内存限制，进一步优化
        if len(optimized_config.layers) > 3:
            optimized_config.layers = optimized_config.layers[:3]

        self.logger.info(
            f"内存优化完成: {current_memory:.2f}GB → "
            f"{self._estimate_memory_usage(optimized_config):.2f}GB"
        )

        return optimized_config

    def _estimate_memory_usage(self, config: NetworkConfig = None) -> float:
        """估算内存使用量（GB）"""
        if config is None:
            config = self.config

        # 简化的内存估算
        total_params = config.input_dim

        for units in config.layers:
            total_params += total_params * units + units
            total_params = units

        total_params += total_params * config.output_dim + config.output_dim

        # 估算内存使用（参数 + 梯度 + 激活值）
        memory_gb = total_params * 4 * 3 / (1024**3)  # 4字节 / 参数，3倍开销

        return memory_gb

    @staticmethod
    def create_calibration_config(
        parameter_count: int,
        target_count: int,
        architecture: str = 'feedforward',
        memory_limit_gb: float = None
    ) -> NetworkConfig:
        """
        为校准任务创建推荐配置

        Args:
            parameter_count: 参数数量
            target_count: 目标数量
            architecture: 网络架构类型
            memory_limit_gb: 内存限制（GB）

        Returns:
            NetworkConfig: 校准专用配置
        """
        # 根据参数和目标数量调整架构
        if parameter_count <= 10:
            layers = [256, 128, 64]
        elif parameter_count <= 50:
            layers = [512, 256, 128, 64]
        else:
            layers = [1024, 512, 256, 128, 64]

        config = NetworkConfig(
            input_dim=parameter_count,
            output_dim=target_count,
            architecture=architecture,
            layers=layers,
            activation='relu',
            dropout_rate=0.2,
            learning_rate=0.001,
            batch_normalization=True,
            l2_regularization=0.001
        )

        # 如果指定了内存限制，进行优化
        if memory_limit_gb is not None:
            temp_dnn = CalibrationDNN(config)
            config = temp_dnn.optimize_for_memory(memory_limit_gb)

        return config

    @staticmethod
    def suggest_optimal_batch_size(
        model_config: NetworkConfig,
        dataset_size: int,
        available_memory_gb: float = 8.0
    ) -> Dict[str, Any]:
        """
        建议最优批大小

        Args:
            model_config: 模型配置
            dataset_size: 数据集大小
            available_memory_gb: 可用内存（GB）

        Returns:
            Dict: 批大小建议
        """
        # 估算单个样本的内存使用
        sample_memory_mb = (
            model_config.input_dim + model_config.output_dim
        ) * 4 / (1024**2)  # 4字节 / 浮点数

        # 考虑模型参数和中间激活值的内存开销
        model_memory_gb = sum(model_config.layers) * 4 * 2 / (1024**3)

        # 计算可用于批处理的内存
        available_for_batch_gb = available_memory_gb - model_memory_gb - 1.0  # 保留1GB缓冲

        if available_for_batch_gb <= 0:
            return {
                'suggested_batch_size': 8,
                'reason': 'insufficient_memory',
                'warning': '可用内存不足，建议使用最小批大小'
            }

        # 计算理论最大批大小
        max_batch_size = int(available_for_batch_gb * 1024 / sample_memory_mb)

        # 应用实际约束
        suggested_batch_size = min(
            max_batch_size,
            dataset_size // 10,  # 不超过数据集的1 / 10
            512  # 不超过512
        )
        suggested_batch_size = max(8, suggested_batch_size)  # 不小于8

        # 调整为2的幂次（GPU友好）
        power_of_2 = 2 ** int(np.log2(suggested_batch_size))
        if power_of_2 * 1.5 <= suggested_batch_size:
            power_of_2 *= 2

        return {
            'suggested_batch_size': power_of_2,
            'max_theoretical_batch_size': max_batch_size,
            'memory_per_sample_mb': sample_memory_mb,
            'estimated_model_memory_gb': model_memory_gb,
            'reason': 'memory_optimized',
            'alternatives': {
                'conservative': max(8, power_of_2 // 2),
                'aggressive': min(512, power_of_2 * 2)
            }
        }


if __name__ == "__main__":
    # 示例用法
    print("=== 深度神经网络校准系统示例 ===")

    # 创建基础配置
    config = CalibrationDNN.create_calibration_config(
        parameter_count=20,
        target_count=24,
        memory_limit_gb=4.0  # 限制内存使用
    )

    dnn = CalibrationDNN(config)
    model = dnn.build_model()

    print("\n1. 模型摘要:")
    print(dnn.get_model_summary())

    # 批大小建议
    print("\n2. 批大小建议:")
    batch_suggestion = CalibrationDNN.suggest_optimal_batch_size(
        model_config=config,
        dataset_size=10000,
        available_memory_gb=8.0
    )
    print(f"建议批大小: {batch_suggestion['suggested_batch_size']}")
    print(f"保守选择: {batch_suggestion['alternatives']['conservative']}")
    print(f"激进选择: {batch_suggestion['alternatives']['aggressive']}")

    # 内存优化示例
    print("\n3. 内存优化:")
    optimized_config = dnn.optimize_for_memory(target_memory_gb=2.0)
    print(f"原始层配置: {config.layers}")
    print(f"优化后配置: {optimized_config.layers}")

    # 可视化架构
    print("\n4. 架构可视化:")
    arch_path = dnn.visualize_architecture("example_architecture.png")
    if arch_path:
        print(f"架构图已保存: {arch_path}")

    print("\n=== 深度神经网络示例完成！ ===")
